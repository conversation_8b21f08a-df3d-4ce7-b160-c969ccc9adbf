#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة التقويم المنبثق من حقل التاريخ
Test removing calendar popup from date field
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class RemoveCalendarPopupTestWindow(QMainWindow):
    """نافذة اختبار إزالة التقويم المنبثق"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إزالة التقويم المنبثق من حقل التاريخ")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إزالة التقويم المنبثق من حقل التاريخ")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 إزالة التقويم المنبثق من حقل التاريخ:
        
        ❌ إزالة التقويم المنبثق:
        • تم تغيير setCalendarPopup(True) إلى setCalendarPopup(False)
        • إزالة النافذة المنبثقة للتقويم عند النقر على حقل التاريخ
        • تبسيط واجهة المستخدم بإزالة عنصر غير ضروري
        
        ✅ الاحتفاظ بالتاريخ التلقائي:
        • الحفاظ على setDate(QDate.currentDate()) لملء الحقل تلقائياً بتاريخ اليوم الحالي
        • التاريخ يُعبأ تلقائياً عند إنشاء خطة معالجة جديدة
        • المستخدم لا يحتاج لإدخال التاريخ يدوياً في معظم الحالات
        
        ✅ الاحتفاظ بالتحسينات السابقة:
        • إزالة أسهم التحكم: setButtonSymbols(QDateEdit.NoButtons)
        • العرض الثابت: setFixedWidth(120)
        • المحاذاة المركزية للعنوان
        • التنسيق والمظهر الحالي
        
        🔧 الاعتبارات التقنية المحققة:
        • الحفاظ على جميع الخصائص الأخرى لحقل التاريخ
        • الحقل لا يزال يقبل الإدخال اليدوي للتاريخ إذا لزم الأمر
        • عدم التأثير على أي حقول أخرى في خطة المعالجة
        • الحفاظ على جميع الوظائف الأساسية
        
        🎨 الفوائد المحققة:
        • تبسيط حقل التاريخ بإزالة التقويم المنبثق غير الضروري
        • تحسين تجربة المستخدم بواجهة أبسط وأقل تعقيداً
        • التركيز على التاريخ التلقائي الذي يكفي في معظم الحالات
        • مظهر أنظف وأكثر بساطة للواجهة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع إزالة التقويم المنبثق
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (بدون تقويم منبثق)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار إزالة التقويم المنبثق:
        
        🔍 ما يجب ملاحظته:
        • حقل التاريخ معبأ تلقائياً بتاريخ اليوم الحالي
        • عدم ظهور تقويم منبثق عند النقر على حقل التاريخ
        • عدم وجود أسهم تحكم في حقل التاريخ
        • الحقل لا يزال يقبل الإدخال اليدوي للتاريخ
        • مظهر أبسط وأنظف لحقل التاريخ
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على حقل التاريخ وتحقق من عدم ظهور تقويم منبثق
        • جرب تحرير التاريخ يدوياً (إذا لزم الأمر)
        • انقر على زر "إضافة خطة معالجة سنية" وتحقق من التاريخ التلقائي
        • تحقق من أن حقل التاريخ يعمل بشكل طبيعي للإدخال
        • لاحظ المظهر الأبسط والأنظف للواجهة
        
        ✅ النتائج المتوقعة:
        • عدم ظهور تقويم منبثق عند النقر على حقل التاريخ
        • التاريخ الحالي معبأ تلقائياً في الحقل
        • عمل الإدخال اليدوي للتاريخ بشكل طبيعي
        • مظهر أبسط وأنظف للواجهة
        • تجربة مستخدم محسنة بواجهة أقل تعقيداً
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إزالة التقويم المنبثق من حقل التاريخ بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = RemoveCalendarPopupTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
