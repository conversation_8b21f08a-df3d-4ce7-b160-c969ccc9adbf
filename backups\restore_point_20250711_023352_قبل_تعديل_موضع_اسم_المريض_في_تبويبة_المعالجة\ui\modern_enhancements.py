"""
تحسينات حديثة لواجهة المستخدم
تطبيق أنماط عصرية مستوحاة من تطبيقات الويب الحديثة
"""

from PyQt5.QtWidgets import (QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, 
                             QHBoxLayout, QTableWidget, QLineEdit, QTextEdit, 
                             QComboBox, QGroupBox, QTabWidget)
from PyQt5.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt5.QtGui import QFont, QFontMetrics
from ui.style_helper import StyleHelper

class ModernEnhancements:
    """مجموعة من التحسينات الحديثة للواجهة"""
    
    @staticmethod
    def enhance_window(window):
        """تحسين النافذة الرئيسية"""
        # إضافة تأثيرات الظل والحدود
        window.setStyleSheet(window.styleSheet() + """
            QMainWindow {
                border-radius: 12px;
            }
        """)
    
    @staticmethod
    def enhance_buttons_in_widget(widget):
        """تحسين جميع الأزرار في widget معين"""
        buttons = widget.findChildren(QPushButton)
        for button in buttons:
            ModernEnhancements.add_button_animation(button)
            
            # تطبيق الأنماط حسب النص
            text = button.text().lower()
            if any(word in text for word in ['إضافة', 'جديد', 'حفظ', 'إنشاء']):
                StyleHelper.apply_primary_button(button)
            elif any(word in text for word in ['حذف', 'إلغاء', 'مسح']):
                StyleHelper.apply_error_button(button)
            elif any(word in text for word in ['تعديل', 'تحديث']):
                StyleHelper.apply_warning_button(button)
            elif any(word in text for word in ['تصدير', 'طباعة', 'بحث']):
                StyleHelper.apply_flat_button(button)
    
    @staticmethod
    def add_button_animation(button):
        """إضافة تأثيرات الحركة للأزرار"""
        # تجنب تعديل الأحداث لتجنب الأخطاء
        pass
    
    @staticmethod
    def animate_button_scale(button, scale_factor):
        """تحريك تكبير/تصغير الزر"""
        # تم تعطيلها مؤقتاً لتجنب الأخطاء
        pass
    
    @staticmethod
    def enhance_table(table):
        """تحسين مظهر الجدول"""
        # تطبيق الأنماط
        StyleHelper.apply_table_style(table)
        
        # تحسين العرض
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setDefaultAlignment(Qt.AlignCenter)
        
        # تحسين الصفوف
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setShowGrid(True)
        
        # تحسين الخط
        font = QFont()
        font.setPointSize(10)
        table.setFont(font)
    
    @staticmethod
    def enhance_form_inputs(widget):
        """تحسين حقول الإدخال في widget"""
        # تحسين QLineEdit
        line_edits = widget.findChildren(QLineEdit)
        for edit in line_edits:
            StyleHelper.apply_form_control_style(edit)
            ModernEnhancements.add_input_focus_effect(edit)
        
        # تحسين QTextEdit
        text_edits = widget.findChildren(QTextEdit)
        for edit in text_edits:
            StyleHelper.apply_form_control_style(edit)
        
        # تحسين QComboBox
        combo_boxes = widget.findChildren(QComboBox)
        for combo in combo_boxes:
            StyleHelper.apply_form_control_style(combo)
    
    @staticmethod
    def add_input_focus_effect(input_widget):
        """إضافة تأثير التركيز لحقول الإدخال"""
        original_focus_in = input_widget.focusInEvent
        original_focus_out = input_widget.focusOutEvent
        
        def enhanced_focus_in(event):
            # تأثير عند التركيز
            input_widget.setStyleSheet(input_widget.styleSheet() + """
                border: 2px solid #2196f3;
                background-color: #f8fcff;
            """)
            original_focus_in(event)
        
        def enhanced_focus_out(event):
            # إزالة التأثير عند فقدان التركيز
            style = input_widget.styleSheet()
            style = style.replace("border: 2px solid #2196f3;", "")
            style = style.replace("background-color: #f8fcff;", "")
            input_widget.setStyleSheet(style)
            original_focus_out(event)
        
        input_widget.focusInEvent = enhanced_focus_in
        input_widget.focusOutEvent = enhanced_focus_out
    
    @staticmethod
    def create_modern_card(title, content_widget):
        """إنشاء بطاقة حديثة"""
        card = QFrame()
        StyleHelper.apply_card_style(card)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # العنوان
        if title:
            title_label = QLabel(title)
            StyleHelper.apply_title_style(title_label)
            layout.addWidget(title_label)
        
        # المحتوى
        layout.addWidget(content_widget)
        
        return card
    
    @staticmethod
    def enhance_tab_widget(tab_widget):
        """تحسين مظهر التبويبات"""
        # تحسين مظهر التبويبات
        tab_widget.setDocumentMode(True)
        tab_widget.setTabsClosable(False)
        tab_widget.setMovable(False)
        
        # تحسين النمط
        tab_widget.setStyleSheet(tab_widget.styleSheet() + """
            QTabWidget::tab-bar {
                alignment: center;
            }
        """)
    
    @staticmethod
    def add_loading_animation(widget):
        """إضافة تأثير التحميل"""
        # TODO: إضافة تأثير دوران أو نبضة للتحميل
        pass
    
    @staticmethod
    def apply_modern_theme_to_widget(widget):
        """تطبيق التيمة الحديثة على widget كامل"""
        # تحسين الأزرار
        ModernEnhancements.enhance_buttons_in_widget(widget)
        
        # تحسين حقول الإدخال
        ModernEnhancements.enhance_form_inputs(widget)
        
        # تحسين الجداول
        tables = widget.findChildren(QTableWidget)
        for table in tables:
            ModernEnhancements.enhance_table(table)
        
        # تحسين GroupBox
        group_boxes = widget.findChildren(QGroupBox)
        for box in group_boxes:
            box.setStyleSheet(box.styleSheet() + """
                QGroupBox {
                    font-weight: 600;
                    padding-top: 15px;
                    margin-top: 10px;
                }
                QGroupBox::title {
                    color: #2196f3;
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                }
            """)
        
        # تحسين التبويبات
        tab_widgets = widget.findChildren(QTabWidget)
        for tab in tab_widgets:
            ModernEnhancements.enhance_tab_widget(tab)
    
    @staticmethod
    def add_hover_effects(widget):
        """إضافة تأثيرات الـ hover لجميع العناصر التفاعلية"""
        # الأزرار
        buttons = widget.findChildren(QPushButton)
        for button in buttons:
            ModernEnhancements.add_button_animation(button)
        
        # الجداول
        tables = widget.findChildren(QTableWidget)
        for table in tables:
            # تأثير hover للصفوف
            table.setMouseTracking(True)
    
    @staticmethod
    def create_gradient_background(widget, color1="#ffffff", color2="#f8f9fa"):
        """إضافة خلفية متدرجة"""
        widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color1},
                    stop: 1 {color2}
                );
            }}
        """)