#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار الأزرار المحدثة في نافذة إدارة الأسعار
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class TestUpdatedButtonsWindow(QMainWindow):
    """نافذة اختبار الأزرار المحدثة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار الأزرار المحدثة - نافذة إدارة الأسعار")
        self.setGeometry(200, 200, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار الأزرار المحدثة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف التحديث
        description = QLabel("""
التحديثات المطبقة على أزرار نافذة إدارة الأسعار:

✅ زر الحفظ: "حفظ جميع التغييرات" (أخضر)
✅ زر الاستعادة: "استعادة افتراضي" (برتقالي)  
✅ زر الإلغاء: "إلغاء" (أحمر)

تم إزالة جميع الرموز التعبيرية (💾 🔄 ❌)
والاحتفاظ بنفس الألوان والوظائف
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # زر فتح النافذة
        open_btn = QPushButton("🔍 فتح نافذة إدارة الأسعار")
        open_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #004085, #002752);
            }
        """)
        open_btn.clicked.connect(self.open_pricing_dialog)
        layout.addWidget(open_btn)
        
        # منطقة الحالة
        self.status_label = QLabel("جاهز لاختبار الأزرار المحدثة...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 5px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.status_label)
        
    def open_pricing_dialog(self):
        """فتح نافذة إدارة الأسعار"""
        try:
            print("=" * 50)
            print("🚀 فتح نافذة إدارة الأسعار مع الأزرار المحدثة...")
            print("=" * 50)
            
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            print("✅ تم إنشاء النافذة بنجاح")
            print("🔍 تحقق من الأزرار في الجزء السفلي:")
            print("   - زر أخضر: 'حفظ جميع التغييرات'")
            print("   - زر برتقالي: 'استعادة افتراضي'")
            print("   - زر أحمر: 'إلغاء'")
            print("=" * 50)
            
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                self.status_label.setText("✅ تم حفظ التغييرات")
                print("✅ تم حفظ التغييرات")
            else:
                self.status_label.setText("ℹ️ تم إلغاء العملية")
                print("ℹ️ تم إلغاء العملية")
                
        except Exception as e:
            self.status_label.setText(f"❌ خطأ: {e}")
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestUpdatedButtonsWindow()
    window.show()
    
    print("تم تشغيل اختبار الأزرار المحدثة")
    print("اضغط على الزر لفتح نافذة إدارة الأسعار")
    print("وتحقق من الأزرار الجديدة بدون رموز تعبيرية")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
