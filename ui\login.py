import sys
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QMessageBox, QFrame, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QPixmap, QFont

class LoginWindow(QWidget):
    # إشارة تسجيل الدخول الناجح
    login_success = pyqtSignal(str, str)  # اسم المستخدم، الدور
    
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # إعداد النافذة
        self.setWindowTitle("إدارة العيادة السنية - تسجيل الدخول")
        self.setMinimumSize(400, 300)
        self.setWindowFlags(Qt.WindowCloseButtonHint | Qt.MSWindowsFixedSizeDialogHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # عنوان التطبيق
        title_label = QLabel("إدارة العيادة السنية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # إضافة مساحة
        main_layout.addSpacing(20)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameShape(QFrame.StyledPanel)
        login_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)
        
        # عنوان تسجيل الدخول
        login_title = QLabel("تسجيل الدخول")
        login_title.setAlignment(Qt.AlignCenter)
        login_font = QFont()
        login_font.setPointSize(14)
        login_title.setFont(login_font)
        login_layout.addWidget(login_title)
        
        # حقل اسم المستخدم
        username_layout = QHBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setMinimumWidth(100)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        login_layout.addLayout(username_layout)
        
        # حقل كلمة المرور
        password_layout = QHBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setMinimumWidth(100)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        login_layout.addLayout(password_layout)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setMinimumHeight(40)
        login_layout.addWidget(self.login_button)
        
        # إضافة إطار تسجيل الدخول إلى التخطيط الرئيسي
        main_layout.addWidget(login_frame)
        
        # إضافة معلومات الإصدار
        version_label = QLabel("الإصدار 1.0")
        version_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(version_label)
        
        # تعيين التخطيط الرئيسي
        self.setLayout(main_layout)
        
        # ربط الإشارات
        self.login_button.clicked.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
        
        # تعيين التركيز الأولي
        self.username_input.setFocus()
    
    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # التحقق من بيانات تسجيل الدخول
        user = self.db_handler.verify_login(username, password)
        
        if user:
            # تسجيل الدخول ناجح
            self.login_success.emit(user['username'], user['role'])
        else:
            # تسجيل الدخول فاشل
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
    
    def clear_fields(self):
        """مسح حقول الإدخال"""
        self.username_input.clear()
        self.password_input.clear()
        self.username_input.setFocus()