/* نمط حديث مستوحى من Bootstrap و Material Design */
/* لنظام إدارة العيادة السنية */

/* متغيرات الألوان العامة */
:root {
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --success-color: #28a745;
    --success-dark: #1e7e34;
    --warning-color: #ffc107;
    --warning-dark: #e0a800;
    --danger-color: #dc3545;
    --danger-dark: #c82333;
    --secondary-color: #6c757d;
    --secondary-dark: #545b62;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --border-color: #dee2e6;
    --text-color: #212529;
    --text-muted: #6c757d;
    --surface-color: #ffffff;
    --background-color: #f8f9fa;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
}

/* الخط الأساسي */
* {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Tahoma', sans-serif;
}

/* متغيرات الألوان (مستوحاة من Bootstrap) */
/* Primary Colors */
.btn-primary, QPushButton[class="btn-primary"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #007bff, stop: 1 #0056b3);
    border: 1px solid #0056b3;
    color: white;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    min-height: 20px;
}

.btn-primary:hover, QPushButton[class="btn-primary"]:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #0056b3, stop: 1 #004085);
    border-color: #004085;
}

/* Success Colors */
.btn-success, QPushButton[class="btn-success"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #28a745, stop: 1 #1e7e34);
    border: 1px solid #1e7e34;
    color: white;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-success:hover, QPushButton[class="btn-success"]:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1e7e34, stop: 1 #155724);
    border-color: #155724;
}

/* Warning Colors */
.btn-warning, QPushButton[class="btn-warning"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ffc107, stop: 1 #e0a800);
    border: 1px solid #e0a800;
    color: #212529;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-warning:hover, QPushButton[class="btn-warning"]:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e0a800, stop: 1 #d39e00);
    border-color: #d39e00;
}

/* Danger Colors */
.btn-danger, QPushButton[class="btn-danger"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #dc3545, stop: 1 #c82333);
    border: 1px solid #c82333;
    color: white;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-danger:hover, QPushButton[class="btn-danger"]:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #c82333, stop: 1 #bd2130);
    border-color: #bd2130;
}

/* Secondary Colors */
.btn-secondary, QPushButton[class="btn-secondary"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #6c757d, stop: 1 #545b62);
    border: 1px solid #545b62;
    color: white;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-secondary:hover, QPushButton[class="btn-secondary"]:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #545b62, stop: 1 #4e555b);
    border-color: #4e555b;
}

/* النافذة الرئيسية */
QMainWindow {
    background-color: #f8f9fa;
    color: #212529;
    font-size: 10pt;
}

/* Bootstrap-like Cards */
.card, QFrame[class="card"] {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin: 8px;
    padding: 16px;
}

.card-header, QFrame[class="card-header"] {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 12px 16px;
    font-weight: 600;
    color: #495057;
}

.card-body, QFrame[class="card-body"] {
    padding: 16px;
}

.card-footer, QFrame[class="card-footer"] {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 8px 8px;
    padding: 12px 16px;
}

/* Bootstrap-like Form Controls */
.form-control, QLineEdit[class="form-control"],
QTextEdit[class="form-control"], QComboBox[class="form-control"] {
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 8px 12px;
    color: #495057;
    font-size: 10pt;
}

.form-control:focus, QLineEdit[class="form-control"]:focus,
QTextEdit[class="form-control"]:focus, QComboBox[class="form-control"]:focus {
    border-color: #80bdff;
    background-color: #f8fcff;
}

/* Bootstrap-like Tables */
.table, QTableWidget[class="table"] {
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.table-striped QTableWidget::item:alternate {
    background-color: #f8f9fa;
}

.table-hover QTableWidget::item:hover {
    background-color: #e9ecef;
}

/* شريط العنوان المخصص */
QFrame[objectName="titleBar"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    border-bottom: 2px solid #2980b9;
}

QFrame[objectName="titleBar"] QLabel {
    color: white;
    font-weight: bold;
}

QFrame[objectName="titleBar"] QPushButton {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 80px;
}

QFrame[objectName="titleBar"] QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

QFrame[objectName="titleBar"] QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.1);
}

/* التبويبات الرئيسية */
QTabWidget {
    background-color: transparent;
    border: none;
}

QTabWidget::pane {
    border: 1px solid #bdc3c7;
    background-color: #ffffff;
    border-radius: 8px;
    margin-top: 10px;
}

QTabBar {
    background-color: transparent;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ffffff, stop: 1 #f8f9fa);
    border: 1px solid #bdc3c7;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 12px 20px;

    color: #2c3e50;
    font-weight: 500;
    min-width: 120px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    border-color: #2980b9;
    font-weight: bold;
}

QTabBar::tab:hover:!selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e8f4fd, stop: 1 #d6eaf8);
    border-color: #3498db;
}



/* الأزرار */
QPushButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ffffff, stop: 1 #f8f9fa);
    border: 1px solid #bdc3c7;
    border-radius: 6px;
    padding: 8px 16px;
    color: #2c3e50;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e8f4fd, stop: 1 #d6eaf8);
    border-color: #3498db;
}

QPushButton:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #d6eaf8, stop: 1 #aed6f1);
    border-color: #2980b9;
}

QPushButton:disabled {
    background-color: #f8f9fa;
    color: #7f8c8d;
    border-color: #dee2e6;
}

/* أزرار أساسية */
QPushButton.primary {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3498db, stop: 1 #2980b9);
    color: white;
    border-color: #2980b9;
    font-weight: bold;
}

QPushButton.primary:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #5dade2, stop: 1 #3498db);
}

/* أزرار النجاح */
QPushButton.success {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #27ae60, stop: 1 #229954);
    color: white;
    border-color: #229954;
}

QPushButton.success:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #58d68d, stop: 1 #27ae60);
}

/* أزرار التحذير */
QPushButton.warning {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f39c12, stop: 1 #e67e22);
    color: white;
    border-color: #e67e22;
}

QPushButton.warning:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f7dc6f, stop: 1 #f39c12);
}

/* أزرار الخطر */
QPushButton.danger {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e74c3c, stop: 1 #c0392b);
    color: white;
    border-color: #c0392b;
}

QPushButton.danger:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ec7063, stop: 1 #e74c3c);
}

/* حقول الإدخال المحسنة */
QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 10px 14px;
    background-color: var(--surface-color);
    color: var(--text-color);
    font-size: 11pt;
    min-height: 20px;
    selection-background-color: var(--primary-color);
    selection-color: white;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus,
QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: var(--primary-color);
    background-color: #f8fcff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover,
QSpinBox:hover, QDoubleSpinBox:hover {
    border-color: #adb5bd;
}

/* القوائم المنسدلة */
QComboBox {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 8px 12px;
    background-color: var(--surface-color);
    color: var(--text-color);
    min-height: 20px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iIzJjM2U1MCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

/* الجداول */
QTableWidget {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--surface-color);
    gridline-color: #e9ecef;
    selection-background-color: #e8f4fd;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

QTableWidget::item:selected {
    background-color: #e8f4fd;
    color: var(--text-color);
}

QHeaderView::section {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f9fa, stop: 1 #e9ecef);
    border: 1px solid var(--border-color);
    padding: 10px;
    font-weight: bold;
    color: var(--text-color);
}

/* شريط الحالة */
QStatusBar {
    background-color: var(--surface-color);
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 5px;
}

/* الإطارات */
QFrame {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--surface-color);
}

QGroupBox {
    font-weight: bold;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: var(--surface-color);
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 10px 0 10px;
    color: var(--primary-color);
    font-weight: bold;
}

/* أشرطة التمرير */
QScrollBar:vertical {
    background-color: #f8f9fa;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: var(--border-color);
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar:horizontal {
    background-color: #f8f9fa;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: var(--border-color);
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #95a5a6;
}

/* التقويم */
QCalendarWidget {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--surface-color);
}

QCalendarWidget QTableView {
    selection-background-color: #3498db;
    selection-color: white;
}
