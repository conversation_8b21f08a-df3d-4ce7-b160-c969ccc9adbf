#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Debug script for the comprehensive treatment pricing dialog
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, <PERSON><PERSON>abel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import traceback

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة الجديدة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class DebugWindow(QMainWindow):
    """نافذة تصحيح الأخطاء"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة التصحيح"""
        self.setWindowTitle("تصحيح أخطاء نافذة إدارة الأسعار")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("تصحيح أخطاء نافذة إدارة الأسعار")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # زر فتح النافذة مع تصحيح الأخطاء
        debug_btn = QPushButton("🔍 فتح النافذة مع تصحيح الأخطاء")
        debug_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        debug_btn.clicked.connect(self.debug_pricing_dialog)
        layout.addWidget(debug_btn)
        
        # منطقة عرض الأخطاء
        self.error_display = QLabel("لا توجد أخطاء حتى الآن...")
        self.error_display.setAlignment(Qt.AlignTop | Qt.AlignRight)
        self.error_display.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #2c3e50;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                min-height: 200px;
            }
        """)
        self.error_display.setWordWrap(True)
        layout.addWidget(self.error_display)
        
    def debug_pricing_dialog(self):
        """فتح النافذة مع تصحيح الأخطاء"""
        try:
            print("🔍 بدء تصحيح الأخطاء...")
            self.error_display.setText("🔍 بدء تصحيح الأخطاء...\n")
            
            print("1. محاولة إنشاء النافذة...")
            self.error_display.setText(self.error_display.text() + "1. محاولة إنشاء النافذة...\n")
            
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            print("2. تم إنشاء النافذة بنجاح!")
            self.error_display.setText(self.error_display.text() + "2. تم إنشاء النافذة بنجاح!\n")
            
            print("3. فحص محتويات النافذة...")
            self.error_display.setText(self.error_display.text() + "3. فحص محتويات النافذة...\n")
            
            # فحص المتغيرات
            print(f"   - عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
            print(f"   - عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
            print(f"   - الأسعار الحالية: {len(dialog.current_prices)} عنصر")
            
            self.error_display.setText(self.error_display.text() + 
                f"   - عدد حقول الأسماء: {len(dialog.treatment_name_fields)}\n" +
                f"   - عدد حقول الأسعار: {len(dialog.price_spinboxes)}\n" +
                f"   - الأسعار الحالية: {len(dialog.current_prices)} عنصر\n")
            
            print("4. محاولة فتح النافذة...")
            self.error_display.setText(self.error_display.text() + "4. محاولة فتح النافذة...\n")
            
            result = dialog.exec_()
            
            print(f"5. تم إغلاق النافذة بالنتيجة: {result}")
            self.error_display.setText(self.error_display.text() + f"5. تم إغلاق النافذة بالنتيجة: {result}\n")
            
            if result == dialog.Accepted:
                print("✅ تم حفظ التغييرات")
                self.error_display.setText(self.error_display.text() + "✅ تم حفظ التغييرات\n")
            else:
                print("❌ تم إلغاء العملية")
                self.error_display.setText(self.error_display.text() + "❌ تم إلغاء العملية\n")
                
        except Exception as e:
            error_msg = f"❌ خطأ في فتح النافذة: {str(e)}\n"
            error_msg += f"تفاصيل الخطأ:\n{traceback.format_exc()}"
            
            print(error_msg)
            self.error_display.setText(error_msg)
            
    def test_individual_methods(self):
        """اختبار الطرق الفردية"""
        try:
            print("🧪 اختبار الطرق الفردية...")
            
            # إنشاء مثيل من النافذة
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            # اختبار إنشاء مجموعة واحدة
            print("اختبار إنشاء مجموعة اللبية...")
            endodontic_group = dialog.create_editable_endodontic_group()
            print(f"تم إنشاء مجموعة اللبية: {endodontic_group is not None}")
            
            # اختبار إنشاء مجموعة الأسعار
            print("اختبار إنشاء مجموعة أسعار اللبية...")
            endodontic_prices_group = dialog.create_editable_endodontic_prices_group()
            print(f"تم إنشاء مجموعة أسعار اللبية: {endodontic_prices_group is not None}")
            
            print("✅ جميع الاختبارات نجحت!")
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {str(e)}")
            print(f"تفاصيل الخطأ:\n{traceback.format_exc()}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = DebugWindow()
    window.show()
    
    print("تم تشغيل نافذة تصحيح الأخطاء")
    print("اضغط على الزر لاختبار النافذة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
