#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة الخط العريض مع الاحتفاظ بباقي التحسينات
Test removing bold font while keeping other improvements
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class RemoveBoldFontTestWindow(QMainWindow):
    """نافذة اختبار إزالة الخط العريض"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إزالة الخط العريض مع الاحتفاظ بباقي التحسينات")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إزالة الخط العريض مع الاحتفاظ بباقي التحسينات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التغييرات المطبقة على مربعات الاختيار:
        
        ❌ إزالة خاصية الخط العريض:
        • حذف font-weight: bold من CSS لجميع مربعات الاختيار
        • ترك الخط بالوزن العادي (normal font weight)
        • تطبيق هذا التغيير على المجموعات الأربعة:
          - مجموعة اللبية (Endodontic) - 8 خيارات
          - مجموعة الترميمية (Restorative) - 5 خيارات
          - مجموعة التيجان (Crowns) - 6 خيارات
          - مجموعة الجراحة (Surgery) - 7 خيارات
        
        ✅ الاحتفاظ بباقي التحسينات:
        • الاحتفاظ بحجم الخط الكبير (font-size: 14px)
        • الاحتفاظ بالعرض المحسن (min-width: 140px)
        • الاحتفاظ بالارتفاع المحسن (min-height: 22px)
        • الاحتفاظ بالحشو المحسن (padding: 3px)
        • الاحتفاظ بمحاذاة النص (text-align: left)
        • الاحتفاظ بإزالة الهوامش (margin: 0px)
        
        🎨 عدم التأثير على باقي التخطيط:
        • الاحتفاظ بالتخطيط الشبكي 2x2 للمجموعات
        • الاحتفاظ بجميع الوظائف والتفاعل الحالي
        • عدم التأثير على أي عناصر أخرى في التبويبة
        
        ✅ النتائج المحققة:
        • نص واضح بحجم كبير (14px) لكن بوزن خط عادي
        • وضوح جيد مع مظهر أقل بروزاً من الخط العريض
        • الحفاظ على جميع التحسينات الأخرى
        • مظهر أكثر نعومة ولطافة للنص
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة خيارات المعالجة مع الخط العادي
        options_title = QLabel("⚙️ خيارات المعالجة (خط كبير عادي)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setAlignment(Qt.AlignCenter)
        self.selected_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                padding: 12px;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد عشوائي
        random_btn = QPushButton("تحديد خيارات عشوائية")
        random_btn.clicked.connect(self.select_random_options)
        random_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(random_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار إزالة الخط العريض:
        
        🔍 ما يجب ملاحظته:
        • النص بحجم كبير (14px) لكن بوزن خط عادي (ليس عريض)
        • الوضوح جيد مع مظهر أقل بروزاً من الخط العريض
        • جميع التحسينات الأخرى محفوظة:
          - العرض المحسن (140px)
          - الارتفاع المحسن (22px)
          - الحشو المحسن (3px)
          - محاذاة النص اليسرى
          - إزالة الهوامش
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على مربعات اختيار مختلفة لاختبار الوظائف
        • استخدم زر "تحديد خيارات عشوائية" لرؤية التحديد
        • استخدم زر "مسح جميع الخيارات" لإلغاء التحديد
        • لاحظ النص الكبير العادي (غير العريض) في جميع المجموعات
        • تحقق من أن جميع التحسينات الأخرى محفوظة
        
        ✅ النتائج المتوقعة:
        • نص واضح بحجم كبير لكن بوزن عادي
        • مظهر أكثر نعومة ولطافة
        • الحفاظ على جميع التحسينات الأخرى
        • الحفاظ على جميع الوظائف الأصلية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إزالة الخط العريض بنجاح مع الاحتفاظ بباقي التحسينات!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_random_options(self):
        """تحديد خيارات عشوائية للاختبار"""
        import random
        checkboxes = list(self.treatment_options.checkboxes.values())
        # تحديد 7-14 خيارات عشوائياً
        num_to_select = random.randint(7, 14)
        selected_checkboxes = random.sample(checkboxes, min(num_to_select, len(checkboxes)))
        
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات العشوائية
        for checkbox in selected_checkboxes:
            checkbox.setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = RemoveBoldFontTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
