#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار زيادة العرض الأدنى لحقول الكلفة وحقل رقم السن
Test increasing minimum width for cost fields and tooth number field
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, TreatmentSessionDialog

class IncreasedFieldWidthTestWindow(QMainWindow):
    """نافذة اختبار زيادة العرض الأدنى للحقول"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار زيادة العرض الأدنى لحقول الكلفة وحقل رقم السن")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار زيادة العرض الأدنى لحقول الكلفة وحقل رقم السن")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 زيادة العرض الأدنى لتحسين المظهر والوضوح:
        
        ✅ زيادة عرض حقول الكلفة:
        • زيادة العرض الثابت لحقل الكلفة في خطة المعالجة من 120px إلى 150px (+30px)
        • زيادة عرض عنوان "الكلفة" ليطابق عرض الحقل الجديد (150px)
        • زيادة العرض الثابت لحقل الدفعة في نافذة جلسات المعالجة إلى 150px
        • إضافة المحاذاة المركزية لحقل الدفعة لتناسق أفضل
        
        ✅ زيادة عرض حقل رقم السن:
        • زيادة العرض الثابت لحقل رقم السن من 80px إلى 100px (+20px)
        • زيادة عرض عنوان "رقم السن" ليطابق عرض الحقل الجديد (100px)
        • تحسين وضوح عرض أرقام الأسنان
        
        🔧 التحسينات التقنية المطبقة:
        • العناوين والحقول في كل مجموعة لها نفس العرض الجديد
        • الاحتفاظ بجميع الخصائص الأخرى:
          - المحاذاة المركزية: setAlignment(Qt.AlignCenter)
          - لاحقة العملة: "ل.س" و "ليرة سورية"
          - إزالة أسهم التحكم: setButtonSymbols(NoButtons)
          - الحد الأقصى: 999,999,999
          - نوع البيانات: QSpinBox (أرقام صحيحة)
        • التخطيط العام لقسم خطة المعالجة لم يتأثر سلبياً
        
        📊 المقارنة قبل وبعد التحسين:
        • حقل الكلفة: 120px → 150px (+25% زيادة)
        • عنوان الكلفة: تلقائي → 150px (تطابق مع الحقل)
        • حقل الدفعة: بدون عرض ثابت → 150px (تحسين جديد)
        • حقل رقم السن: 80px → 100px (+25% زيادة)
        • عنوان رقم السن: 80px → 100px (تطابق مع الحقل)
        
        ✅ النتائج المحققة:
        • عرض أوضح للقيم المالية الكبيرة (حتى 999,999,999)
        • وضوح أكبر لأرقام الأسنان
        • مظهر عام متوازن ومتناسق
        • تحسين تجربة المستخدم
        • سهولة قراءة القيم الطويلة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع العرض المحسن
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع عرض محسن للحقول)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار قيمة كلفة كبيرة
        test_large_cost_btn = QPushButton("اختبار كلفة 123,456,789 ل.س")
        test_large_cost_btn.clicked.connect(self.test_large_cost)
        test_large_cost_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_large_cost_btn)
        
        # زر اختبار رقم سن
        test_tooth_btn = QPushButton("اختبار رقم السن 18")
        test_tooth_btn.clicked.connect(self.test_tooth_number)
        test_tooth_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_tooth_btn)
        
        # زر اختبار نافذة الجلسات
        test_session_btn = QPushButton("اختبار نافذة الجلسات")
        test_session_btn.clicked.connect(self.test_session_dialog)
        test_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_session_btn)
        
        # زر مسح القيم
        clear_btn = QPushButton("مسح جميع القيم")
        clear_btn.clicked.connect(self.clear_values)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار العرض المحسن:
        
        🔍 ما يجب ملاحظته:
        • حقل الكلفة في خطة المعالجة أوسع (150px) لعرض القيم الكبيرة بوضوح
        • عنوان "الكلفة" يطابق عرض الحقل تماماً
        • حقل رقم السن أوسع (100px) لوضوح أكبر
        • عنوان "رقم السن" يطابق عرض الحقل تماماً
        • حقل الدفعة في نافذة الجلسات بعرض 150px مع محاذاة مركزية
        • المظهر العام متوازن ومتناسق
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على "اختبار كلفة 123,456,789 ل.س" لرؤية عرض قيمة كبيرة
        • انقر على "اختبار رقم السن 18" لرؤية عرض رقم السن
        • انقر على "اختبار نافذة الجلسات" لفتح نافذة الجلسات واختبار حقل الدفعة
        • جرب إدخال قيم مختلفة في الحقول لاختبار العرض
        • لاحظ التطابق في العرض بين العناوين والحقول
        • تحقق من أن القيم الطويلة تظهر بوضوح كامل
        
        ✅ النتائج المتوقعة:
        • عرض واضح ومقروء للقيم المالية الكبيرة
        • وضوح أكبر لأرقام الأسنان
        • تطابق كامل في العرض بين العناوين والحقول
        • مظهر متوازن ومتناسق للواجهة
        • تحسين تجربة المستخدم مع الحقول الأوسع
        • الحفاظ على جميع الوظائف الأصلية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم زيادة العرض الأدنى لجميع حقول الكلفة وحقل رقم السن بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_large_cost(self):
        """اختبار قيمة كلفة كبيرة"""
        # تعيين قيمة كلفة كبيرة لاختبار العرض
        self.dental_tab.treatment_plan.cost_spinbox.setValue(123456789)
        
    def test_tooth_number(self):
        """اختبار رقم السن"""
        # تعيين رقم سن لاختبار العرض
        self.dental_tab.treatment_plan.tooth_number_edit.setText("18")
        
    def test_session_dialog(self):
        """اختبار نافذة جلسة المعالجة مع كلفة كبيرة"""
        # إنشاء نافذة جلسة معالجة مع كلفة كبيرة لاختبار العرض
        dialog = TreatmentSessionDialog(
            plan_number="001",
            cost=987654321,  # كلفة كبيرة لاختبار العرض
            parent=self
        )
        dialog.exec_()
        
    def clear_values(self):
        """مسح جميع القيم"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(0)
        self.dental_tab.treatment_plan.tooth_number_edit.clear()

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = IncreasedFieldWidthTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
