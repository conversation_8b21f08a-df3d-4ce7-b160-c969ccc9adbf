#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التناسق البصري الموحد لجميع عناوين الحاويات
Test unified visual consistency for all container titles
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class UnifiedTitlesTestWindow(QMainWindow):
    """نافذة اختبار التناسق البصري الموحد للعناوين"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار التناسق البصري الموحد - عناوين الحاويات")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار التناسق البصري الموحد لجميع عناوين الحاويات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الاختبار
        info_label = QLabel("""
        🎯 التحسينات المطبقة على عنوان "مخطط الأسنان التفاعلي":
        
        ✅ تحويل من QLabel عادي إلى QGroupBox متقدم
        ✅ تطبيق نفس تنسيق CSS المستخدم في الحاويات الأخرى:
           • نفس الخط والحجم (font-size: 12px, font-weight: bold)
           • نفس اللون (#007bff) للعنوان والحدود
           • نفس الخلفية (#f8f9ff) والحدود المدورة
           • نفس المسافات والهوامش
        ✅ تحسين موضع العنوان: محاذاة إلى اليمين (RTL)
        ✅ تحقيق تناسق بصري كامل مع جميع الحاويات
        
        📋 ما يجب ملاحظته:
        • جميع العناوين تستخدم نفس تنسيق QGroupBox
        • ألوان وخطوط موحدة في جميع الحاويات
        • محاذاة صحيحة للنص العربي (RTL)
        • مظهر احترافي ومتسق
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #2c3e50;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة واجهة علاج الأسنان الكاملة للاختبار
        try:
            self.dental_tab = DentalTreatmentsTab(None)  # None بدلاً من db_handler للاختبار
            layout.addWidget(self.dental_tab)
            
            # معلومات النجاح
            success_label = QLabel("✅ تم تحميل واجهة علاج الأسنان بنجاح!")
            success_label.setAlignment(Qt.AlignCenter)
            success_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #27ae60;
                    padding: 10px;
                    background-color: #d5f4e6;
                    border: 2px solid #27ae60;
                    border-radius: 8px;
                    margin-top: 10px;
                }
            """)
            layout.addWidget(success_label)
            
        except Exception as e:
            # في حالة وجود خطأ
            error_label = QLabel(f"❌ خطأ في تحميل الواجهة: {str(e)}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #e74c3c;
                    padding: 10px;
                    background-color: #fadbd8;
                    border: 2px solid #e74c3c;
                    border-radius: 8px;
                    margin-top: 10px;
                }
            """)
            layout.addWidget(error_label)
        
        # نتيجة الاختبار
        result_label = QLabel("""
        🎉 النتيجة: تم توحيد التناسق البصري بنجاح!
        
        الآن جميع عناوين الحاويات تستخدم:
        • نفس تنسيق QGroupBox المتقدم
        • نفس الألوان والخطوط والحدود
        • نفس المسافات والهوامش
        • محاذاة صحيحة للنص العربي
        
        ✅ مخطط الأسنان التفاعلي
        ✅ خيارات المعالجة  
        ✅ خطة المعالجة السنية
        """)
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #2980b9;
                padding: 15px;
                background-color: #eaf2f8;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 20px;
                line-height: 1.4;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار البصري"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UnifiedTitlesTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
