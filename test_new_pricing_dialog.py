#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار سريع للنافذة الجديدة لإدارة الأسعار
Quick test for the new comprehensive pricing dialog
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة الجديدة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class QuickTestWindow(QMainWindow):
    """نافذة اختبار سريع"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار سريع - النافذة الجديدة لإدارة الأسعار")
        self.setGeometry(200, 200, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار النافذة الجديدة لإدارة أسعار وأسماء المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف
        description = QLabel("""
النافذة الجديدة تتضمن:
✅ جميع المجموعات الثمانية للمعالجة
✅ تعديل أسماء المعالجات مباشرة
✅ تعديل الأسعار لجميع المعالجات
✅ حفظ دائم للتغييرات
✅ واجهة عربية RTL كاملة
✅ تصميم حديث ومتجاوب
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #3498db;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # زر فتح النافذة الجديدة
        open_btn = QPushButton("🚀 فتح النافذة الجديدة لإدارة الأسعار")
        open_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #4e60c6 0%, #5e377e 100%);
            }
        """)
        open_btn.clicked.connect(self.open_new_pricing_dialog)
        layout.addWidget(open_btn)
        
        # معلومات إضافية
        info = QLabel("إذا فتحت النافذة بنجاح، فهذا يعني أن التكامل يعمل بشكل صحيح!")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 6px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(info)
        
    def open_new_pricing_dialog(self):
        """فتح النافذة الجديدة لإدارة الأسعار"""
        try:
            print("محاولة فتح النافذة الجديدة...")
            dialog = ComprehensiveTreatmentPricingDialog(self)
            print("تم إنشاء النافذة بنجاح!")
            
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("تم حفظ التغييرات")
                self.show_success_message()
            else:
                print("تم إلغاء العملية")
                self.show_cancel_message()
                
        except Exception as e:
            print(f"خطأ في فتح النافذة: {e}")
            import traceback
            traceback.print_exc()
            self.show_error_message(str(e))
            
    def show_success_message(self):
        """عرض رسالة نجاح"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "نجح!", 
                              "تم فتح النافذة الجديدة وحفظ التغييرات بنجاح!\n"
                              "النظام الجديد يعمل بشكل صحيح.")
        
    def show_cancel_message(self):
        """عرض رسالة إلغاء"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "تم الإلغاء", 
                              "تم فتح النافذة الجديدة بنجاح ولكن تم إلغاء العملية.\n"
                              "هذا يعني أن النظام يعمل بشكل صحيح!")
        
    def show_error_message(self, error):
        """عرض رسالة خطأ"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.critical(self, "خطأ", f"حدث خطأ في فتح النافذة:\n{error}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = QuickTestWindow()
    window.show()
    
    print("تم تشغيل نافذة الاختبار السريع")
    print("اضغط على الزر لاختبار النافذة الجديدة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
