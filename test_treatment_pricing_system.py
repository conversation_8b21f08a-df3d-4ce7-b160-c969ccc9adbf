#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام التسعير الديناميكي في تبويبة علاج الأسنان
Test dynamic pricing system in dental treatment tab
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget, TreatmentPlanWidget

class TreatmentPricingTestWindow(QMainWindow):
    """نافذة اختبار نظام التسعير الديناميكي"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار نظام التسعير الديناميكي في تبويبة علاج الأسنان")
        self.setGeometry(100, 100, 1600, 1000)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار نظام التسعير الديناميكي في تبويبة علاج الأسنان")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 نظام التسعير الديناميكي المطور:
        
        ✅ مجموعات الأسعار الجديدة:
        • أسعار اللبية - مع أسعار افتراضية لكل نوع معالجة لبية
        • أسعار الترميمية - مع أسعار افتراضية لكل نوع معالجة ترميمية
        • أسعار التيجان - مع أسعار افتراضية لكل نوع تاج
        • أسعار الجراحة - مع أسعار افتراضية لكل نوع عملية جراحية
        
        ✅ الحساب التلقائي للكلفة:
        • عند تحديد أي مربع اختيار، يُضاف السعر المقابل للكلفة الإجمالية
        • عند إلغاء التحديد، يُطرح السعر من الكلفة الإجمالية
        • تحديث فوري لحقل الكلفة في خطة المعالجة
        • إمكانية تعديل الأسعار وإعادة حساب الكلفة تلقائياً
        
        🔧 الميزات التقنية:
        • أسعار افتراضية معقولة لجميع أنواع المعالجات
        • حقول أسعار قابلة للتعديل (QSpinBox بدون أسهم)
        • دعم القيم المالية الكبيرة (حتى 999,999,999)
        • حفظ الأسعار المخصصة مع بيانات خطة المعالجة
        • إعادة تعيين الأسعار الافتراضية
        
        📊 الأسعار الافتراضية (بالليرة السورية):
        
        🦷 اللبية:
        • Vital: 120,000 ل.س
        • Necrotic: 150,000 ل.س
        • إعادة معالجة: 200,000 ل.س
        • متكلسة: 180,000 ل.س
        • C shape: 250,000 ل.س
        • ذروة مفتوحة: 160,000 ل.س
        • أداة مكسورة: 300,000 ل.س
        
        🔧 الترميمية:
        • كومبوزت: 75,000 ل.س
        • أملغم: 50,000 ل.س
        • GIC: 40,000 ل.س
        • وتد فايبر: 80,000 ل.س
        • قلب معدني: 60,000 ل.س
        
        👑 التيجان:
        • خزف معدن: 150,000 ل.س
        • زيركون 4D: 200,000 ل.س
        • زيركون مغطى إيماكس: 250,000 ل.س
        • زيركون مغطى خزف: 220,000 ل.س
        • زيركون cutback: 230,000 ل.س
        • ستانلس: 80,000 ل.س
        • إيماكس: 180,000 ل.س
        
        🔪 الجراحة:
        • خلع بسيط: 30,000 ل.س
        • خلع جراحي: 75,000 ل.س
        • خلع ضرس عقل: 100,000 ل.س
        • تضحيك: 120,000 ل.س
        • زرع: 500,000 ل.س
        • رفع جيب: 300,000 ل.س
        • تطعيم عظم: 250,000 ل.س
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة حاوية خيارات المعالجة مع الأسعار
        options_title = QLabel("⚙️ حاوية خيارات المعالجة (مع مجموعات الأسعار الجديدة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء حاوية خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        layout.addWidget(self.treatment_options)
        
        # إضافة حاوية خطة المعالجة
        plan_title = QLabel("📋 حاوية خطة المعالجة (مع حقل الكلفة التلقائي)")
        plan_title.setAlignment(Qt.AlignCenter)
        plan_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #28a745;
                padding: 10px;
                background-color: #f0fff0;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(plan_title)
        
        # إنشاء حاوية خطة المعالجة
        self.treatment_plan = TreatmentPlanWidget()
        layout.addWidget(self.treatment_plan)
        
        # ربط الحاويتين للحساب التلقائي
        self.treatment_options.treatment_plan = self.treatment_plan
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار تحديد خيارات متعددة
        test_multiple_btn = QPushButton("اختبار تحديد خيارات متعددة")
        test_multiple_btn.clicked.connect(self.test_multiple_selections)
        test_multiple_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_multiple_btn)
        
        # زر اختبار تعديل الأسعار
        test_prices_btn = QPushButton("اختبار تعديل الأسعار")
        test_prices_btn.clicked.connect(self.test_price_modification)
        test_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_prices_btn)
        
        # زر إعادة تعيين الأسعار الافتراضية
        reset_prices_btn = QPushButton("إعادة تعيين الأسعار الافتراضية")
        reset_prices_btn.clicked.connect(self.reset_default_prices)
        reset_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(reset_prices_btn)
        
        # زر مسح جميع التحديدات
        clear_all_btn = QPushButton("مسح جميع التحديدات")
        clear_all_btn.clicked.connect(self.clear_all_selections)
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_all_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار نظام التسعير الديناميكي:
        
        🔍 ما يجب ملاحظته:
        • مجموعات الأسعار الجديدة إلى يسار كل مجموعة خيارات
        • الأسعار الافتراضية المعقولة لكل نوع معالجة
        • حقل الكلفة في خطة المعالجة يبدأ فارغاً
        
        🧪 اختبارات يمكن إجراؤها:
        • حدد أي مربع اختيار ولاحظ تحديث الكلفة تلقائياً
        • ألغ تحديد مربع اختيار ولاحظ طرح السعر من الكلفة
        • عدّل أي سعر في مجموعات الأسعار ولاحظ إعادة حساب الكلفة
        • حدد خيارات من فئات مختلفة ولاحظ جمع الأسعار
        • استخدم أزرار الاختبار لتجربة سيناريوهات مختلفة
        
        ✅ النتائج المتوقعة:
        • حساب تلقائي دقيق للكلفة الإجمالية
        • تحديث فوري عند تغيير الخيارات أو الأسعار
        • مرونة في تخصيص الأسعار لكل عيادة
        • واجهة منظمة وسهلة الاستخدام
        • دعم القيم المالية الكبيرة
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تطوير نظام التسعير الديناميكي بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_multiple_selections(self):
        """اختبار تحديد خيارات متعددة"""
        # تحديد بعض الخيارات من فئات مختلفة
        test_options = [
            "endodontic_Vital",
            "restorative_كومبوزت", 
            "crowns_زيركون 4D",
            "surgery_خلع بسيط"
        ]
        
        for option_key in test_options:
            if option_key in self.treatment_options.checkboxes:
                self.treatment_options.checkboxes[option_key].setChecked(True)
                
    def test_price_modification(self):
        """اختبار تعديل الأسعار"""
        # تعديل بعض الأسعار
        price_modifications = {
            "endodontic_Vital": 140000,
            "restorative_كومبوزت": 85000,
            "crowns_زيركون 4D": 220000
        }
        
        for option_key, new_price in price_modifications.items():
            if option_key in self.treatment_options.price_spinboxes:
                self.treatment_options.price_spinboxes[option_key].setValue(new_price)
                
    def reset_default_prices(self):
        """إعادة تعيين الأسعار الافتراضية"""
        self.treatment_options.reset_to_default_prices()
        
    def clear_all_selections(self):
        """مسح جميع التحديدات"""
        for checkbox in self.treatment_options.checkboxes.values():
            checkbox.setChecked(False)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TreatmentPricingTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
