#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد محاذاة مربعات الاختيار في حاوية خيارات المعالجة
Test unified checkbox alignment in treatment options container
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class UnifiedCheckboxAlignmentTestWindow(QMainWindow):
    """نافذة اختبار توحيد محاذاة مربعات الاختيار"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار توحيد محاذاة مربعات الاختيار")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار توحيد محاذاة مربعات الاختيار")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة على محاذاة مربعات الاختيار:
        
        📐 توحيد محاذاة جميع مربعات الاختيار:
        • تأكيد أن جميع مربعات الاختيار داخل كل مجموعة لها نفس المحاذاة الأفقية
        • جعل محاذاة كل مربع اختيار مطابقة تماماً لمحاذاة أول مربع في نفس المجموعة
        • تطبيق التحسين على المجموعات الأربعة:
          - مجموعة اللبية (Endodontic) - 8 خيارات
          - مجموعة الترميمية (Restorative) - 5 خيارات
          - مجموعة التيجان (Crowns) - 6 خيارات
          - مجموعة الجراحة (Surgery) - 7 خيارات
        
        ⚖️ ضبط المحاذاة الأفقية:
        • استخدام Qt.AlignCenter موحد لجميع المربعات في كل مجموعة
        • عدم وجود اختلاف في المحاذاة بين المربعات داخل نفس المجموعة
        • ضبط المسافات والهوامش لتكون متناسقة تماماً
        
        🔧 التحسينات التقنية المطبقة:
        • مسافة موحدة بين المربعات: setSpacing(4) لجميع المجموعات
        • هوامش موحدة: margin: 2px 2px 2px 2px لجميع المربعات
        • حشو موحد: padding: 1px لجميع المربعات
        • محاذاة نص موحدة: text-align: center لجميع المربعات
        • محاذاة widget موحدة: Qt.AlignCenter لجميع المربعات
        
        ✅ النتائج المحققة:
        • محاذاة أفقية موحدة ومتناسقة
        • جميع المربعات متراصة على نفس الخط الوهمي الأفقي
        • مظهر احترافي ومنتظم
        • الحفاظ على جميع الوظائف والتفاعل
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة خيارات المعالجة مع المحاذاة الموحدة
        options_title = QLabel("⚙️ خيارات المعالجة (محاذاة موحدة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setAlignment(Qt.AlignCenter)
        self.selected_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                padding: 12px;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد عشوائي
        random_btn = QPushButton("تحديد خيارات عشوائية")
        random_btn.clicked.connect(self.select_random_options)
        random_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(random_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار المحاذاة الموحدة:
        
        🔍 ما يجب ملاحظته:
        • جميع مربعات الاختيار داخل كل مجموعة لها نفس المحاذاة الأفقية
        • المربعات متراصة على نفس الخط الوهمي الأفقي
        • المسافات متناسقة ومنتظمة بين جميع المربعات
        • الهوامش والحشو موحد لجميع المربعات
        • لا يوجد اختلاف في المحاذاة بين المربعات داخل نفس المجموعة
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على مربعات اختيار مختلفة لاختبار الوظائف
        • استخدم زر "تحديد خيارات عشوائية" لرؤية التحديد
        • استخدم زر "مسح جميع الخيارات" لإلغاء التحديد
        • لاحظ المحاذاة الموحدة للمربعات في كل مجموعة
        • تحقق من أن جميع المربعات متراصة بشكل مثالي
        
        ✅ النتائج المتوقعة:
        • محاذاة أفقية موحدة ومتناسقة
        • مظهر احترافي ومنتظم
        • سهولة قراءة وتفاعل محسنة
        • الحفاظ على جميع الوظائف الأصلية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم توحيد محاذاة مربعات الاختيار بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_random_options(self):
        """تحديد خيارات عشوائية للاختبار"""
        import random
        checkboxes = list(self.treatment_options.checkboxes.values())
        # تحديد 5-10 خيارات عشوائياً
        num_to_select = random.randint(5, 10)
        selected_checkboxes = random.sample(checkboxes, min(num_to_select, len(checkboxes)))
        
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات العشوائية
        for checkbox in selected_checkboxes:
            checkbox.setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UnifiedCheckboxAlignmentTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
