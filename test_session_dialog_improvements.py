#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات نافذة إضافة جلسة معالجة سنية
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestSessionDialogWindow(QMainWindow):
    """نافذة اختبار تحسينات نافذة جلسة المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار تحسينات نافذة إضافة جلسة معالجة سنية")
        self.setGeometry(200, 200, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار تحسينات نافذة إضافة جلسة معالجة سنية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف التحسينات
        description = QLabel("""
🔧 التحسينات المطبقة:

1️⃣ زيادة الحد الأدنى لارتفاع النافذة:
   • من: 700px إلى 800px
   • الهدف: إظهار جميع الإطارات الداخلية بوضوح
   • منع قطع المحتوى أو الحاجة للتمرير

2️⃣ تحسين محاذاة التسميات إلى اليمين:
   • CSS: qproperty-alignment: 'AlignRight | AlignVCenter'
   • Python: setAlignment(Qt.AlignRight | Qt.AlignVCenter)
   • إضافة: setLayoutDirection(Qt.RightToLeft)
   • النتيجة: محاذاة مثالية للنص العربي

🎯 الفوائد:
✅ عرض أفضل لجميع الحقول والإطارات
✅ محاذاة صحيحة للنص العربي
✅ تجربة مستخدم محسنة
✅ عدم قطع المحتوى
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # تعليمات الاختبار
        instructions = QLabel("""
📋 تعليمات الاختبار:

1. شغل التطبيق الرئيسي: python main.py
2. اختر مريض من تبويبة المرضى
3. اذهب إلى تبويبة المعالجة السنية
4. اضغط "إضافة جلسة معالجة سنية"
5. تحقق من:
   • ارتفاع النافذة الجديد (800px)
   • ظهور جميع الإطارات بوضوح
   • محاذاة التسميات إلى اليمين
   • عدم قطع أي محتوى

✅ النتيجة المتوقعة:
• نافذة أطول تعرض جميع الحقول
• تسميات محاذاة إلى اليمين بوضوح
• تجربة مستخدم محسنة
        """)
        instructions.setAlignment(Qt.AlignRight)
        instructions.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #495057;
                padding: 15px;
                background-color: #e3f2fd;
                border-radius: 8px;
                border-left: 4px solid #2196f3;
                line-height: 1.6;
            }
        """)
        layout.addWidget(instructions)
        
        # زر فتح التطبيق الرئيسي
        open_main_btn = QPushButton("🚀 فتح التطبيق الرئيسي للاختبار")
        open_main_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
        """)
        open_main_btn.clicked.connect(self.open_main_app)
        layout.addWidget(open_main_btn)
        
        # منطقة الحالة
        self.status_label = QLabel("جاهز لاختبار تحسينات نافذة جلسة المعالجة...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 5px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.status_label)
        
    def open_main_app(self):
        """فتح التطبيق الرئيسي"""
        try:
            import subprocess
            import os
            
            # تشغيل التطبيق الرئيسي
            subprocess.Popen([sys.executable, "main.py"], cwd=os.getcwd())
            
            self.status_label.setText("✅ تم فتح التطبيق الرئيسي - اتبع تعليمات الاختبار أعلاه")
            
            print("=" * 60)
            print("🚀 تم فتح التطبيق الرئيسي")
            print("=" * 60)
            print("📋 خطوات الاختبار:")
            print("1. اختر مريض من تبويبة المرضى")
            print("2. اذهب إلى تبويبة المعالجة السنية")
            print("3. اضغط 'إضافة جلسة معالجة سنية'")
            print("4. تحقق من ارتفاع النافذة الجديد (800px)")
            print("5. تحقق من محاذاة التسميات إلى اليمين")
            print("6. تأكد من ظهور جميع الإطارات بوضوح")
            print("=" * 60)
            
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في فتح التطبيق: {e}")
            print(f"❌ خطأ في فتح التطبيق: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestSessionDialogWindow()
    window.show()
    
    print("تم تشغيل اختبار تحسينات نافذة جلسة المعالجة")
    print("اضغط على الزر لفتح التطبيق الرئيسي واختبار التحسينات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
