#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استجابة واجهة خيارات المعالجة السنية
Test for responsive dental treatment options interface
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel, QSizePolicy
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class TestWindow(QMainWindow):
    """نافذة اختبار لواجهة خيارات المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار استجابة خيارات المعالجة السنية")
        self.setGeometry(100, 100, 1000, 600)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار استجابة واجهة خيارات المعالجة السنية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # أزرار اختبار أحجام مختلفة
        buttons_layout = QHBoxLayout()
        
        small_btn = QPushButton("نافذة صغيرة (600x400)")
        small_btn.clicked.connect(lambda: self.resize(600, 400))
        small_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        buttons_layout.addWidget(small_btn)
        
        medium_btn = QPushButton("نافذة متوسطة (900x600)")
        medium_btn.clicked.connect(lambda: self.resize(900, 600))
        medium_btn.setStyleSheet(self.get_button_style("#f39c12"))
        buttons_layout.addWidget(medium_btn)
        
        large_btn = QPushButton("نافذة كبيرة (1200x800)")
        large_btn.clicked.connect(lambda: self.resize(1200, 800))
        large_btn.setStyleSheet(self.get_button_style("#27ae60"))
        buttons_layout.addWidget(large_btn)
        
        xlarge_btn = QPushButton("نافذة كبيرة جداً (1600x900)")
        xlarge_btn.clicked.connect(lambda: self.resize(1600, 900))
        xlarge_btn.setStyleSheet(self.get_button_style("#3498db"))
        buttons_layout.addWidget(xlarge_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات الاختبار
        info_label = QLabel("""
        تعليمات الاختبار:
        1. اضغط على الأزرار أعلاه لتغيير حجم النافذة
        2. لاحظ كيف تتكيف مربعات الاختيار مع الحجم الجديد
        3. تأكد من عدم اختفاء أو تداخل أي عناصر
        4. جرب تحديد وإلغاء تحديد مربعات الاختيار
        5. تأكد من عمل التمرير عند الحاجة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                color: #2c3e50;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة واجهة خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 10px;
                border-radius: 5px;
                font-size: 11px;
                color: #2980b9;
                border: 1px solid #3498db;
            }
        """)
        layout.addWidget(self.selected_info)
        
    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}aa;
            }}
        """
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
