#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسين حقول سنة الولادة والعمر في نموذج معلومات المريض
Test improving birth year and age fields in patient information form
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.patients_tab import PatientsTab

class ImprovedAgeBirthYearFieldsTestWindow(QMainWindow):
    """نافذة اختبار تحسين حقول سنة الولادة والعمر"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تحسين حقول سنة الولادة والعمر في نموذج معلومات المريض")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تحسين حقول سنة الولادة والعمر في نموذج معلومات المريض")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 تحسين حقول سنة الولادة والعمر في نموذج معلومات المريض:
        
        ✅ إزالة أسهم التحكم من حقول سنة الولادة والعمر:
        • إزالة أسهم الزيادة والنقصان من حقل سنة الولادة باستخدام setButtonSymbols(QSpinBox.NoButtons)
        • إزالة أسهم الزيادة والنقصان من حقل العمر باستخدام setButtonSymbols(QSpinBox.NoButtons)
        • الاحتفاظ بجميع الوظائف الأخرى (الحد الأدنى، الحد الأقصى، المحاذاة، الاتجاه)
        
        ✅ تعيين القيمة الافتراضية فارغة لحقول سنة الولادة والعمر:
        • استخدام setSpecialValueText("") لحقل سنة الولادة لإظهار حقل فارغ عند القيمة 1900
        • استخدام setSpecialValueText("") لحقل العمر لإظهار حقل فارغ عند القيمة 0
        • تعيين الحد الأدنى المناسب (0 للعمر و 1900 لسنة الولادة) لتفعيل النص الخاص
        
        🔧 التحسينات التقنية المطبقة:
        • الاحتفاظ بوظيفة الحساب التلقائي بين سنة الولادة والعمر
        • تحديث دوال calculate_age و calculate_birth_year للتعامل مع القيم الفارغة
        • تحديث دالة get_patient_data للتعامل مع القيم الفارغة (حفظ كـ None)
        • تحديث دالة set_patient_data للتعامل مع القيم الفارغة من قاعدة البيانات
        • تحديث دالة clear_form لتعيين القيم الفارغة بدلاً من القيم الافتراضية
        
        📊 القيم الافتراضية الجديدة:
        • حقل سنة الولادة: فارغ (القيمة الخاصة 1900)
        • حقل العمر: فارغ (القيمة الخاصة 0)
        • النطاق المسموح: 1900-2024 لسنة الولادة، 0-120 للعمر
        • الحساب التلقائي: يعمل فقط مع القيم الصحيحة
        
        ✅ النتائج المحققة:
        • تحسين تجربة المستخدم بإزالة العناصر البصرية غير الضرورية
        • حقول تبدأ فارغة بدلاً من قيم افتراضية
        • سهولة إدخال البيانات مباشرة بدون حذف القيم الافتراضية
        • الحفاظ على جميع الوظائف الحالية والحساب التلقائي
        • التعامل الصحيح مع القيم الفارغة في الحفظ والاسترجاع
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة المرضى مع الحقول المحسنة
        options_title = QLabel("⚙️ تبويبة المرضى (مع حقول سنة الولادة والعمر محسنة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة المرضى
        self.patients_tab = PatientsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.patients_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار إدخال سنة الولادة
        test_birth_year_btn = QPushButton("اختبار إدخال سنة الولادة 1985")
        test_birth_year_btn.clicked.connect(self.test_birth_year_input)
        test_birth_year_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_birth_year_btn)
        
        # زر اختبار إدخال العمر
        test_age_btn = QPushButton("اختبار إدخال العمر 30")
        test_age_btn.clicked.connect(self.test_age_input)
        test_age_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_age_btn)
        
        # زر اختبار الحساب التلقائي
        test_calculation_btn = QPushButton("اختبار الحساب التلقائي")
        test_calculation_btn.clicked.connect(self.test_automatic_calculation)
        test_calculation_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_calculation_btn)
        
        # زر اختبار البيانات الفارغة
        test_empty_btn = QPushButton("اختبار البيانات الفارغة")
        test_empty_btn.clicked.connect(self.test_empty_values)
        test_empty_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(test_empty_btn)
        
        # زر مسح النموذج
        clear_form_btn = QPushButton("مسح النموذج")
        clear_form_btn.clicked.connect(self.clear_form)
        clear_form_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_form_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار حقول سنة الولادة والعمر المحسنة:
        
        🔍 ما يجب ملاحظته:
        • حقل سنة الولادة يظهر فارغاً (بدون أسهم تحكم)
        • حقل العمر يظهر فارغاً (بدون أسهم تحكم)
        • عند إدخال سنة الولادة، يتم حساب العمر تلقائياً
        • عند إدخال العمر، يتم حساب سنة الولادة تلقائياً
        • الحقول تبدأ فارغة ولا تحتوي على قيم افتراضية
        
        🧪 اختبارات يمكن إجراؤها:
        • لاحظ أن حقلي سنة الولادة والعمر فارغان عند فتح البرنامج
        • انقر على "اختبار إدخال سنة الولادة 1985" لتعيين سنة الولادة ومشاهدة حساب العمر
        • انقر على "اختبار إدخال العمر 30" لتعيين العمر ومشاهدة حساب سنة الولادة
        • انقر على "اختبار الحساب التلقائي" لرؤية التفاعل بين الحقلين
        • انقر على "اختبار البيانات الفارغة" لرؤية كيفية التعامل مع القيم الفارغة
        • انقر على "مسح النموذج" لإعادة الحقول للحالة الفارغة
        • جرب إدخال قيم مختلفة يدوياً في الحقول
        
        ✅ النتائج المتوقعة:
        • حقول فارغة عند بداية الاستخدام (بدون أسهم تحكم)
        • إمكانية البدء بالكتابة مباشرة بدون حذف قيم افتراضية
        • حساب تلقائي صحيح بين سنة الولادة والعمر
        • التعامل الصحيح مع القيم الفارغة في جميع العمليات
        • تحسين كبير في تجربة المستخدم وسهولة الاستخدام
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تحسين حقول سنة الولادة والعمر بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_birth_year_input(self):
        """اختبار إدخال سنة الولادة"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            form.birth_year_input.setValue(1985)
            
    def test_age_input(self):
        """اختبار إدخال العمر"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            form.age_input.setValue(30)
            
    def test_automatic_calculation(self):
        """اختبار الحساب التلقائي"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            # تعيين سنة الولادة ومشاهدة حساب العمر
            form.birth_year_input.setValue(1990)
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "الحساب التلقائي",
                f"تم تعيين سنة الولادة: 1990\n"
                f"العمر المحسوب تلقائياً: {form.age_input.value()}"
            )
            
    def test_empty_values(self):
        """اختبار البيانات الفارغة"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            # الحصول على البيانات مع القيم الفارغة
            data = form.get_patient_data()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self,
                "البيانات الفارغة",
                f"سنة الولادة: {data['birth_year']}\n"
                f"(None يعني قيمة فارغة في قاعدة البيانات)"
            )
            
    def clear_form(self):
        """مسح النموذج"""
        if hasattr(self.patients_tab, 'patient_form'):
            self.patients_tab.patient_form.clear_form()

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ImprovedAgeBirthYearFieldsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
