#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التناسق البصري لعناوين الحاويات في واجهة علاج الأسنان
Visual consistency test for container titles in dental treatment interface
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QLabel
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget, TreatmentPlanWidget

class VisualTestWindow(QMainWindow):
    """نافذة اختبار التناسق البصري"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار التناسق البصري - عناوين الحاويات")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار التناسق البصري لعناوين الحاويات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الاختبار
        info_label = QLabel("""
        🎯 الهدف: التحقق من توحيد التنسيق البصري لعناوين الحاويات
        
        ✅ ما يجب ملاحظته:
        • كلا الحاويتين تستخدم نفس تنسيق QGroupBox
        • العناوين مندمجة مع إطار الحاوية بنفس الطريقة
        • نفس الألوان والخطوط والحدود
        • نفس المسافات والهوامش
        • مظهر متسق ومتناغم
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #2c3e50;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_label)
        
        # حاوية للمقارنة الجانبية
        comparison_layout = QHBoxLayout()
        comparison_layout.setSpacing(20)
        
        # الحاوية الأولى - خيارات المعالجة
        options_container = QWidget()
        options_layout = QVBoxLayout(options_container)
        options_layout.setContentsMargins(10, 10, 10, 10)
        
        options_title = QLabel("حاوية خيارات المعالجة")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 8px;
                background-color: #fadbd8;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        options_layout.addWidget(options_title)
        
        self.treatment_options = TreatmentOptionsWidget()
        options_layout.addWidget(self.treatment_options)
        
        comparison_layout.addWidget(options_container)
        
        # الحاوية الثانية - خطة المعالجة
        plan_container = QWidget()
        plan_layout = QVBoxLayout(plan_container)
        plan_layout.setContentsMargins(10, 10, 10, 10)
        
        plan_title = QLabel("حاوية خطة المعالجة السنية")
        plan_title.setAlignment(Qt.AlignCenter)
        plan_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        plan_layout.addWidget(plan_title)
        
        self.treatment_plan = TreatmentPlanWidget()
        plan_layout.addWidget(self.treatment_plan)
        
        comparison_layout.addWidget(plan_container)
        
        layout.addLayout(comparison_layout)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ النتيجة: تم توحيد التنسيق البصري بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار البصري"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = VisualTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
