from setuptools import setup, find_packages

with open('README.md', 'r', encoding='utf-8') as f:
    long_description = f.read()

with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = f.read().splitlines()

setup(
    name="dental_clinic_management",
    version="1.0.0",
    author="Dental Clinic Management System",
    author_email="<EMAIL>",
    description="نظام إدارة العيادة السنية",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/dental-clinic-management",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.7",
    install_requires=requirements,
    entry_points={
        'console_scripts': [
            'dental-clinic=main:main',
        ],
    },
    include_package_data=True,
)