# نظام إدارة العيادة السنية

تطبيق سطح مكتب لإدارة العيادات السنية مكتوب بلغة Python مع واجهة مستخدم عربية بالكامل وتصميم من اليمين إلى اليسار (RTL).

## المميزات

- واجهة مستخدم عربية بالكامل مع دعم RTL
- نظام تسجيل دخول وإدارة مستخدمين
- إدارة بيانات المرضى
- إدارة خطط العلاج والجلسات
- جدولة المواعيد مع عرض يومي/أسبوعي/شهري
- إدارة طلبات المخبر
- تتبع المصروفات
- تقارير شاملة (مرضى، مخبر، عيادة)
- نسخ احتياطي واستعادة البيانات

## متطلبات النظام

- Python 3.7 أو أحدث
- المكتبات المطلوبة (مذكورة في ملف requirements.txt)

## التثبيت

1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المستودع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة:

```
pip install -r requirements.txt
```

## تشغيل التطبيق

لتشغيل التطبيق، قم بتنفيذ الأمر التالي في مجلد المشروع:

```
python main.py
```

## بنية المشروع

```
/
├── main.py                 # نقطة الدخول الرئيسية للتطبيق
├── requirements.txt        # قائمة المكتبات المطلوبة
├── README.md               # ملف التوثيق
├── database/               # وحدة قاعدة البيانات
│   ├── __init__.py
│   └── db_handler.py       # معالج قاعدة البيانات SQLite
├── ui/                     # وحدة واجهة المستخدم
│   ├── __init__.py
│   ├── login.py            # واجهة تسجيل الدخول
│   ├── main_window.py      # النافذة الرئيسية
│   └── tabs/               # تبويبات التطبيق
│       ├── __init__.py
│       ├── patients_tab.py      # تبويبة المرضى
│       ├── treatment_tab.py     # تبويبة المعالجة
│       ├── appointments_tab.py  # تبويبة المواعيد
│       ├── lab_tab.py           # تبويبة المخبر
│       ├── expenses_tab.py      # تبويبة المصروفات
│       ├── reports_tab.py       # تبويبة التقارير
│       └── settings_tab.py      # تبويبة الإعدادات
├── assets/                # الأصول (الصور والأيقونات)
│   ├── __init__.py
│   ├── icons/
│   │   └── __init__.py
│   └── images/
│       └── __init__.py
├── reports/               # قوالب التقارير
│   └── __init__.py
└── backup/                # ملفات النسخ الاحتياطي
    └── __init__.py
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## الترخيص

هذا المشروع مرخص بموجب رخصة MIT. انظر ملف LICENSE للحصول على التفاصيل.