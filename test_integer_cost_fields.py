#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تعديل حقول الكلفة لتعمل مع الأرقام الصحيحة
Test modifying cost fields to work with integer numbers
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, TreatmentSessionDialog

class IntegerCostFieldsTestWindow(QMainWindow):
    """نافذة اختبار حقول الكلفة بالأرقام الصحيحة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تعديل حقول الكلفة لتعمل مع الأرقام الصحيحة")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تعديل حقول الكلفة لتعمل مع الأرقام الصحيحة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 تعديل حقول الكلفة لتعمل مع الأرقام الصحيحة:
        
        ✅ تعديل حقل الكلفة في خطة المعالجة:
        • تغيير نوع الحقل من QDoubleSpinBox إلى QSpinBox لقبول الأرقام الصحيحة فقط
        • حذف الفاصلة العشرية من عرض القيم
        • الاحتفاظ بلاحقة العملة "ل.س" (ليرة سورية)
        • الاحتفاظ بالحد الأقصى للقيمة (999999)
        • الاحتفاظ بإزالة أسهم التحكم (NoButtons)
        
        ✅ تعديل عرض الكلفة في نافذة جلسات المعالجة:
        • تعديل عرض الكلفة لتظهر كرقم صحيح بدون فاصلة عشرية
        • تعديل حقل الدفعة ليقبل أرقام صحيحة فقط (QSpinBox)
        • تعديل حساب المبلغ المتبقي ليعمل مع الأرقام الصحيحة
        • إزالة ".00" من جميع عروض القيم المالية
        
        🔧 التحسينات التقنية المطبقة:
        • استخدام QSpinBox بدلاً من QDoubleSpinBox
        • استخدام int() بدلاً من float في المتغيرات والحسابات
        • تعديل دوال الحفظ والاسترجاع للتعامل مع الأرقام الصحيحة
        • تحديث جميع العمليات الحسابية لتعطي نتائج صحيحة
        
        📊 التعديلات المطبقة:
        • حقل الكلفة في خطة المعالجة: QSpinBox مع setMaximum(999999)
        • حقل الدفعة في جلسات المعالجة: QSpinBox مع setMaximum(999999)
        • عرض الكلفة: f"{int(self.cost)} ليرة سورية"
        • عرض المجموع: f"{total_payments} ليرة سورية"
        • عرض المتبقي: f"{remaining} ليرة سورية"
        • دالة get_plan_data(): int(self.cost_spinbox.value())
        • دالة get_session_data(): int(self.payment_spinbox.value())
        
        ✅ النتائج المحققة:
        • جميع القيم المالية تظهر كأرقام صحيحة بدون فاصلة عشرية
        • إدخال القيم يقبل أرقام صحيحة فقط
        • الحسابات تعمل بشكل صحيح مع الأرقام الصحيحة
        • الحفاظ على جميع الوظائف الأخرى
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع حقول الكلفة المحدثة
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع حقول كلفة بأرقام صحيحة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار كلفة
        test_cost_btn = QPushButton("اختبار كلفة 50000 ل.س")
        test_cost_btn.clicked.connect(self.test_cost_value)
        test_cost_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_cost_btn)
        
        # زر اختبار جلسة معالجة
        test_session_btn = QPushButton("اختبار نافذة جلسة المعالجة")
        test_session_btn.clicked.connect(self.test_session_dialog)
        test_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_session_btn)
        
        # زر مسح القيم
        clear_btn = QPushButton("مسح جميع القيم")
        clear_btn.clicked.connect(self.clear_values)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار حقول الكلفة بالأرقام الصحيحة:
        
        🔍 ما يجب ملاحظته:
        • حقل الكلفة في خطة المعالجة يقبل أرقام صحيحة فقط (بدون فاصلة عشرية)
        • عرض القيم بدون ".00" في النهاية
        • لاحقة "ل.س" محفوظة في جميع الحقول
        • الحد الأقصى للقيمة 999999 (بدون فاصلة عشرية)
        • عدم وجود أسهم تحكم في حقول الكلفة
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على زر "اختبار كلفة 50000 ل.س" لتعيين قيمة في حقل الكلفة
        • جرب إدخال قيم مختلفة في حقل الكلفة (أرقام صحيحة فقط)
        • انقر على زر "اختبار نافذة جلسة المعالجة" لفتح نافذة الجلسات
        • في نافذة الجلسات، لاحظ عرض الكلفة كرقم صحيح
        • جرب إدخال قيم في حقل الدفعة (أرقام صحيحة فقط)
        • لاحظ حساب المبلغ المتبقي بأرقام صحيحة
        
        ✅ النتائج المتوقعة:
        • جميع حقول الكلفة تقبل أرقام صحيحة فقط
        • عرض جميع القيم المالية بدون فاصلة عشرية
        • الحسابات تعمل بشكل صحيح مع الأرقام الصحيحة
        • الحفاظ على جميع الوظائف الأخرى (لاحقة العملة، الحد الأقصى، إلخ)
        • مظهر أنظف وأكثر وضوحاً للقيم المالية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تعديل جميع حقول الكلفة لتعمل مع الأرقام الصحيحة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_cost_value(self):
        """اختبار قيمة الكلفة"""
        # تعيين قيمة كلفة تجريبية
        self.dental_tab.treatment_plan.cost_spinbox.setValue(50000)
        
    def test_session_dialog(self):
        """اختبار نافذة جلسة المعالجة"""
        # إنشاء نافذة جلسة معالجة تجريبية
        dialog = TreatmentSessionDialog(
            plan_number="001",
            cost=75000,  # كلفة تجريبية
            parent=self
        )
        dialog.exec_()
        
    def clear_values(self):
        """مسح جميع القيم"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(0)
        self.dental_tab.treatment_plan.plan_number_edit.clear()
        self.dental_tab.treatment_plan.tooth_number_edit.clear()

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = IntegerCostFieldsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
