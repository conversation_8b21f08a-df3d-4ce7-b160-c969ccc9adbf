#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة القيمة الافتراضية (الصفر) من حقول الكلفة
Test removing default zero value from cost fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, TreatmentSessionDialog

class RemoveDefaultZeroValueTestWindow(QMainWindow):
    """نافذة اختبار إزالة القيمة الافتراضية من حقول الكلفة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إزالة القيمة الافتراضية (الصفر) من حقول الكلفة")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إزالة القيمة الافتراضية (الصفر) من حقول الكلفة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 إزالة القيمة الافتراضية (الصفر) من جميع حقول الكلفة:
        
        ✅ إزالة القيمة الافتراضية من حقل الكلفة في خطة المعالجة:
        • استخدام setSpecialValueText("") لإظهار حقل فارغ عندما تكون القيمة 0
        • تعيين setMinimum(0) للسماح بالقيمة صفر
        • الاحتفاظ بجميع الخصائص الأخرى:
          - نوع الحقل: QSpinBox (أرقام صحيحة)
          - الحد الأقصى: 999,999,999
          - لاحقة العملة: "ل.س"
          - العرض الثابت: 150px
          - إزالة أسهم التحكم: NoButtons
        
        ✅ إزالة القيمة الافتراضية من حقل الدفعة في نافذة جلسات المعالجة:
        • تطبيق نفس الحل: setSpecialValueText("")
        • تعيين setMinimum(0) للسماح بالقيمة صفر
        • الاحتفاظ بجميع الخصائص الأخرى:
          - نوع الحقل: QSpinBox (أرقام صحيحة)
          - الحد الأقصى: 999,999,999
          - لاحقة العملة: "ليرة سورية"
          - العرض الثابت: 150px
          - المحاذاة المركزية: AlignCenter
        
        🔧 التحسينات التقنية المطبقة:
        • تحديث دالة get_plan_data() للتعامل مع القيم الفارغة
        • تحديث دالة get_session_data() للتعامل مع القيم الفارغة
        • تحديث دالة calculate_remaining() للتعامل مع القيم الفارغة
        • التأكد من عدم حدوث أخطاء في الحسابات مع القيم الفارغة
        • الحفاظ على جميع الوظائف الأخرى (التحقق من صحة البيانات، الحد الأقصى)
        
        📊 المقارنة قبل وبعد التحسين:
        • قبل: حقل الكلفة يظهر "0 ل.س" افتراضياً
        • بعد: حقل الكلفة يظهر فارغاً (بدون نص)
        • قبل: حقل الدفعة يظهر "0 ليرة سورية" افتراضياً
        • بعد: حقل الدفعة يظهر فارغاً (بدون نص)
        • النتيجة: المستخدم يبدأ الكتابة مباشرة بدون حاجة لحذف الصفر
        
        ✅ النتائج المحققة:
        • تحسين تجربة المستخدم بشكل كبير
        • إزالة الحاجة لحذف الصفر يدوياً
        • بداية الكتابة مباشرة في حقل فارغ
        • الحفاظ على جميع الوظائف والحسابات
        • التعامل الصحيح مع القيم الفارغة في الكود
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع الحقول الفارغة
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع حقول كلفة فارغة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار إدخال كلفة
        test_cost_btn = QPushButton("اختبار إدخال كلفة 75000")
        test_cost_btn.clicked.connect(self.test_cost_input)
        test_cost_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_cost_btn)
        
        # زر اختبار نافذة الجلسات
        test_session_btn = QPushButton("اختبار نافذة الجلسات")
        test_session_btn.clicked.connect(self.test_session_dialog)
        test_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_session_btn)
        
        # زر اختبار البيانات
        test_data_btn = QPushButton("اختبار الحصول على البيانات")
        test_data_btn.clicked.connect(self.test_get_data)
        test_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_data_btn)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("إعادة تعيين الحقول")
        reset_btn.clicked.connect(self.reset_fields)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(reset_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار إزالة القيمة الافتراضية:
        
        🔍 ما يجب ملاحظته:
        • حقل الكلفة في خطة المعالجة يظهر فارغاً (بدون "0 ل.س")
        • حقل الدفعة في نافذة الجلسات يظهر فارغاً (بدون "0 ليرة سورية")
        • عند النقر على الحقل، يمكن البدء بالكتابة مباشرة
        • عند إدخال قيمة ثم حذفها، يعود الحقل للحالة الفارغة
        • جميع الحسابات تعمل بشكل صحيح مع القيم الفارغة
        
        🧪 اختبارات يمكن إجراؤها:
        • لاحظ أن حقل الكلفة فارغ عند فتح البرنامج
        • انقر على "اختبار إدخال كلفة 75000" لتعيين قيمة
        • انقر على "اختبار نافذة الجلسات" لفتح نافذة الجلسات ولاحظ حقل الدفعة الفارغ
        • انقر على "اختبار الحصول على البيانات" لاختبار دوال البيانات
        • جرب إدخال قيم مختلفة في الحقول يدوياً
        • جرب حذف القيم والعودة للحالة الفارغة
        • انقر على "إعادة تعيين الحقول" لإعادة الحقول للحالة الفارغة
        
        ✅ النتائج المتوقعة:
        • حقول كلفة فارغة عند بداية الاستخدام
        • إمكانية البدء بالكتابة مباشرة بدون حذف الصفر
        • عمل جميع الحسابات بشكل صحيح مع القيم الفارغة
        • عدم حدوث أخطاء عند التعامل مع القيم الفارغة
        • تحسين كبير في تجربة المستخدم
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إزالة القيمة الافتراضية من جميع حقول الكلفة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_cost_input(self):
        """اختبار إدخال كلفة"""
        # تعيين قيمة كلفة لاختبار الإدخال
        self.dental_tab.treatment_plan.cost_spinbox.setValue(75000)
        
    def test_session_dialog(self):
        """اختبار نافذة جلسة المعالجة"""
        # إنشاء نافذة جلسة معالجة لاختبار حقل الدفعة الفارغ
        dialog = TreatmentSessionDialog(
            plan_number="001",
            cost=100000,  # كلفة للاختبار
            parent=self
        )
        dialog.exec_()
        
    def test_get_data(self):
        """اختبار الحصول على البيانات"""
        # اختبار دالة الحصول على بيانات الخطة
        plan_data = self.dental_tab.treatment_plan.get_plan_data()
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(
            self, 
            "بيانات الخطة", 
            f"بيانات خطة المعالجة:\n\n"
            f"رقم الخطة: {plan_data['plan_number']}\n"
            f"رقم السن: {plan_data['tooth_number']}\n"
            f"المعالجة: {plan_data['treatment']}\n"
            f"الكلفة: {plan_data['cost']} ليرة سورية\n"
            f"التاريخ: {plan_data['date']}"
        )
        
    def reset_fields(self):
        """إعادة تعيين الحقول للحالة الفارغة"""
        # إعادة تعيين حقل الكلفة للحالة الفارغة
        self.dental_tab.treatment_plan.cost_spinbox.setValue(0)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = RemoveDefaultZeroValueTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
