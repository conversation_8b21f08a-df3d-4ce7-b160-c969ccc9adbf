from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget)
from PyQt5.QtCore import Qt

# استيراد التبويبات الفرعية
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class TreatmentTab(QWidget):
    """تبويبة المعالجة الرئيسية"""
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.patient_name = ""
        self.patient_id = None
        self.init_ui()

    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # تم إزالة عنصر عرض اسم المريض لتبسيط الواجهة
        # self.patient_label = QLabel(f"المريض: {self.patient_name}")
        # self.patient_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        # main_layout.addWidget(self.patient_label)

        # إنشاء منطقة المحتوى مع التبويبات على اليمين
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # شريط التبويبات الفرعية - تعيين موضع التبويبات على اليمين (East)
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)

        # تطبيق أنماط حديثة تشبه شريط التبويبات الرئيسي
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                margin-left: 0px;
            }

            QTabBar {
                background-color: #1e3a8a;
                border: none;
                border-radius: 8px;
                padding: 5px;
            }

            QTabBar::tab {
                background-color: transparent;
                border: none;
                border-radius: 6px;
                min-height: 45px;
                min-width: 100px; /* Adjusted for horizontal layout */
                padding: 12px 16px;
                margin: 2px 3px; /* Adjusted for horizontal layout */
                color: white;
                font-weight: 600;
                font-size: 13px;
                text-align: center;
                qproperty-alignment: AlignRight | AlignVCenter;
            }

            QTabBar::tab:hover:!selected {
                background-color: rgba(255, 255, 255, 0.15);
            }

            QTabBar::tab:pressed {
                background-color: rgba(255, 255, 255, 0.25);
            }

            QTabBar::tab:selected {
                background-color: white;
                color: #1e3a8a;
                font-weight: 700;
                border: none;
            }

            QTabBar::tab:first {
                margin-left: 5px; /* Adjusted for horizontal layout */
            }

            QTabBar::tab:last {
                margin-right: 5px; /* Adjusted for horizontal layout */
            }
        """)

        # إنشاء التبويبات الفرعية
        self.create_tabs()

        # إضافة التبويبات إلى التخطيط الرئيسي
        content_layout.addWidget(self.tabs)
        main_layout.addLayout(content_layout)

    def create_tabs(self):
        """إنشاء التبويبات الفرعية مع ربط شامل"""
        # تبويب المعالجات السنية مع ربط مباشر
        self.dental_treatments_tab = DentalTreatmentsTab(self.db_handler)

        # ربط مباشر مع التبويبة الرئيسية
        self._setup_dental_treatments_integration()

        self.tabs.addTab(self.dental_treatments_tab, "علاج الأسنان")

        # تبويب الجسور
        bridges = QWidget()
        self.tabs.addTab(bridges, "الجسور")

        # تبويب الفينير
        veneers = QWidget()
        self.tabs.addTab(veneers, "فينير")

        # تبويب التبييض
        whitening = QWidget()
        self.tabs.addTab(whitening, "التبييض")

        # تبويب الأطفال
        children = QWidget()
        self.tabs.addTab(children, "الأطفال")

        # تبويب اللثة
        gums = QWidget()
        self.tabs.addTab(gums, "اللثة")

        # تبويب التقويم
        orthodontics = QWidget()
        self.tabs.addTab(orthodontics, "التقويم")

        # تبويب الزرع
        implants = QWidget()
        self.tabs.addTab(implants, "الزرع")

        # تبويب المتحركة
        removable = QWidget()
        self.tabs.addTab(removable, "المتحركة")

    def _setup_dental_treatments_integration(self):
        """إعداد التكامل الشامل مع تبويبة علاج الأسنان"""
        if hasattr(self.dental_treatments_tab, 'set_parent_treatment_tab'):
            # ربط التبويبة الفرعية مع التبويبة الرئيسية
            self.dental_treatments_tab.set_parent_treatment_tab(self)

        # تعيين المريض الحالي إذا كان موجوداً
        if self.patient_id:
            if hasattr(self.dental_treatments_tab, 'set_current_patient'):
                self.dental_treatments_tab.set_current_patient(self.patient_id)

        # ربط إشارات التحديث
        self._connect_dental_treatments_signals()

    def _connect_dental_treatments_signals(self):
        """ربط إشارات التحديث مع تبويبة علاج الأسنان"""
        # يمكن إضافة ربط إشارات مخصصة هنا إذا لزم الأمر
        pass

    def set_patient_name(self, name):
        """تعيين اسم المريض مع تحديث العرض"""
        self.patient_name = name

        # تم تعطيل تحديث عرض اسم المريض لتبسيط الواجهة
        # تحديث عرض اسم المريض مع تنسيق محسن
        # if name and name != "لم يتم اختيار مريض":
        #     display_text = f"👤 المريض: {self.patient_name}"
        #     self.patient_label.setStyleSheet("""
        #         font-size: 16px;
        #         font-weight: bold;
        #         color: #2c3e50;
        #         background-color: #e8f5e8;
        #         padding: 8px 12px;
        #         border-radius: 6px;
        #         border: 2px solid #27ae60;
        #     """)
        # else:
        #     display_text = "⚠️ لم يتم اختيار مريض"
        #     self.patient_label.setStyleSheet("""
        #         font-size: 16px;
        #         font-weight: bold;
        #         color: #e74c3c;
        #         background-color: #fdf2f2;
        #         padding: 8px 12px;
        #         border-radius: 6px;
        #         border: 2px solid #e74c3c;
        #     """)

        # self.patient_label.setText(display_text)

    def set_patient(self, patient_data):
        """تعيين بيانات المريض وربطه شاملاً مع جميع التبويبات الفرعية - حل جذري محسن"""
        print(f"🔄 TreatmentTab.set_patient استقبل: {patient_data} (نوع: {type(patient_data)})")

        if patient_data:
            # التعامل مع كلا من القاموس والرقم الصحيح
            if isinstance(patient_data, dict):
                # إذا كانت البيانات قاموس (الحالة المفضلة)
                self.patient_id = patient_data.get('id')
                self.patient_name = patient_data.get('name', '')
                processed_data = patient_data
                print(f"✅ معالجة قاموس: patient_id={self.patient_id}, patient_name={self.patient_name}")
            elif isinstance(patient_data, int):
                # إذا كانت البيانات رقم معرف فقط (للتوافق مع الكود القديم)
                self.patient_id = patient_data
                print(f"🔢 معالجة رقم صحيح: patient_id={self.patient_id}")

                # محاولة الحصول على بيانات المريض من قاعدة البيانات
                if hasattr(self, 'db_handler') and self.db_handler:
                    try:
                        full_patient_data = self.db_handler.get_patient(patient_data)
                        if full_patient_data:
                            self.patient_name = full_patient_data.get('name', '')
                            processed_data = full_patient_data
                            print(f"✅ تم الحصول على بيانات كاملة من قاعدة البيانات: {self.patient_name}")
                        else:
                            self.patient_name = f"مريض #{patient_data}"
                            processed_data = {'id': patient_data, 'name': self.patient_name}
                            print(f"⚠️ لم يتم العثور على المريض في قاعدة البيانات، استخدام اسم افتراضي")
                    except Exception as e:
                        print(f"❌ خطأ في الحصول على بيانات المريض: {e}")
                        self.patient_name = f"مريض #{patient_data}"
                        processed_data = {'id': patient_data, 'name': self.patient_name}
                else:
                    self.patient_name = f"مريض #{patient_data}"
                    processed_data = {'id': patient_data, 'name': self.patient_name}
                    print(f"⚠️ قاعدة البيانات غير متاحة، استخدام اسم افتراضي")
            else:
                # نوع بيانات غير مدعوم
                print(f"❌ نوع بيانات غير مدعوم للمريض: {type(patient_data)}")
                return

            # تحديث عرض اسم المريض في التبويبة الرئيسية
            print(f"🏷️ تحديث عرض اسم المريض: {self.patient_name}")
            self.set_patient_name(self.patient_name)

            # ربط شامل مع تبويبة علاج الأسنان الفرعية - الحل الجذري
            print(f"🔗 بدء ربط المريض مع تبويبة علاج الأسنان...")
            self._force_link_patient_to_dental_treatments(processed_data)

        else:
            # إعادة تعيين جميع البيانات عند عدم وجود مريض
            print(f"🔄 مسح بيانات المريض...")
            self.patient_id = None
            self.patient_name = ""
            self.set_patient_name("لم يتم اختيار مريض")

            # إعادة تعيين المريض في جميع التبويبات الفرعية
            self._clear_patient_from_all_tabs()

    def _force_link_patient_to_dental_treatments(self, patient_data):
        """ربط قوي ومضمون للمريض مع تبويبة علاج الأسنان - الحل الجذري"""
        print(f"🔧 _force_link_patient_to_dental_treatments: بدء الربط الجذري...")
        print(f"📋 بيانات المريض المرسلة: {patient_data}")

        # التحقق من وجود تبويبة علاج الأسنان
        if not hasattr(self, 'dental_treatments_tab'):
            print(f"❌ تبويبة علاج الأسنان غير موجودة (dental_treatments_tab)")
            return False

        if not self.dental_treatments_tab:
            print(f"❌ تبويبة علاج الأسنان فارغة (None)")
            return False

        print(f"✅ تبويبة علاج الأسنان موجودة")

        # الحل الجذري: ربط مباشر ومتعدد المستويات
        success_count = 0
        total_attempts = 0

        # المحاولة 1: تعيين current_patient_id مباشرة
        total_attempts += 1
        try:
            self.dental_treatments_tab.current_patient_id = self.patient_id
            print(f"✅ [1/4] تم تعيين current_patient_id مباشرة: {self.patient_id}")
            success_count += 1
        except Exception as e:
            print(f"❌ [1/4] فشل في تعيين current_patient_id مباشرة: {e}")

        # المحاولة 2: استدعاء set_current_patient إذا كانت موجودة
        total_attempts += 1
        try:
            if hasattr(self.dental_treatments_tab, 'set_current_patient'):
                self.dental_treatments_tab.set_current_patient(self.patient_id)
                print(f"✅ [2/4] تم استدعاء set_current_patient: {self.patient_id}")
                success_count += 1
            else:
                print(f"⚠️ [2/4] دالة set_current_patient غير موجودة")
        except Exception as e:
            print(f"❌ [2/4] فشل في استدعاء set_current_patient: {e}")

        # المحاولة 3: تحديث عرض اسم المريض مباشرة (معطل لتبسيط الواجهة)
        total_attempts += 1
        try:
            # تم تعطيل تحديث patient_name_label لتبسيط الواجهة
            # if hasattr(self.dental_treatments_tab, 'patient_name_label') and self.dental_treatments_tab.patient_name_label:
            #     self.dental_treatments_tab.patient_name_label.setText(self.patient_name)
            #     print(f"✅ [3/4] تم تحديث patient_name_label: {self.patient_name}")
            #     success_count += 1
            # else:
            #     print(f"⚠️ [3/4] patient_name_label غير موجود")
            print(f"⚠️ [3/4] تحديث patient_name_label معطل (تبسيط الواجهة)")
        except Exception as e:
            print(f"❌ [3/4] فشل في تحديث patient_name_label: {e}")

        # المحاولة 4: التحقق النهائي من نجاح الربط
        total_attempts += 1
        try:
            final_patient_id = getattr(self.dental_treatments_tab, 'current_patient_id', None)
            if final_patient_id == self.patient_id:
                print(f"✅ [4/4] التحقق النهائي نجح: current_patient_id = {final_patient_id}")
                success_count += 1
            else:
                print(f"❌ [4/4] التحقق النهائي فشل: متوقع {self.patient_id}, موجود {final_patient_id}")
        except Exception as e:
            print(f"❌ [4/4] خطأ في التحقق النهائي: {e}")

        # تقرير النتائج
        success_rate = (success_count / total_attempts) * 100
        print(f"📊 نتائج الربط الجذري: {success_count}/{total_attempts} ({success_rate:.1f}%)")

        if success_count >= 2:  # نحتاج على الأقل تعيين المعرف والتحقق
            print(f"🎉 الربط الجذري نجح! المريض {self.patient_id} مرتبط بتبويبة علاج الأسنان")
            return True
        else:
            print(f"💥 الربط الجذري فشل! المريض {self.patient_id} غير مرتبط بشكل صحيح")
            return False

    def comprehensive_patient_linking_test(self, patient_id):
        """اختبار شامل لربط المريض - للتحقق من الحل الجذري"""
        print(f"\n🧪 بدء الاختبار الشامل لربط المريض: {patient_id}")
        print("=" * 60)

        # المرحلة 1: اختبار تعيين المريض
        print(f"🔄 المرحلة 1: تعيين المريض...")
        self.set_patient(patient_id)

        # المرحلة 2: فحص حالة TreatmentTab
        print(f"🔍 المرحلة 2: فحص حالة TreatmentTab...")
        treatment_success = True

        if self.patient_id == patient_id:
            print(f"✅ TreatmentTab.patient_id صحيح: {self.patient_id}")
        else:
            print(f"❌ TreatmentTab.patient_id خطأ: متوقع {patient_id}, موجود {self.patient_id}")
            treatment_success = False

        if self.patient_name:
            print(f"✅ TreatmentTab.patient_name موجود: {self.patient_name}")
        else:
            print(f"❌ TreatmentTab.patient_name فارغ")
            treatment_success = False

        # المرحلة 3: فحص حالة DentalTreatmentsTab
        print(f"🔍 المرحلة 3: فحص حالة DentalTreatmentsTab...")
        dental_success = True

        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            dental_tab = self.dental_treatments_tab

            # فحص current_patient_id
            dental_patient_id = getattr(dental_tab, 'current_patient_id', None)
            if dental_patient_id == patient_id:
                print(f"✅ DentalTreatmentsTab.current_patient_id صحيح: {dental_patient_id}")
            else:
                print(f"❌ DentalTreatmentsTab.current_patient_id خطأ: متوقع {patient_id}, موجود {dental_patient_id}")
                dental_success = False

            # فحص patient_name_label
            if hasattr(dental_tab, 'patient_name_label') and dental_tab.patient_name_label:
                label_text = dental_tab.patient_name_label.text()
                if label_text and label_text != "لم يتم اختيار مريض":
                    print(f"✅ DentalTreatmentsTab.patient_name_label صحيح: {label_text}")
                else:
                    print(f"❌ DentalTreatmentsTab.patient_name_label خطأ: {label_text}")
                    dental_success = False
            else:
                print(f"⚠️ DentalTreatmentsTab.patient_name_label غير موجود")

        else:
            print(f"❌ DentalTreatmentsTab غير موجود")
            dental_success = False

        # المرحلة 4: اختبار حفظ خطة المعالجة
        print(f"🔍 المرحلة 4: اختبار حفظ خطة المعالجة...")
        save_success = True

        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            if hasattr(self.dental_treatments_tab, 'validate_patient_for_operations'):
                try:
                    is_valid, message = self.dental_treatments_tab.validate_patient_for_operations()
                    if is_valid:
                        print(f"✅ التحقق من صحة المريض نجح: {message}")
                    else:
                        print(f"❌ التحقق من صحة المريض فشل: {message}")
                        save_success = False
                except Exception as e:
                    print(f"❌ خطأ في التحقق من صحة المريض: {e}")
                    save_success = False
            else:
                print(f"⚠️ دالة validate_patient_for_operations غير موجودة")
        else:
            save_success = False

        # النتيجة النهائية
        print("=" * 60)
        overall_success = treatment_success and dental_success and save_success

        if overall_success:
            print(f"🎉 الاختبار الشامل نجح! المريض {patient_id} مرتبط بشكل صحيح")
            print(f"✅ يمكن الآن حفظ خطط المعالجة بدون مشاكل")
        else:
            print(f"💥 الاختبار الشامل فشل! هناك مشاكل في ربط المريض {patient_id}")
            print(f"📊 النتائج:")
            print(f"   TreatmentTab: {'✅' if treatment_success else '❌'}")
            print(f"   DentalTreatmentsTab: {'✅' if dental_success else '❌'}")
            print(f"   حفظ خطة المعالجة: {'✅' if save_success else '❌'}")

        print("=" * 60)
        return overall_success

    def set_patient_by_id(self, patient_id):
        """تعيين المريض بالمعرف فقط (للتوافق مع الكود القديم)"""
        if patient_id:
            # محاولة الحصول على بيانات المريض الكاملة
            if hasattr(self, 'db_handler') and self.db_handler:
                patient_data = self.db_handler.get_patient(patient_id)
                if patient_data:
                    self.set_patient(patient_data)
                else:
                    # إنشاء بيانات أساسية إذا لم توجد في قاعدة البيانات
                    basic_data = {'id': patient_id, 'name': f"مريض #{patient_id}"}
                    self.set_patient(basic_data)
            else:
                # إنشاء بيانات أساسية بدون قاعدة بيانات
                basic_data = {'id': patient_id, 'name': f"مريض #{patient_id}"}
                self.set_patient(basic_data)
        else:
            self.set_patient(None)

    def _link_patient_to_dental_treatments(self, patient_data):
        """ربط المريض مع تبويبة علاج الأسنان وجميع عملياتها"""
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            # تعيين المريض الحالي في تبويبة علاج الأسنان
            if hasattr(self.dental_treatments_tab, 'set_current_patient'):
                self.dental_treatments_tab.set_current_patient(self.patient_id)
                print(f"🔗 تم ربط المريض {self.patient_id} مع تبويبة علاج الأسنان")

            # تحديث عرض اسم المريض في تبويبة علاج الأسنان
            if hasattr(self.dental_treatments_tab, 'update_patient_display'):
                self.dental_treatments_tab.update_patient_display(patient_data)

            # تحديث جميع النوافذ والعمليات المرتبطة
            if hasattr(self.dental_treatments_tab, 'refresh_all_data'):
                self.dental_treatments_tab.refresh_all_data()

            # التحقق من نجاح الربط
            if hasattr(self.dental_treatments_tab, 'current_patient_id'):
                if self.dental_treatments_tab.current_patient_id == self.patient_id:
                    print(f"✅ تم التحقق من ربط المريض بنجاح: {self.patient_id}")
                else:
                    print(f"❌ فشل في ربط المريض: متوقع {self.patient_id}, موجود {self.dental_treatments_tab.current_patient_id}")
            else:
                print("⚠️ تبويبة علاج الأسنان لا تحتوي على current_patient_id")

    def _clear_patient_from_all_tabs(self):
        """مسح بيانات المريض من جميع التبويبات الفرعية"""
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            # إعادة تعيين المريض في تبويبة علاج الأسنان
            if hasattr(self.dental_treatments_tab, 'set_current_patient'):
                self.dental_treatments_tab.set_current_patient(None)

            # مسح جميع البيانات المعروضة
            if hasattr(self.dental_treatments_tab, 'clear_all_data'):
                self.dental_treatments_tab.clear_all_data()

    def get_current_patient_info(self):
        """الحصول على معلومات المريض الحالي"""
        return {
            'id': self.patient_id,
            'name': self.patient_name,
            'is_selected': self.patient_id is not None
        }

    def validate_patient_selection(self):
        """التحقق من صحة اختيار المريض"""
        if not self.patient_id:
            return False, "يرجى اختيار مريض من قائمة المرضى أولاً"
        return True, "تم اختيار المريض بنجاح"

    def update_all_patient_displays(self):
        """تحديث جميع عروض المريض في التبويبات الفرعية"""
        patient_info = self.get_current_patient_info()

        # تحديث تبويبة علاج الأسنان
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            if hasattr(self.dental_treatments_tab, 'update_patient_display'):
                self.dental_treatments_tab.update_patient_display(patient_info)

    def sync_patient_state(self):
        """مزامنة حالة المريض بين جميع التبويبات"""
        # مزامنة مع تبويبة علاج الأسنان
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            # تعيين معرف المريض
            if hasattr(self.dental_treatments_tab, 'current_patient_id'):
                self.dental_treatments_tab.current_patient_id = self.patient_id

            # تحديث عرض اسم المريض (معطل لتبسيط الواجهة)
            # if hasattr(self.dental_treatments_tab, 'patient_name_label'):
            #     if self.patient_name:
            #         self.dental_treatments_tab.patient_name_label.setText(self.patient_name)
            #     else:
            #         self.dental_treatments_tab.patient_name_label.setText("لم يتم اختيار مريض")

    def check_integration_status(self):
        """فحص حالة التكامل مع التبويبات الفرعية"""
        status = {
            'dental_treatments_tab_exists': hasattr(self, 'dental_treatments_tab'),
            'dental_treatments_tab_ready': False,
            'patient_linked': False,
            'current_patient_id': self.patient_id,
            'current_patient_name': self.patient_name
        }

        if status['dental_treatments_tab_exists']:
            status['dental_treatments_tab_ready'] = self.dental_treatments_tab is not None

            if status['dental_treatments_tab_ready']:
                # فحص ربط المريض
                if hasattr(self.dental_treatments_tab, 'current_patient_id'):
                    status['patient_linked'] = (
                        self.dental_treatments_tab.current_patient_id == self.patient_id
                    )

        return status

    def force_sync_all_tabs(self):
        """فرض مزامنة جميع التبويبات مع المريض الحالي"""
        if self.patient_id:
            # إعادة تعيين المريض لضمان المزامنة
            patient_data = {
                'id': self.patient_id,
                'name': self.patient_name
            }
            self._link_patient_to_dental_treatments(patient_data)
        else:
            # مسح جميع البيانات
            self._clear_patient_from_all_tabs()

        # تحديث العرض
        self.update_all_patient_displays()
        self.sync_patient_state()

    def debug_full_patient_linking(self):
        """تشخيص شامل لربط المريض"""
        print("🔍 تشخيص شامل لربط المريض:")
        print(f"   TreatmentTab - patient_id: {self.patient_id}")
        print(f"   TreatmentTab - patient_name: {self.patient_name}")

        # فحص تبويبة علاج الأسنان
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            print("   DentalTreatmentsTab موجودة")
            if hasattr(self.dental_treatments_tab, 'current_patient_id'):
                print(f"   DentalTreatmentsTab - current_patient_id: {self.dental_treatments_tab.current_patient_id}")
            else:
                print("   ❌ DentalTreatmentsTab لا تحتوي على current_patient_id")

            if hasattr(self.dental_treatments_tab, 'debug_patient_linking'):
                self.dental_treatments_tab.debug_patient_linking()
        else:
            print("   ❌ DentalTreatmentsTab غير موجودة")

    def test_patient_linking(self, patient_id):
        """اختبار ربط المريض"""
        print(f"🧪 اختبار ربط المريض: {patient_id}")

        # محاكاة بيانات المريض
        test_patient_data = {
            'id': patient_id,
            'name': f'مريض اختبار #{patient_id}'
        }

        # تعيين المريض
        self.set_patient(test_patient_data)

        # فحص النتيجة
        self.debug_full_patient_linking()

        # اختبار حفظ خطة المعالجة
        if hasattr(self, 'dental_treatments_tab') and self.dental_treatments_tab:
            if hasattr(self.dental_treatments_tab, 'validate_patient_for_operations'):
                is_valid, message = self.dental_treatments_tab.validate_patient_for_operations()
                print(f"   نتيجة التحقق: {is_valid} - {message}")

        return self.patient_id == patient_id
