#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد العرض الأفقي بين العناوين وحقول الإدخال
Test unified horizontal width alignment between labels and input fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class UnifiedWidthAlignmentTestWindow(QMainWindow):
    """نافذة اختبار توحيد العرض الأفقي"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار توحيد العرض الأفقي بين العناوين وحقول الإدخال")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار توحيد العرض الأفقي بين العناوين وحقول الإدخال")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 توحيد العرض الأفقي بين العناوين وحقول الإدخال:
        
        📏 تحليل العرض الحالي:
        • فحص العرض الحالي لعنوان "رقم السن" (tooth_label)
        • فحص العرض الحالي لحقل إدخال رقم السن (self.tooth_number_edit)
        • فحص العرض الحالي لعنوان "رقم الخطة" (plan_label)
        • فحص العرض الحالي لحقل إدخال رقم الخطة (self.plan_number_edit)
        
        ✅ توحيد العرض الأفقي:
        • حقل إدخال رقم السن له نفس العرض الأفقي تماماً لعنوان "رقم السن" (80px)
        • حقل إدخال رقم الخطة له نفس العرض الأفقي تماماً لعنوان "رقم الخطة" (100px)
        • العنصران (العنوان والحقل) لهما نفس العرض بالبكسل في كل مجموعة
        • تم تطبيق setFixedWidth() على كل من العناوين والحقول
        
        🔧 التحسينات المطبقة:
        • رقم السن: العنوان والحقل كلاهما 80px
        • رقم الخطة: العنوان والحقل كلاهما 100px
        • تطابق كامل في العرض الأفقي لكل مجموعة
        • الحفاظ على جميع الخصائص الأخرى (القراءة فقط، المحاذاة المركزية، النص التوضيحي)
        
        ⚙️ الحفاظ على التناسق:
        • التعديل لا يؤثر على الحقول الأخرى (المعالجة، الكلفة، التاريخ)
        • الاحتفاظ بجميع الخصائص الأخرى لحقل رقم السن:
          - القراءة فقط: setReadOnly(True)
          - المحاذاة المركزية: setAlignment(Qt.AlignCenter)
          - النص التوضيحي: setPlaceholderText("السن")
        • الاحتفاظ بالتنسيق البصري الحالي (الألوان، الخطوط، الحدود)
        
        ✅ النتائج المحققة:
        • تطابق كامل في العرض الأفقي بين عنوان "رقم السن" وحقل الإدخال
        • تطابق كامل في العرض الأفقي بين عنوان "رقم الخطة" وحقل الإدخال
        • التخطيط العام لقسم خطة المعالجة لم يتأثر سلبياً
        • مظهر متناسق ومتوازن للواجهة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع العرض الموحد
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع عرض موحد للعناوين والحقول)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار تحديد سن
        test_tooth_btn = QPushButton("اختبار تحديد السن رقم 11")
        test_tooth_btn.clicked.connect(self.test_tooth_selection)
        test_tooth_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_tooth_btn)
        
        # زر اختبار رقم خطة
        test_plan_btn = QPushButton("اختبار رقم خطة 001")
        test_plan_btn.clicked.connect(self.test_plan_number)
        test_plan_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_plan_btn)
        
        # زر مسح الحقول
        clear_btn = QPushButton("مسح جميع الحقول")
        clear_btn.clicked.connect(self.clear_fields)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار توحيد العرض الأفقي:
        
        🔍 ما يجب ملاحظته:
        • عنوان "رقم السن" وحقل إدخال رقم السن لهما نفس العرض الأفقي بالضبط (80px)
        • عنوان "رقم الخطة" وحقل إدخال رقم الخطة لهما نفس العرض الأفقي بالضبط (100px)
        • العناصر في كل مجموعة متطابقة تماماً في العرض
        • المحاذاة المركزية محفوظة لجميع العناصر
        • التخطيط العام متوازن ومتناسق
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على زر "اختبار تحديد السن رقم 11" لرؤية رقم السن في الحقل
        • انقر على زر "اختبار رقم خطة 001" لرؤية رقم الخطة في الحقل
        • انقر على زر "مسح جميع الحقول" لمسح الحقول
        • انقر على أسنان مختلفة في المخطط التفاعلي
        • لاحظ التطابق الكامل في العرض بين العناوين والحقول
        • تحقق من أن المحاذاة المركزية تعمل بشكل صحيح
        
        ✅ النتائج المتوقعة:
        • تطابق كامل في العرض الأفقي بين عنوان "رقم السن" وحقل الإدخال
        • تطابق كامل في العرض الأفقي بين عنوان "رقم الخطة" وحقل الإدخال
        • مظهر متوازن ومتناسق للواجهة
        • عمل جميع الوظائف الأصلية بشكل صحيح
        • تحسين المظهر العام لقسم خطة المعالجة
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم توحيد العرض الأفقي بين العناوين وحقول الإدخال بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_tooth_selection(self):
        """اختبار تحديد السن رقم 11"""
        # محاكاة تحديد السن رقم 11
        self.dental_tab.treatment_plan.tooth_number_edit.setText("11")
        
    def test_plan_number(self):
        """اختبار رقم خطة 001"""
        # محاكاة إدخال رقم خطة
        self.dental_tab.treatment_plan.plan_number_edit.setText("001")
        
    def clear_fields(self):
        """مسح جميع الحقول"""
        self.dental_tab.treatment_plan.tooth_number_edit.clear()
        self.dental_tab.treatment_plan.plan_number_edit.clear()

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UnifiedWidthAlignmentTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
