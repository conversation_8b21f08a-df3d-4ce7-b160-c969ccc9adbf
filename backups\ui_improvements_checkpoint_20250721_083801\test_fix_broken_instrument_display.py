#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة عرض خيار "أداة مكسورة" في مجموعة اللبية
Test fixing display issue for "أداة مكسورة" option in Endodontic group
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class FixBrokenInstrumentDisplayTestWindow(QMainWindow):
    """نافذة اختبار إصلاح مشكلة عرض خيار أداة مكسورة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إصلاح مشكلة عرض خيار أداة مكسورة")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إصلاح مشكلة عرض خيار أداة مكسورة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الإصلاح
        info_label = QLabel("""
        🎯 إصلاح مشكلة عرض خيار "أداة مكسورة" في مجموعة اللبية:
        
        🔍 فحص مشكلة العرض:
        • تحقق من أن نص "أداة مكسورة" يظهر بالكامل داخل مربع الاختيار
        • التأكد من أن النص لا يتم قطعه أو إخفاؤه جزئياً
        • فحص ما إذا كان العرض السابق (min-width: 140px) كافي لعرض النص بالكامل
        
        ✅ تحسين عرض النص:
        • زيادة العرض الأدنى من 140px إلى 160px (+20px)
        • زيادة الارتفاع الأدنى من 22px إلى 24px (+2px)
        • زيادة الحشو من 3px إلى 4px (+1px) لضمان ظهور النص بوضوح
        • ضمان ظهور جميع النصوص في مجموعة اللبية بالكامل
        
        📏 التحسينات المطبقة على مجموعة اللبية:
        • العرض الأدنى: min-width: 160px (بدلاً من 140px)
        • الارتفاع الأدنى: min-height: 24px (بدلاً من 22px)
        • الحشو: padding: 4px (بدلاً من 3px)
        • حجم الخط: font-size: 14px (محفوظ)
        • المحاذاة: text-align: left (محفوظ)
        
        🎨 الحفاظ على التناسق:
        • تطبيق نفس التحسين على جميع مربعات الاختيار في مجموعة اللبية
        • عدم التأثير على المجموعات الأخرى (الترميمية، التيجان، الجراحة)
        • الحفاظ على التخطيط الشبكي 2x2 للمجموعات الأربعة
        • الحفاظ على جميع الوظائف والتفاعل
        
        ✅ النتائج المحققة:
        • ظهور خيار "أداة مكسورة" بالكامل ووضوح
        • ظهور جميع خيارات مجموعة اللبية بالكامل
        • نص واضح ومقروء بالكامل
        • تفاعل صحيح مع جميع مربعات الاختيار
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة خيارات المعالجة مع الإصلاح
        options_title = QLabel("⚙️ خيارات المعالجة (مع إصلاح عرض أداة مكسورة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setAlignment(Qt.AlignCenter)
        self.selected_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                padding: 12px;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد خيار أداة مكسورة
        broken_instrument_btn = QPushButton("تحديد أداة مكسورة")
        broken_instrument_btn.clicked.connect(self.select_broken_instrument)
        broken_instrument_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(broken_instrument_btn)
        
        # زر تحديد جميع خيارات اللبية
        endodontic_btn = QPushButton("تحديد جميع خيارات اللبية")
        endodontic_btn.clicked.connect(self.select_all_endodontic)
        endodontic_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        buttons_layout.addWidget(endodontic_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار إصلاح عرض أداة مكسورة:
        
        🔍 ما يجب ملاحظته:
        • خيار "أداة مكسورة" في مجموعة اللبية يظهر بالكامل
        • النص واضح ومقروء بالكامل دون قطع أو إخفاء
        • جميع خيارات مجموعة اللبية تظهر بوضوح:
          - Vital, Necrotic, إعادة معالجة, متكلسة
          - منحنية بشدة, C shape, ذروة مفتوحة, أداة مكسورة
        • العرض المحسن (160px) والارتفاع المحسن (24px)
        • الحشو المحسن (4px) يوفر مساحة كافية للنص
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على زر "تحديد أداة مكسورة" لتحديد هذا الخيار تحديداً
        • انقر على زر "تحديد جميع خيارات اللبية" لتحديد جميع الخيارات
        • انقر مباشرة على مربع اختيار "أداة مكسورة" للتأكد من عمله
        • تحقق من أن النص يظهر بالكامل ووضوح
        • قارن مع المجموعات الأخرى للتأكد من التناسق
        
        ✅ النتائج المتوقعة:
        • ظهور خيار "أداة مكسورة" بالكامل ووضوح
        • عمل التفاعل مع مربع الاختيار بشكل صحيح
        • ظهور جميع خيارات مجموعة اللبية بوضوح
        • الحفاظ على التناسق مع باقي المجموعات
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إصلاح مشكلة عرض خيار أداة مكسورة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_broken_instrument(self):
        """تحديد خيار أداة مكسورة"""
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد خيار أداة مكسورة
        checkboxes = self.treatment_options.checkboxes
        if "endodontic_أداة مكسورة" in checkboxes:
            checkboxes["endodontic_أداة مكسورة"].setChecked(True)
        
    def select_all_endodontic(self):
        """تحديد جميع خيارات مجموعة اللبية"""
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد جميع خيارات اللبية
        checkboxes = self.treatment_options.checkboxes
        endodontic_options = [key for key in checkboxes.keys() if key.startswith("endodontic_")]
        
        for option_key in endodontic_options:
            checkboxes[option_key].setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = FixBrokenInstrumentDisplayTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
