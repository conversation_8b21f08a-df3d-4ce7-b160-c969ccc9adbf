/* تصميم حديث يشبه تطبيقات الويب العصرية */
/* مستوحى من Material-UI و Ant Design و Bootstrap 5 */

/* متغيرات الألوان الحديثة */
:root {
    /* الألوان الأساسية */
    --primary-50: #e3f2fd;
    --primary-100: #bbdefb;
    --primary-200: #90caf9;
    --primary-300: #64b5f6;
    --primary-400: #42a5f5;
    --primary-500: #2196f3;
    --primary-600: #1e88e5;
    --primary-700: #1976d2;
    --primary-800: #1565c0;
    --primary-900: #0d47a1;
    
    /* الألوان الثانوية */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #eeeeee;
    --gray-300: #e0e0e0;
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: #757575;
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;
    
    /* ألوان الحالة */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    /* ألوان النص */
    --text-primary: #212121;
    --text-secondary: #757575;
    --text-disabled: #bdbdbd;
    
    /* الخلفيات */
    --bg-default: #fafafa;
    --bg-paper: #ffffff;
    --bg-level1: #f5f5f5;
    --bg-level2: #eeeeee;
    
    /* الحدود والظلال */
    --border-color: #e0e0e0;
    --divider: #e0e0e0;
    --shadow-1: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-2: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    --shadow-3: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
    --shadow-4: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
    --shadow-5: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
    
    /* نصف القطر */
    --radius-xs: 2px;
    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-2xl: 16px;
    
    /* المسافات */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
}

/* الخط الأساسي */
* {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* النافذة الرئيسية */
QMainWindow {
    background-color: var(--bg-default);
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
}

/* شريط العنوان الحديث */
QFrame[objectName="titleBar"] {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    border: none;
    border-bottom: 1px solid var(--primary-800);
    padding: 0;
    min-height: 64px;
    max-height: 64px;
}

QFrame[objectName="titleBar"] QLabel {
    color: white;
    font-size: 20px;
    font-weight: 600;
    letter-spacing: -0.025em;
    margin: 0;
    padding: 0 var(--spacing-lg);
}

QFrame[objectName="titleBar"] QPushButton {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    font-size: 14px;
    min-width: 80px;
    margin: 0 var(--spacing-xs);
    transition: all 0.2s ease;
}

QFrame[objectName="titleBar"] QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: var(--shadow-1);
}

QFrame[objectName="titleBar"] QPushButton:pressed {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(1px);
}

/* التبويبات الحديثة */
QTabWidget {
    background-color: transparent;
    border: none;
    margin: 0;
}

QTabWidget::pane {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-top: 0;
    box-shadow: var(--shadow-1);
}

QTabBar {
    background-color: transparent;
    border: none;
    margin: 0;
    padding: 0 var(--spacing-md);
}

QTabBar::tab {
    background-color: transparent;
    border: none;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    padding: var(--spacing-md) var(--spacing-lg);
    margin: 0 var(--spacing-xs);
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 14px;
    min-width: 120px;
    text-align: center;
    transition: all 0.2s ease;
    position: relative;
}

QTabBar::tab:selected {
    background-color: var(--bg-paper);
    color: var(--primary-600);
    font-weight: 600;
    box-shadow: var(--shadow-1);
    border: 1px solid var(--border-color);
    border-bottom: 1px solid var(--bg-paper);
    margin-top: -1px;
}

QTabBar::tab:hover:!selected {
    background-color: var(--primary-50);
    color: var(--primary-700);
}

QTabBar::tab:first {
    margin-left: 0;
}

QTabBar::tab:last {
    margin-right: 0;
}

/* الأزرار الحديثة */
QPushButton {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
    min-height: 36px;
    min-width: 64px;
    transition: all 0.2s ease;
    cursor: pointer;
}

QPushButton:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-300);
    box-shadow: var(--shadow-1);
    transform: translateY(-1px);
}

QPushButton:pressed {
    background-color: var(--gray-100);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
    transform: translateY(0);
}

QPushButton:disabled {
    background-color: var(--gray-100);
    color: var(--text-disabled);
    border-color: var(--gray-200);
    cursor: not-allowed;
}

/* أزرار ملونة */
QPushButton[class="primary"] {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    border: 1px solid var(--primary-600);
    font-weight: 600;
}

QPushButton[class="primary"]:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    border-color: var(--primary-700);
}

QPushButton[class="success"] {
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    border: 1px solid #45a049;
    font-weight: 600;
}

QPushButton[class="success"]:hover {
    background: linear-gradient(135deg, #45a049 0%, #3e8e41 100%);
    border-color: #3e8e41;
}

QPushButton[class="warning"] {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    border: 1px solid #f57c00;
    font-weight: 600;
}

QPushButton[class="warning"]:hover {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%);
    border-color: #ef6c00;
}

QPushButton[class="error"] {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border: 1px solid #d32f2f;
    font-weight: 600;
}

QPushButton[class="error"]:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
    border-color: #c62828;
}

/* أزرار مسطحة */
QPushButton[class="flat"] {
    background-color: transparent;
    border: 1px solid transparent;
    color: var(--primary-600);
}

QPushButton[class="flat"]:hover {
    background-color: var(--primary-50);
    border-color: transparent;
}

/* حقول الإدخال الحديثة */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 14px;
    min-height: 20px;
    transition: all 0.2s ease;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: var(--primary-500);
    background-color: var(--bg-paper);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    outline: none;
}

QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {
    border-color: var(--gray-400);
}

/* SpinBox الحديث */
QSpinBox, QDoubleSpinBox {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 14px;
    min-height: 20px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: var(--gray-100);
    border: none;
    border-radius: var(--radius-sm);
    width: 20px;
    margin: 2px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: var(--gray-100);
    border: none;
    border-radius: var(--radius-sm);
    width: 20px;
    margin: 2px;
}

/* القوائم المنسدلة الحديثة */
QComboBox {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 14px;
    min-height: 20px;
    padding-right: 30px;
}

QComboBox:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

QComboBox::drop-down {
    border: none;
    width: 30px;
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

QComboBox::down-arrow {
    image: none;
    border: 2px solid var(--text-secondary);
    border-top: none;
    border-right: none;
    width: 6px;
    height: 6px;
    transform: rotate(-45deg);
    margin-top: -3px;
}

QComboBox QAbstractItemView {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-2);
    selection-background-color: var(--primary-50);
    selection-color: var(--primary-700);
    padding: var(--spacing-xs);
}

/* الجداول الحديثة */
QTableWidget {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    gridline-color: var(--divider);
    selection-background-color: var(--primary-50);
    selection-color: var(--primary-700);
    font-size: 14px;
}

QTableWidget::item {
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--divider);
    border-right: none;
    border-left: none;
    border-top: none;
}

QTableWidget::item:selected {
    background-color: var(--primary-50);
    color: var(--primary-800);
}

QTableWidget::item:hover {
    background-color: var(--gray-50);
}

QHeaderView::section {
    background: linear-gradient(to bottom, var(--gray-50) 0%, var(--gray-100) 100%);
    border: none;
    border-bottom: 2px solid var(--primary-500);
    border-right: 1px solid var(--divider);
    padding: var(--spacing-md);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
    text-align: right;
}

QHeaderView::section:first {
    border-top-right-radius: var(--radius-lg);
}

QHeaderView::section:last {
    border-top-left-radius: var(--radius-lg);
    border-right: none;
}

/* البطاقات الحديثة */
QFrame[class="card"] {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    padding: var(--spacing-lg);
    margin: var(--spacing-md);
    box-shadow: var(--shadow-1);
}

QFrame[class="card-header"] {
    background: linear-gradient(to bottom, var(--gray-50) 0%, var(--gray-100) 100%);
    border: none;
    border-bottom: 1px solid var(--divider);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
    margin: calc(-1 * var(--spacing-lg)) calc(-1 * var(--spacing-lg)) var(--spacing-md) calc(-1 * var(--spacing-lg));
}

/* مجموعات العناصر */
QGroupBox {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-md);
    font-weight: 600;
    font-size: 14px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 var(--spacing-sm);
    color: var(--primary-600);
    font-weight: 600;
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    margin-top: calc(-1 * var(--spacing-sm));
}

/* أشرطة التمرير الحديثة */
QScrollBar:vertical {
    background-color: var(--gray-100);
    width: 8px;
    border-radius: 4px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background-color: var(--gray-400);
    border-radius: 4px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: var(--gray-500);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: var(--gray-100);
    height: 8px;
    border-radius: 4px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background-color: var(--gray-400);
    border-radius: 4px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: var(--gray-500);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* شريط الحالة الحديث */
QStatusBar {
    background: linear-gradient(to bottom, var(--gray-50) 0%, var(--gray-100) 100%);
    border-top: 1px solid var(--divider);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
}

/* التحسينات التفاعلية */
QWidget:focus {
    outline: none;
}

/* تحسينات خاصة للنص العربي */
QLabel {
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
}

QLabel[class="title"] {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

QLabel[class="subtitle"] {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

QLabel[class="caption"] {
    font-size: 12px;
    color: var(--text-secondary);
}

/* تحسينات خاصة للقوائم */
QListWidget {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    font-size: 14px;
}

QListWidget::item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    margin: 2px;
}

QListWidget::item:selected {
    background-color: var(--primary-50);
    color: var(--primary-700);
}

QListWidget::item:hover {
    background-color: var(--gray-50);
}

/* تحسينات خاصة للتاريخ والوقت */
QDateEdit, QTimeEdit, QDateTimeEdit {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    font-size: 14px;
    min-height: 20px;
}

QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* تحسينات إضافية للمظهر الاحترافي */
QToolTip {
    background-color: var(--gray-800);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 12px;
    box-shadow: var(--shadow-2);
}

QSplitter::handle {
    background-color: var(--divider);
    width: 1px;
    height: 1px;
}

QSplitter::handle:hover {
    background-color: var(--primary-500);
}

/* تحسينات للتطبيق كاملاً */
QMainWindow::separator {
    background-color: var(--divider);
    width: 1px;
    height: 1px;
}

QMenuBar {
    background-color: var(--bg-paper);
    border-bottom: 1px solid var(--divider);
    color: var(--text-primary);
    font-size: 14px;
    padding: var(--spacing-xs) 0;
}

QMenuBar::item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-xs);
}

QMenuBar::item:selected {
    background-color: var(--primary-50);
    color: var(--primary-700);
}

QMenu {
    background-color: var(--bg-paper);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xs);
    box-shadow: var(--shadow-2);
}

QMenu::item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    margin: 2px;
}

QMenu::item:selected {
    background-color: var(--primary-50);
    color: var(--primary-700);
}