#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النافذة الجديدة لإدارة الأسعار
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة الجديدة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class TestNewWindow(QMainWindow):
    """نافذة اختبار النافذة الجديدة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار النافذة الجديدة لإدارة الأسعار")
        self.setGeometry(200, 200, 700, 500)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار النافذة الجديدة لإدارة أسعار وأسماء المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النافذة الجديدة
        description = QLabel("""
النافذة الجديدة المطورة:
✅ 8 مجموعات مرتبة أفقياً (مطابقة للأصل)
✅ 4 مجموعات أسماء + 4 مجموعات أسعار
✅ 32 معالجة قابلة للتعديل (أسماء وأسعار)
✅ واجهة عربية RTL كاملة
✅ حفظ دائم في JSON
✅ أزرار تحكم: حفظ، استعادة، إلغاء
✅ تصميم مطابق للواجهة الأصلية
✅ منطقة تمرير للمحتوى
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #007bff;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # زر فتح النافذة الجديدة
        open_btn = QPushButton("🚀 فتح النافذة الجديدة (8 مجموعات)")
        open_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #004085, #002752);
            }
        """)
        open_btn.clicked.connect(self.open_new_dialog)
        layout.addWidget(open_btn)
        
        # معلومات إضافية
        info = QLabel("النافذة الآن تحتوي على 8 مجموعات مرتبة أفقياً مع إمكانية تعديل جميع الأسماء والأسعار")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 6px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(info)
        
        # معلومات تقنية
        tech_info = QLabel("""
المعلومات التقنية:
• تم حذف الكلاس القديم بالكامل
• تم إنشاء كلاس جديد من الصفر
• 8 مجموعات: لبية، أسعار لبية، ترميمية، أسعار ترميمية، تيجان، أسعار تيجان، جراحة، أسعار جراحة
• كل مجموعة تحتوي على 8 عناصر قابلة للتعديل
• حفظ دائم في ملف JSON
        """)
        tech_info.setAlignment(Qt.AlignRight)
        tech_info.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #495057;
                padding: 10px;
                background-color: #f1f3f4;
                border-radius: 6px;
                margin-top: 10px;
                border: 1px solid #dee2e6;
            }
        """)
        layout.addWidget(tech_info)
        
    def open_new_dialog(self):
        """فتح النافذة الجديدة"""
        try:
            print("🚀 فتح النافذة الجديدة...")
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            print(f"📊 عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
            print(f"📊 عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
            
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("✅ تم حفظ التغييرات")
            else:
                print("❌ تم إلغاء العملية")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestNewWindow()
    window.show()
    
    print("تم تشغيل نافذة اختبار النافذة الجديدة")
    print("اضغط على الزر لاختبار النافذة الجديدة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
