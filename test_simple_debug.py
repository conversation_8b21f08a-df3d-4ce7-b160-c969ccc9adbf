#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مبسط للنافذة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    try:
        print("=" * 50)
        print("🚀 بدء اختبار النافذة...")
        print("=" * 50)
        
        # استيراد النافذة
        from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog
        
        print("✅ تم استيراد الكلاس بنجاح")
        
        # إنشاء النافذة
        dialog = ComprehensiveTreatmentPricingDialog()
        
        print("✅ تم إنشاء النافذة بنجاح")
        print(f"📊 عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
        print(f"📊 عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
        
        # عرض النافذة
        dialog.show()
        
        print("✅ تم عرض النافذة")
        print("=" * 50)
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
