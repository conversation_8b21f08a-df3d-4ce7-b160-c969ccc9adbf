#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التخطيط الجديد بدون إطار التمرير
Test new layout without scroll area
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class NoScrollTestWindow(QMainWindow):
    """نافذة اختبار التخطيط بدون إطار التمرير"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار التخطيط الجديد - بدون إطار التمرير")
        self.setGeometry(100, 100, 1000, 700)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار التخطيط الجديد - حذف إطار التمرير")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات الاختبار
        info_label = QLabel("""
        🎯 التحسينات المطبقة:
        
        ✅ حذف إطار التمرير (QScrollArea) بالكامل
        ✅ وضع المجموعات الأربعة مباشرة في QGroupBox الرئيسي
        ✅ استخدام QGridLayout مباشرة بدون حاوية وسطية
        ✅ زيادة ارتفاع المجموعات إلى 250px لعرض جميع الخيارات
        ✅ تحسين المسافات والهوامش لاستغلال أفضل للمساحة
        
        📋 ما يجب ملاحظته:
        • جميع مربعات الاختيار مرئية بدون حاجة للتمرير
        • المجموعات الأربعة تظهر في شبكة 2x2 منتظمة
        • استغلال كامل للمساحة المتاحة
        • عدم وجود أشرطة تمرير
        • التخطيط المتجاوب يعمل بشكل صحيح
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #2c3e50;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة واجهة خيارات المعالجة المحسنة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 10px;
                border-radius: 5px;
                font-size: 11px;
                color: #2980b9;
                border: 1px solid #3498db;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد عشوائي
        from PyQt5.QtWidgets import QPushButton
        import random
        
        random_btn = QPushButton("تحديد خيارات عشوائية")
        random_btn.clicked.connect(self.select_random_options)
        random_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(random_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
    def select_random_options(self):
        """تحديد خيارات عشوائية للاختبار"""
        import random
        checkboxes = list(self.treatment_options.checkboxes.values())
        # تحديد 3-6 خيارات عشوائياً
        num_to_select = random.randint(3, 6)
        selected_checkboxes = random.sample(checkboxes, min(num_to_select, len(checkboxes)))
        
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات العشوائية
        for checkbox in selected_checkboxes:
            checkbox.setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = NoScrollTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
