#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النافذة مع رسائل التشخيص
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class TestDebugWindow(QMainWindow):
    """نافذة اختبار مع رسائل التشخيص"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار النافذة مع رسائل التشخيص")
        self.setGeometry(200, 200, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار النافذة مع رسائل التشخيص")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف
        description = QLabel("""
سيتم عرض رسائل التشخيص في وحدة التحكم:
🔧 إنشاء واجهة النافذة
📊 تحميل البيانات
✏️ تعبئة الحقول
🏗️ إنشاء حاوية المجموعات
🔨 إنشاء كل مجموعة
✅ النتيجة النهائية

راقب وحدة التحكم لرؤية التفاصيل
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # زر فتح النافذة
        open_btn = QPushButton("🔍 فتح النافذة مع التشخيص")
        open_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #218838, #1ea085);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #1e7e34, #198754);
            }
        """)
        open_btn.clicked.connect(self.open_debug_dialog)
        layout.addWidget(open_btn)
        
    def open_debug_dialog(self):
        """فتح النافذة مع رسائل التشخيص"""
        try:
            print("=" * 50)
            print("🚀 بدء اختبار النافذة...")
            print("=" * 50)
            
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            print("=" * 50)
            print("📊 معلومات النافذة:")
            print(f"   - عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
            print(f"   - عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
            print(f"   - حجم النافذة: {dialog.size().width()}x{dialog.size().height()}")
            print("=" * 50)
            
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("✅ تم حفظ التغييرات")
            else:
                print("❌ تم إلغاء العملية")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestDebugWindow()
    window.show()
    
    print("تم تشغيل نافذة اختبار التشخيص")
    print("اضغط على الزر ولاحظ رسائل التشخيص في وحدة التحكم")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
