"""
إصلاح مشكلة القوائم المنسدلة في PyQt5
حل مشكلة اختفاء القائمة المنسدلة عند الضغط عليها
"""

from PyQt5.QtWidgets import QComboBox, QApplication
from PyQt5.QtCore import Qt, QEvent, QTimer
from PyQt5.QtGui import QMouseEvent


class FixedComboBox(QComboBox):
    """قائمة منسدلة محسنة لحل مشكلة الاختفاء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_fixed_behavior()
    
    def setup_fixed_behavior(self):
        """إعداد السلوك المحسن للقائمة المنسدلة"""
        # إعدادات أساسية
        self.setFocusPolicy(Qt.StrongFocus)
        self.setInsertPolicy(QComboBox.NoInsert)
        
        # تطبيق التنسيق المحسن
        self.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 8px 12px;
                color: #495057;
                font-size: 14px;
                min-height: 25px;
                padding-right: 35px;
            }
            QComboBox:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border-color: #007bff;
                background-color: white;
                outline: none;
            }
            QComboBox:on {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 30px;
                border-left: 2px solid #dee2e6;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down:hover {
                background-color: #e9ecef;
                border-left-color: #007bff;
            }
            QComboBox::down-arrow {
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid #6c757d;
                margin-top: 2px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #007bff;
            }
            QComboBox::down-arrow:on {
                border-top-color: #007bff;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 2px solid #007bff;
                border-radius: 6px;
                selection-background-color: #007bff;
                selection-color: white;
                padding: 2px;
                outline: none;
                show-decoration-selected: 1;
            }
            QComboBox QAbstractItemView::item {
                padding: 10px 15px;
                border: none;
                min-height: 25px;
                color: #495057;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: #007bff;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات إضافية
        self.setMaxVisibleItems(10)
        self.view().setAlternatingRowColors(False)
    
    def mousePressEvent(self, event):
        """معالجة حدث الضغط بالماوس"""
        if event.button() == Qt.LeftButton:
            # التأكد من أن القائمة ستظهر
            if not self.view().isVisible():
                self.showPopup()
            else:
                self.hidePopup()
        else:
            super().mousePressEvent(event)
    
    def showPopup(self):
        """عرض القائمة المنسدلة مع ضمان الظهور"""
        # التأكد من التركيز
        self.setFocus()
        
        # عرض القائمة
        super().showPopup()
        
        # التأكد من أن القائمة ظاهرة
        QTimer.singleShot(10, self._ensure_popup_visible)
    
    def _ensure_popup_visible(self):
        """التأكد من أن القائمة المنسدلة ظاهرة"""
        if not self.view().isVisible():
            super().showPopup()


class ComboBoxFixer:
    """فئة لإصلاح جميع القوائم المنسدلة في التطبيق"""
    
    @staticmethod
    def fix_combobox(combo: QComboBox):
        """إصلاح قائمة منسدلة واحدة"""
        if isinstance(combo, FixedComboBox):
            return  # مُصلحة بالفعل
        
        # حفظ البيانات الحالية
        items = [combo.itemText(i) for i in range(combo.count())]
        current_text = combo.currentText()
        current_index = combo.currentIndex()
        
        # تطبيق الإصلاح
        combo.setFocusPolicy(Qt.StrongFocus)
        combo.setInsertPolicy(QComboBox.NoInsert)
        
        # تطبيق التنسيق المحسن
        combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 6px;
                padding: 8px 12px;
                color: #495057;
                font-size: 14px;
                min-height: 25px;
                padding-right: 35px;
            }
            QComboBox:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border-color: #007bff;
                background-color: white;
                outline: none;
            }
            QComboBox:on {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 30px;
                border-left: 2px solid #dee2e6;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down:hover {
                background-color: #e9ecef;
                border-left-color: #007bff;
            }
            QComboBox::down-arrow {
                width: 0;
                height: 0;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 8px solid #6c757d;
                margin-top: 2px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #007bff;
            }
            QComboBox::down-arrow:on {
                border-top-color: #007bff;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 2px solid #007bff;
                border-radius: 6px;
                selection-background-color: #007bff;
                selection-color: white;
                padding: 2px;
                outline: none;
                show-decoration-selected: 1;
            }
            QComboBox QAbstractItemView::item {
                padding: 10px 15px;
                border: none;
                min-height: 25px;
                color: #495057;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: #007bff;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)
        
        # إعدادات إضافية
        combo.setMaxVisibleItems(10)
        combo.view().setAlternatingRowColors(False)
        
        # إعادة تعيين البيانات
        combo.clear()
        combo.addItems(items)
        if current_index >= 0:
            combo.setCurrentIndex(current_index)
        
        # ربط حدث مخصص للضغط
        original_mouse_press = combo.mousePressEvent
        
        def custom_mouse_press(event):
            if event.button() == Qt.LeftButton:
                if not combo.view().isVisible():
                    combo.showPopup()
                else:
                    combo.hidePopup()
            else:
                original_mouse_press(event)
        
        combo.mousePressEvent = custom_mouse_press
    
    @staticmethod
    def fix_all_comboboxes(widget):
        """إصلاح جميع القوائم المنسدلة في widget"""
        combo_boxes = widget.findChildren(QComboBox)
        for combo in combo_boxes:
            ComboBoxFixer.fix_combobox(combo)
        
        print(f"تم إصلاح {len(combo_boxes)} قائمة منسدلة")
