#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعادة تنظيم تخطيط تبويبة المرضى
Test patients tab layout reorganization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.patients_tab import PatientsTab

# محاكي قاعدة البيانات للاختبار
class MockDBHandler:
    """محاكي قاعدة البيانات للاختبار"""
    
    def __init__(self):
        # بيانات وهمية للمرضى
        self.patients_data = [
            {
                'id': 1,
                'name': '<PERSON><PERSON><PERSON><PERSON> محمد علي',
                'birth_year': 1985,
                'age': 38,
                'mobile': '**********',
                'whatsapp': '**********',
                'general_diseases': 'ضغط الدم',
                'medications': 'أملوديبين 5mg',
                'notes': 'مريض منتظم في المواعيد'
            },
            {
                'id': 2,
                'name': 'فاطمة أحمد حسن',
                'birth_year': 1990,
                'age': 33,
                'mobile': '**********',
                'whatsapp': '**********',
                'general_diseases': 'لا يوجد',
                'medications': 'لا يوجد',
                'notes': 'تحتاج متابعة دورية'
            },
            {
                'id': 3,
                'name': 'محمد عبد الله',
                'birth_year': 1975,
                'age': 48,
                'mobile': '0955555555',
                'whatsapp': '0955555555',
                'general_diseases': 'السكري',
                'medications': 'ميتفورمين 500mg',
                'notes': 'يحتاج عناية خاصة'
            },
            {
                'id': 4,
                'name': 'سارة محمود',
                'birth_year': 1995,
                'age': 28,
                'mobile': '0944444444',
                'whatsapp': '0944444444',
                'general_diseases': 'لا يوجد',
                'medications': 'لا يوجد',
                'notes': 'مريضة جديدة'
            },
            {
                'id': 5,
                'name': 'عبد الرحمن خالد',
                'birth_year': 1980,
                'age': 43,
                'mobile': '**********',
                'whatsapp': '**********',
                'general_diseases': 'أمراض القلب',
                'medications': 'أسبرين 100mg',
                'notes': 'يحتاج استشارة طبية قبل العلاج'
            }
        ]
    
    def get_all_patients(self):
        """الحصول على جميع المرضى"""
        return self.patients_data
    
    def search_patients(self, search_term):
        """البحث في المرضى"""
        if not search_term:
            return self.patients_data
        
        search_term = search_term.lower()
        results = []
        for patient in self.patients_data:
            if (search_term in patient['name'].lower() or 
                search_term in patient['mobile'] or 
                search_term in patient['whatsapp']):
                results.append(patient)
        return results
    
    def add_patient(self, patient_data):
        """إضافة مريض جديد"""
        new_id = max([p['id'] for p in self.patients_data]) + 1 if self.patients_data else 1
        patient_data['id'] = new_id
        self.patients_data.append(patient_data)
        return new_id
    
    def update_patient(self, patient_id, patient_data):
        """تحديث بيانات مريض"""
        for i, patient in enumerate(self.patients_data):
            if patient['id'] == patient_id:
                patient_data['id'] = patient_id
                self.patients_data[i] = patient_data
                return True
        return False
    
    def delete_patient(self, patient_id):
        """حذف مريض"""
        for i, patient in enumerate(self.patients_data):
            if patient['id'] == patient_id:
                del self.patients_data[i]
                return True
        return False
    
    def get_patient_by_id(self, patient_id):
        """الحصول على مريض بالمعرف"""
        for patient in self.patients_data:
            if patient['id'] == patient_id:
                return patient
        return None

class PatientsTabLayoutTestWindow(QMainWindow):
    """نافذة اختبار إعادة تنظيم تخطيط تبويبة المرضى"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إعادة تنظيم تخطيط تبويبة المرضى")
        self.setGeometry(50, 50, 1600, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إعادة تنظيم تخطيط تبويبة المرضى")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة على تخطيط تبويبة المرضى:
        
        ✅ 1. حذف تسمية "البحث السريع":
        • تم حذف النص/العنوان "🔍 البحث السريع"
        • الاحتفاظ بمربع البحث نفسه مع جميع وظائفه
        • وظيفة البحث تعمل بشكل طبيعي (بحث بالاسم أو رقم الموبايل)
        
        ✅ 2. إعادة ترتيب التخطيط الرئيسي:
        • تغيير من التخطيط الحالي إلى تخطيط أفقي (QHBoxLayout)
        • تقسيم الواجهة إلى قسمين رئيسيين: يمين ويسار
        • دعم كامل للاتجاه من اليمين إلى اليسار (RTL)
        
        ✅ 3. القسم الأيمن (الجديد):
        • نقل قائمة المرضى (patients table) إلى القسم الأيمن
        • وضع مربع البحث مباشرة أسفل أعلى القسم الأيمن
        • عنوان "👥 قائمة المرضى" أسفل مربع البحث
        • ترتيب منطقي: [البحث] ← [عنوان القائمة] ← [جدول المرضى]
        
        ✅ 4. القسم الأيسر (الجديد):
        • نقل منطقة معلومات المريض المحدد إلى القسم الأيسر
        • نقل جميع أزرار الإجراءات (إضافة، تعديل، حذف، إلخ) إلى القسم الأيسر
        • عرض معلومات المريض والأزرار في القسم الأيسر بدلاً من موقعها الحالي
        
        ✅ 5. الوظائف المحفوظة:
        • جميع وظائف البحث (البحث بالاسم أو رقم الموبايل)
        • جميع وظائف إدارة المرضى (إضافة، تعديل، حذف)
        • التنسيق البصري والألوان الحالية
        • التكيف مع أحجام النوافذ المختلفة
        • دعم الاتجاه من اليمين إلى اليسار (RTL)
        
        📊 مقارنة التخطيط:
        
        قبل التحسين:
        • التخطيط: عمودي مع أقسام متداخلة
        • البحث: في القسم الأيمن مع نموذج المريض
        • قائمة المرضى: في القسم الأيسر منفصلة
        • معلومات المريض: في القسم الأيمن مع البحث
        
        بعد التحسين:
        • التخطيط: أفقي مع قسمين واضحين
        • البحث: في القسم الأيمن مع قائمة المرضى
        • قائمة المرضى: في القسم الأيمن مع البحث
        • معلومات المريض: في القسم الأيسر مع الأزرار
        
        🎨 الفوائد المحققة:
        • تنظيم أفضل للواجهة مع فصل واضح بين الوظائف
        • سهولة أكبر في الاستخدام مع تجميع العناصر المترابطة
        • تدفق منطقي: البحث والاختيار في اليمين، العرض والتعديل في اليسار
        • استغلال أفضل للمساحة الأفقية المتاحة
        • تجربة مستخدم محسنة مع ترتيب أكثر بديهية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة المرضى مع التخطيط الجديد
        patients_title = QLabel("⚙️ تبويبة المرضى (مع التخطيط الأفقي الجديد)")
        patients_title.setAlignment(Qt.AlignCenter)
        patients_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(patients_title)
        
        # إنشاء تبويبة المرضى مع محاكي قاعدة البيانات
        mock_db = MockDBHandler()
        self.patients_tab = PatientsTab(mock_db)
        layout.addWidget(self.patients_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار البحث
        test_search_btn = QPushButton("اختبار وظيفة البحث")
        test_search_btn.clicked.connect(self.test_search_function)
        test_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_search_btn)
        
        # زر اختبار اختيار مريض
        test_selection_btn = QPushButton("اختبار اختيار مريض")
        test_selection_btn.clicked.connect(self.test_patient_selection)
        test_selection_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_selection_btn)
        
        # زر مسح البحث
        clear_search_btn = QPushButton("مسح البحث")
        clear_search_btn.clicked.connect(self.clear_search)
        clear_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_search_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار التخطيط الجديد:
        
        🔍 ما يجب ملاحظته:
        • القسم الأيمن: مربع البحث في الأعلى، ثم عنوان "قائمة المرضى"، ثم جدول المرضى
        • القسم الأيسر: معلومات المريض المحدد مع أزرار الإجراءات
        • عدم وجود تسمية "البحث السريع" (تم حذفها حسب المطلوب)
        • التخطيط الأفقي مع قسمين واضحين
        
        🧪 اختبارات يمكن إجراؤها:
        • استخدم "اختبار وظيفة البحث" لاختبار البحث بكلمة "أحمد"
        • استخدم "اختبار اختيار مريض" لاختيار أول مريض في القائمة
        • استخدم "مسح البحث" لإعادة عرض جميع المرضى
        • جرب البحث يدوياً في مربع البحث
        • انقر على المرضى في الجدول ولاحظ عرض معلوماتهم في القسم الأيسر
        
        ✅ النتائج المتوقعة:
        • تخطيط أفقي واضح مع قسمين منفصلين
        • البحث وقائمة المرضى في القسم الأيمن
        • معلومات المريض والأزرار في القسم الأيسر
        • عمل جميع الوظائف بشكل طبيعي
        • تدفق منطقي ومنظم للواجهة
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إعادة تنظيم تخطيط تبويبة المرضى بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_search_function(self):
        """اختبار وظيفة البحث"""
        self.patients_tab.search_input.setText("أحمد")
        print("✅ تم اختبار البحث بكلمة 'أحمد'")
        
    def test_patient_selection(self):
        """اختبار اختيار مريض"""
        if self.patients_tab.patients_table.rowCount() > 0:
            self.patients_tab.patients_table.selectRow(0)
            self.patients_tab.patients_table.itemClicked.emit(
                self.patients_tab.patients_table.item(0, 0)
            )
            print("✅ تم اختيار أول مريض في القائمة")
        else:
            print("❌ لا توجد مرضى في القائمة")
            
    def clear_search(self):
        """مسح البحث"""
        self.patients_tab.search_input.clear()
        print("✅ تم مسح البحث")

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = PatientsTabLayoutTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
