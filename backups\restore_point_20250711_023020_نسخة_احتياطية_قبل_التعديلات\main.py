import sys
import os
import sqlite3
import datetime
from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget, QStackedWidget, QMessageBox
from PyQt5.QtCore import Qt, QSize, QDate, QTime, QDateTime
from PyQt5.QtGui import QIcon, QPixmap, QFont
try:
    import pyqtdarktheme as qdarktheme
except ImportError:
    qdarktheme = None

# استيراد الوحدات المخصصة
from ui.login import LoginWindow
from ui.main_window import MainWindow
from database.db_handler import DatabaseHandler

# تهيئة قاعدة البيانات مع معالجة الأخطاء المحسنة
def initialize_database():
    try:
        print("🔧 بدء تهيئة قاعدة البيانات...")

        # محاولة إنشاء معالج قاعدة البيانات
        db_handler = DatabaseHandler()
        print("✅ تم إنشاء معالج قاعدة البيانات")

        # محاولة إنشاء الجداول
        if not db_handler.create_tables():
            raise Exception("فشل في إنشاء جداول قاعدة البيانات")
        print("✅ تم إنشاء/تحديث جداول قاعدة البيانات")

        # إنشاء حساب المسؤول الافتراضي إذا لم يكن موجوداً
        try:
            if not db_handler.check_user_exists('admin'):
                if db_handler.add_user('admin', 'admin123', 'مدير النظام', 'admin'):
                    print("✅ تم إنشاء حساب المسؤول الافتراضي")
                else:
                    print("⚠️ تحذير: فشل في إنشاء حساب المسؤول الافتراضي")
            else:
                print("✅ حساب المسؤول موجود")
        except Exception as e:
            print(f"⚠️ تحذير: خطأ في إنشاء حساب المسؤول: {e}")

        print("🎉 تم إكمال تهيئة قاعدة البيانات بنجاح")
        return db_handler

    except Exception as e:
        print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")

        # محاولة إصلاح قاعدة البيانات
        try:
            print("🔧 محاولة إصلاح قاعدة البيانات...")
            return repair_database()
        except Exception as repair_error:
            print(f"❌ فشل في إصلاح قاعدة البيانات: {repair_error}")
            return None

def repair_database():
    """إصلاح قاعدة البيانات في حالة وجود مشاكل"""
    import os
    from datetime import datetime

    try:
        # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
        db_path = 'dental_clinic.db'
        if os.path.exists(db_path):
            backup_path = f"dental_clinic_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            import shutil
            shutil.copy2(db_path, backup_path)
            print(f"📦 تم إنشاء نسخة احتياطية: {backup_path}")

            # حذف قاعدة البيانات التالفة
            os.remove(db_path)
            print("🗑️ تم حذف قاعدة البيانات التالفة")

        # إنشاء قاعدة بيانات جديدة
        print("🆕 إنشاء قاعدة بيانات جديدة...")
        db_handler = DatabaseHandler()

        if db_handler.create_tables():
            print("✅ تم إنشاء قاعدة بيانات جديدة بنجاح")

            # إنشاء حساب المسؤول
            if db_handler.add_user('admin', 'admin123', 'مدير النظام', 'admin'):
                print("✅ تم إنشاء حساب المسؤول")

            return db_handler
        else:
            raise Exception("فشل في إنشاء قاعدة البيانات الجديدة")

    except Exception as e:
        print(f"❌ فشل في إصلاح قاعدة البيانات: {e}")
        return None

# الدالة الرئيسية
def main():
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)  # تعيين اتجاه الواجهة من اليمين إلى اليسار
    
    # تطبيق السمة الداكنة/الفاتحة
    if qdarktheme:
        qdarktheme.setup_theme("auto")
    
    # تهيئة قاعدة البيانات
    db_handler = initialize_database()
    
    if db_handler is None:
        QMessageBox.critical(None, "خطأ في قاعدة البيانات",
                           "فشل في تهيئة قاعدة البيانات.\n\n"
                           "الأسباب المحتملة:\n"
                           "• قاعدة البيانات تالفة أو غير متوافقة\n"
                           "• نقص في صلاحيات الكتابة\n"
                           "• مساحة القرص ممتلئة\n\n"
                           "تم إنشاء نسخة احتياطية إذا كانت البيانات موجودة.\n"
                           "يرجى التحقق من ملفات النسخ الاحتياطية في مجلد التطبيق.")
        sys.exit(1)
    
    # إنشاء نافذة تسجيل الدخول
    login_window = LoginWindow(db_handler)
    
    # عند تسجيل الدخول بنجاح، إنشاء النافذة الرئيسية
    def on_login_success(username, user_role):
        login_window.hide()
        main_window = MainWindow(db_handler, username, user_role)
        main_window.show()
        # عند تسجيل الخروج، إظهار نافذة تسجيل الدخول مرة أخرى
        main_window.logout_signal.connect(lambda: [
            main_window.close(),
            login_window.clear_fields(),
            login_window.show()
        ])
    
    # ربط إشارة تسجيل الدخول الناجح
    login_window.login_success.connect(on_login_success)
    
    # عرض نافذة تسجيل الدخول
    login_window.show()
    
    # تنفيذ التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()