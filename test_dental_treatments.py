import sys
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import Qt
from database.db_handler import <PERSON>Handler
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class TestWindow(QMainWindow):
    """نافذة اختبار لتبويبة المعالجات السنية"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار تبويبة المعالجات السنية")
        self.setMinimumSize(1000, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء معالج قاعدة البيانات
        self.db_handler = DatabaseHandler()
        
        # إنشاء تبويبة المعالجات السنية
        self.dental_treatments_tab = DentalTreatmentsTab(self.db_handler)
        
        # تعيين التبويبة كعنصر مركزي
        self.setCentralWidget(self.dental_treatments_tab)

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق نمط عام
    app.setStyleSheet("""
        QWidget {
            font-family: "Segoe UI", "Tahoma", "Arabic UI Text", sans-serif;
            font-size: 12px;
        }
    """)
    
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()