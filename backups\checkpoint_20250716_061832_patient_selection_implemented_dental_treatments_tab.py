from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QFrame, QLineEdit, QTextEdit,
                             QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QGroupBox, QScrollArea, QDialog, QTableWidget,
                             QTableWidgetItem, QHeaderView, QMessageBox,
                             QSplitter, QFormLayout, QSizePolicy, QListWidget,
                             QListWidgetItem, QApplication)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QFont
from datetime import datetime
import json
import os


class PricesManager:
    """مدير الأسعار الافتراضية - حفظ وتحميل الأسعار بشكل دائم"""

    def __init__(self):
        # مسار ملف حفظ الأسعار
        self.prices_file = "dental_prices_config.json"

        # الأسعار الافتراضية المبرمجة (للاستعادة)
        self.default_prices = {
            # اللبية
            "endodontic_Vital": 120000,
            "endodontic_Necrotic": 150000,
            "endodontic_إعادة معالجة": 200000,
            "endodontic_متكلسة": 180000,
            "endodontic_C shape": 250000,
            "endodontic_ذروة مفتوحة": 160000,
            "endodontic_أداة مكسورة": 300000,

            # الترميمية
            "restorative_كومبوزت": 75000,
            "restorative_أملغم": 50000,
            "restorative_GIC": 40000,
            "restorative_وتد فايبر": 80000,
            "restorative_قلب معدني": 60000,

            # التيجان
            "crowns_خزف معدن": 150000,
            "crowns_زيركون 4D": 200000,
            "crowns_زيركون مغطى إيماكس": 250000,
            "crowns_زيركون مغطى خزف": 220000,
            "crowns_زيركون cutback": 230000,
            "crowns_ستانلس": 80000,
            "crowns_إيماكس": 180000,

            # الجراحة - الخيارات الأصلية
            "surgery_قلع بسيط": 30000,
            "surgery_قلع جراحي": 75000,
            "surgery_منحصرة": 100000,
            "surgery_منطمرة": 120000,
            "surgery_تطويل تاج": 80000,
            "surgery_قطع ذروة": 90000,
            "surgery_تضحيك": 150000
        }

    def load_prices(self):
        """تحميل الأسعار المحفوظة أو الافتراضية"""
        try:
            if os.path.exists(self.prices_file):
                with open(self.prices_file, 'r', encoding='utf-8') as f:
                    saved_prices = json.load(f)

                # التحقق من وجود جميع المفاتيح المطلوبة
                for key in self.default_prices:
                    if key not in saved_prices:
                        saved_prices[key] = self.default_prices[key]

                print(f"تم تحميل الأسعار المحفوظة من {self.prices_file}")
                return saved_prices
            else:
                print("لم يتم العثور على ملف أسعار محفوظ، استخدام الأسعار الافتراضية")
                return self.default_prices.copy()

        except Exception as e:
            print(f"خطأ في تحميل الأسعار: {e}")
            print("استخدام الأسعار الافتراضية")
            return self.default_prices.copy()

    def save_prices(self, prices):
        """حفظ الأسعار بشكل دائم"""
        try:
            with open(self.prices_file, 'w', encoding='utf-8') as f:
                json.dump(prices, f, ensure_ascii=False, indent=4)
            print(f"تم حفظ الأسعار في {self.prices_file}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ الأسعار: {e}")
            return False

    def reset_to_defaults(self):
        """إعادة تعيين الأسعار للقيم الافتراضية المبرمجة"""
        try:
            if os.path.exists(self.prices_file):
                os.remove(self.prices_file)
                print("تم حذف ملف الأسعار المحفوظة")
            return self.default_prices.copy()
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار: {e}")
            return self.default_prices.copy()

    def get_default_prices(self):
        """الحصول على الأسعار الافتراضية المبرمجة"""
        return self.default_prices.copy()


class ToothButton(QPushButton):
    """زر سن تفاعلي"""
    tooth_selected = pyqtSignal(int)

    def __init__(self, tooth_number, parent=None):
        super().__init__(parent)
        self.tooth_number = tooth_number
        self.is_selected = False
        self.setFixedSize(25, 30)
        self.setText(str(tooth_number))
        self.setCheckable(True)
        self.clicked.connect(self.on_clicked)
        self.update_style()

    def on_clicked(self):
        """عند النقر على السن"""
        self.is_selected = not self.is_selected
        self.tooth_selected.emit(self.tooth_number)
        self.update_style()

    def set_selected(self, selected):
        """تعيين حالة التحديد"""
        self.is_selected = selected
        self.setChecked(selected)
        self.update_style()

    def update_style(self):
        """تحديث تنسيق الزر"""
        if self.is_selected:
            self.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: 2px solid #0056b3;
                    border-radius: 4px;
                    font-size: 8px;
                    font-weight: bold;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: #f8f9fa;
                    color: #495057;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    font-size: 8px;
                }
                QPushButton:hover {
                    background-color: #e9ecef;
                    border-color: #007bff;
                }
            """)

class CompactTeethChart(QWidget):
    """مخطط أسنان مدمج للبالغين"""
    tooth_selected = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_tooth = None
        self.tooth_buttons = {}
        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة مخطط الأسنان مع نفس تنسيق الحاويات الأخرى
        group = QGroupBox("مخطط الأسنان التفاعلي")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                right: 10px;
                padding: 2px 8px 2px 8px;
                color: #007bff;
                text-align: right;
            }
        """)

        # تخطيط المجموعة (هوامش مقللة لتوفير المساحة)
        group_layout = QVBoxLayout(group)
        group_layout.setContentsMargins(8, 12, 8, 8)  # تقليل الهوامش
        group_layout.setSpacing(3)  # تقليل المسافات

        # حاوية المخطط (ارتفاع محسن ومقلل)
        chart_container = QWidget()
        chart_container.setFixedSize(450, 65)  # تقليل الارتفاع من 80 إلى 65
        chart_layout = QGridLayout(chart_container)
        chart_layout.setContentsMargins(1, 1, 1, 1)  # تقليل الهوامش
        chart_layout.setSpacing(1)

        # إنشاء أزرار الأسنان
        self.create_teeth_buttons(chart_layout)

        # إضافة حاوية المخطط إلى المجموعة
        group_layout.addWidget(chart_container, 0, Qt.AlignCenter)

        # إضافة المجموعة إلى التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(group)

    def create_teeth_buttons(self, layout):
        """إنشاء أزرار الأسنان"""
        # الفك العلوي (من اليمين إلى اليسار)
        upper_teeth = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28]
        for i, tooth in enumerate(upper_teeth):
            btn = ToothButton(tooth)
            btn.tooth_selected.connect(self.on_tooth_selected)
            self.tooth_buttons[tooth] = btn
            layout.addWidget(btn, 0, i)

        # الفك السفلي (من اليمين إلى اليسار)
        lower_teeth = [48, 47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37, 38]
        for i, tooth in enumerate(lower_teeth):
            btn = ToothButton(tooth)
            btn.tooth_selected.connect(self.on_tooth_selected)
            self.tooth_buttons[tooth] = btn
            layout.addWidget(btn, 1, i)

    def on_tooth_selected(self, tooth_number):
        """معالجة اختيار السن"""
        # إلغاء تحديد السن السابق
        if self.selected_tooth and self.selected_tooth != tooth_number:
            self.tooth_buttons[self.selected_tooth].set_selected(False)

        self.selected_tooth = tooth_number
        self.tooth_selected.emit(tooth_number)

    def get_selected_tooth(self):
        """الحصول على السن المحدد"""
        return self.selected_tooth

class TreatmentOptionsWidget(QWidget):
    """حاوية خيارات المعالجة"""
    options_changed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checkboxes = {}
        self.price_spinboxes = {}  # قاموس لحفظ حقول الأسعار

        # إنشاء مدير الأسعار
        self.prices_manager = PricesManager()

        # تحميل الأسعار المحفوظة
        self.current_prices = self.prices_manager.load_prices()

        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة خيارات المعالجة مع نفس تنسيق خطة المعالجة
        group = QGroupBox("خيارات المعالجة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                color: #007bff;
            }
        """)

        # تخطيط أفقي مباشرة داخل المجموعة (صف واحد)
        self.horizontal_layout = QHBoxLayout(group)
        self.horizontal_layout.setSpacing(10)  # مسافة أقل بين المجموعات
        self.horizontal_layout.setContentsMargins(10, 25, 10, 15)

        # إنشاء المجموعات الأربعة مع عرض محسن
        self.endodontic_group = self.create_endodontic_group()
        self.restorative_group = self.create_restorative_group()
        self.crowns_group = self.create_crowns_group()
        self.surgery_group = self.create_surgery_group()

        # إنشاء مجموعات الأسعار المقابلة
        self.endodontic_prices_group = self.create_endodontic_prices_group()
        self.restorative_prices_group = self.create_restorative_prices_group()
        self.crowns_prices_group = self.create_crowns_prices_group()
        self.surgery_prices_group = self.create_surgery_prices_group()

        # ترتيب المجموعات في صف أفقي واحد من اليمين إلى اليسار
        # [لبية] [أسعار اللبية] [ترميمية] [أسعار الترميمية] [تيجان] [أسعار التيجان] [جراحة] [أسعار الجراحة]
        self.horizontal_layout.addWidget(self.endodontic_group)
        self.horizontal_layout.addWidget(self.endodontic_prices_group)
        self.horizontal_layout.addWidget(self.restorative_group)
        self.horizontal_layout.addWidget(self.restorative_prices_group)
        self.horizontal_layout.addWidget(self.crowns_group)
        self.horizontal_layout.addWidget(self.crowns_prices_group)
        self.horizontal_layout.addWidget(self.surgery_group)
        self.horizontal_layout.addWidget(self.surgery_prices_group)

        # تعيين نسب التمدد المتساوية لجميع المجموعات الثمانية
        for i in range(8):
            self.horizontal_layout.setStretch(i, 1)

        # إضافة المجموعة إلى التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(group)

        # تطبيق التخطيط الأولي بناءً على الحجم الحالي
        self.adjust_layout_for_width(self.width())

    def create_endodontic_group(self):
        """إنشاء مجموعة اللبية"""
        group = QGroupBox("لبية (Endodontic)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)  # ارتفاع محسن لضمان ظهور الحدود بالكامل
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        options = ["Vital", "Necrotic", "إعادة معالجة", "متكلسة",
                  "C shape", "ذروة مفتوحة", "أداة مكسورة"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار مع عرض محسن
        for option in options:
            checkbox = QCheckBox(option)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع عرض أكبر لضمان ظهور جميع النصوص بالكامل
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 13px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"endodontic_{option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_endodontic_prices_group(self):
        """إنشاء مجموعة أسعار اللبية"""
        group = QGroupBox("أسعار اللبية")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات اللبية مع الأسعار المحفوظة
        endodontic_options = ["Vital", "Necrotic", "إعادة معالجة", "متكلسة",
                             "C shape", "ذروة مفتوحة", "أداة مكسورة"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in endodontic_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"endodontic_{option}"
            saved_price = self.current_prices.get(price_key, 0)
            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setSuffix(" ل.س")
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"endodontic_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_restorative_group(self):
        """إنشاء مجموعة الترميمية"""
        group = QGroupBox("ترميمية (Restorative)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)  # ارتفاع محسن لضمان ظهور الحدود بالكامل
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        options = ["كومبوزت", "أملغم", "GIC", "وتد فايبر", "قلب معدني"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for option in options:
            checkbox = QCheckBox(option)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 13px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"restorative_{option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_restorative_prices_group(self):
        """إنشاء مجموعة أسعار الترميمية"""
        group = QGroupBox("أسعار الترميمية")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات الترميمية مع الأسعار المحفوظة
        restorative_options = ["كومبوزت", "أملغم", "GIC", "وتد فايبر", "قلب معدني"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in restorative_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"restorative_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setSuffix(" ل.س")
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"restorative_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_crowns_group(self):
        """إنشاء مجموعة التيجان"""
        group = QGroupBox("تيجان (Crowns)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(170)  # عرض أكبر قليلاً للنصوص الطويلة مثل "زيركون مغطى إيماكس"
        group.setMinimumHeight(220)  # ارتفاع محسن لضمان ظهور الحدود بالكامل
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        options = ["خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس",
                  "زيركون مغطى خزف", "زيركون cutback", "ستانلس", "إيماكس"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for option in options:
            checkbox = QCheckBox(option)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 12px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 140px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"crowns_{option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_crowns_prices_group(self):
        """إنشاء مجموعة أسعار التيجان"""
        group = QGroupBox("أسعار التيجان")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(170)  # عرض أكبر قليلاً لمطابقة مجموعة التيجان
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات التيجان مع الأسعار المحفوظة
        crowns_options = ["خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس",
                         "زيركون مغطى خزف", "زيركون cutback", "ستانلس", "إيماكس"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in crowns_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"crowns_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setSuffix(" ل.س")
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"crowns_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_surgery_group(self):
        """إنشاء مجموعة الجراحة"""
        group = QGroupBox("جراحة (Surgery)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)  # ارتفاع محسن لضمان ظهور الحدود بالكامل
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        options = ["قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة",
                  "تطويل تاج", "قطع ذروة", "تضحيك"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for option in options:
            checkbox = QCheckBox(option)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 13px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"surgery_{option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_surgery_prices_group(self):
        """إنشاء مجموعة أسعار الجراحة"""
        group = QGroupBox("أسعار الجراحة")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات الجراحة الأصلية مع الأسعار المحفوظة
        surgery_options = ["قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة",
                          "تطويل تاج", "قطع ذروة", "تضحيك"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in surgery_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"surgery_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setSuffix(" ل.س")
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"surgery_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def get_group_style(self):
        """الحصول على تنسيق المجموعة المحسن"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: #ffffff;
                min-height: 210px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                background-color: #f8f9fa;
                border-radius: 4px;
                color: #495057;
            }
            QCheckBox {
                font-size: 10px;
                margin: 2px 5px;
                padding: 2px;
                color: #495057;
            }
            QCheckBox:hover {
                background-color: #f8f9fa;
                border-radius: 3px;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border-radius: 2px;
                border: 1px solid #ced4da;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
            }
        """

    def on_option_changed(self):
        """عند تغيير أي خيار - حساب الكلفة التلقائي"""
        self.calculate_total_cost()
        self.options_changed.emit()

    def calculate_total_cost(self):
        """حساب الكلفة الإجمالية بناءً على الخيارات المحددة والأسعار"""
        try:
            total_cost = 0

            # التحقق من جميع مربعات الاختيار المحددة
            for checkbox_key, checkbox in self.checkboxes.items():
                if checkbox.isChecked():
                    # البحث عن السعر المقابل في حقول الأسعار
                    if checkbox_key in self.price_spinboxes:
                        price = self.price_spinboxes[checkbox_key].value()
                        total_cost += price

            # تحديث حقل الكلفة في خطة المعالجة
            self.update_cost_field(total_cost)

        except Exception as e:
            print(f"خطأ في حساب الكلفة الإجمالية: {e}")

    def update_cost_field(self, total_cost):
        """تحديث حقل الكلفة في خطة المعالجة"""
        try:
            # البحث عن حقل الكلفة في الواجهة الرئيسية
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'treatment_plan'):
                    cost_spinbox = parent_widget.treatment_plan.cost_spinbox
                    cost_spinbox.setValue(total_cost)
                    break
                parent_widget = parent_widget.parent()
        except Exception as e:
            print(f"خطأ في تحديث حقل الكلفة: {e}")

    def get_custom_prices(self):
        """الحصول على الأسعار المخصصة"""
        custom_prices = {}
        for key, spinbox in self.price_spinboxes.items():
            custom_prices[key] = spinbox.value()
        return custom_prices

    def save_custom_prices_permanently(self):
        """حفظ الأسعار المخصصة بشكل دائم"""
        try:
            current_prices = self.get_custom_prices()
            success = self.prices_manager.save_prices(current_prices)
            if success:
                # تحديث الأسعار الحالية
                self.current_prices = current_prices
                print("تم حفظ الأسعار المخصصة بشكل دائم")
                return True
            else:
                print("فشل في حفظ الأسعار المخصصة")
                return False
        except Exception as e:
            print(f"خطأ في حفظ الأسعار المخصصة: {e}")
            return False

    def load_saved_prices(self):
        """تحميل الأسعار المحفوظة وتطبيقها"""
        try:
            # تحميل الأسعار المحفوظة
            self.current_prices = self.prices_manager.load_prices()

            # تطبيق الأسعار على حقول الأسعار
            for key, price in self.current_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            print("تم تحميل الأسعار المحفوظة وتطبيقها")
            return True
        except Exception as e:
            print(f"خطأ في تحميل الأسعار المحفوظة: {e}")
            return False

    def reset_prices_to_defaults(self):
        """إعادة تعيين الأسعار للقيم الافتراضية المبرمجة"""
        try:
            # إعادة تعيين الأسعار للافتراضية
            default_prices = self.prices_manager.reset_to_defaults()

            # تطبيق الأسعار الافتراضية على حقول الأسعار
            for key, price in default_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            # تحديث الأسعار الحالية
            self.current_prices = default_prices

            print("تم إعادة تعيين الأسعار للقيم الافتراضية")
            return True
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار: {e}")
            return False

    def set_custom_prices(self, custom_prices):
        """تعيين الأسعار المخصصة"""
        try:
            for key, price in custom_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)
        except Exception as e:
            print(f"خطأ في تعيين الأسعار المخصصة: {e}")

    def reset_to_default_prices(self):
        """إعادة تعيين الأسعار الافتراضية"""
        try:
            # أسعار افتراضية لجميع الفئات
            default_prices = {
                # اللبية
                "endodontic_Vital": 120000,
                "endodontic_Necrotic": 150000,
                "endodontic_إعادة معالجة": 200000,
                "endodontic_متكلسة": 180000,
                "endodontic_C shape": 250000,
                "endodontic_ذروة مفتوحة": 160000,
                "endodontic_أداة مكسورة": 300000,

                # الترميمية
                "restorative_كومبوزت": 75000,
                "restorative_أملغم": 50000,
                "restorative_GIC": 40000,
                "restorative_وتد فايبر": 80000,
                "restorative_قلب معدني": 60000,

                # التيجان
                "crowns_خزف معدن": 150000,
                "crowns_زيركون 4D": 200000,
                "crowns_زيركون مغطى إيماكس": 250000,
                "crowns_زيركون مغطى خزف": 220000,
                "crowns_زيركون cutback": 230000,
                "crowns_ستانلس": 80000,
                "crowns_إيماكس": 180000,

                # الجراحة - الخيارات الأصلية
                "surgery_قلع بسيط": 30000,
                "surgery_قلع جراحي": 75000,
                "surgery_منحصرة": 100000,
                "surgery_منطمرة": 120000,
                "surgery_تطويل تاج": 80000,
                "surgery_قطع ذروة": 90000,
                "surgery_تضحيك": 150000
            }

            self.set_custom_prices(default_prices)
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار الافتراضية: {e}")

    def get_selected_options(self):
        """الحصول على الخيارات المحددة"""
        selected = []
        for checkbox in self.checkboxes.values():
            if checkbox.isChecked():
                selected.append(checkbox.text())
        return selected

    def clear_all_options(self):
        """مسح جميع الخيارات"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة لضمان الاستجابة"""
        super().resizeEvent(event)

        # تعديل التخطيط بناءً على العرض الجديد
        self.adjust_layout_for_width(self.width())

        # إجبار إعادة حساب التخطيط
        self.updateGeometry()

    def adjust_layout_for_width(self, width):
        """تعديل التخطيط بناءً على عرض النافذة"""
        if not hasattr(self, 'grid_layout'):
            return

        # إذا كان العرض صغيراً، رتب المجموعات في عمود واحد
        if width < 800:
            # ترتيب عمودي (4x1)
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 1, 0)
            self.grid_layout.addWidget(self.crowns_group, 2, 0)
            self.grid_layout.addWidget(self.surgery_group, 3, 0)

            # إعادة تعيين نسب التمدد
            for i in range(4):
                self.grid_layout.setRowStretch(i, 1)
            self.grid_layout.setColumnStretch(0, 1)

        elif width < 1200:
            # ترتيب في عمودين (2x2)
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 0, 1)
            self.grid_layout.addWidget(self.crowns_group, 1, 0)
            self.grid_layout.addWidget(self.surgery_group, 1, 1)

            # إعادة تعيين نسب التمدد
            self.grid_layout.setColumnStretch(0, 1)
            self.grid_layout.setColumnStretch(1, 1)
            self.grid_layout.setRowStretch(0, 1)
            self.grid_layout.setRowStretch(1, 1)

        else:
            # ترتيب أفقي (1x4) للشاشات الكبيرة
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 0, 1)
            self.grid_layout.addWidget(self.crowns_group, 0, 2)
            self.grid_layout.addWidget(self.surgery_group, 0, 3)

            # إعادة تعيين نسب التمدد
            for i in range(4):
                self.grid_layout.setColumnStretch(i, 1)
            self.grid_layout.setRowStretch(0, 1)

    def showEvent(self, event):
        """عند عرض الواجهة لأول مرة"""
        super().showEvent(event)
        # تطبيق التخطيط المناسب بناءً على الحجم الحالي
        self.adjust_layout_for_width(self.width())

class PatientSelectionModal(QDialog):
    """نافذة اختيار المريض"""

    patient_selected = pyqtSignal(int, str)  # patient_id, patient_name

    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.selected_patient_id = None
        self.selected_patient_name = ""
        self.init_ui()
        self.load_patients()

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختيار المريض")
        self.setModal(True)
        self.resize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("اختر مريضاً")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # حقل البحث
        search_layout = QHBoxLayout()
        search_label = QLabel("البحث:")
        search_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو رقم الهاتف...")
        self.search_input.textChanged.connect(self.filter_patients)
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)

        # قائمة المرضى
        self.patients_list = QListWidget()
        self.patients_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:hover {
                background-color: #e8f4fd;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        self.patients_list.itemDoubleClicked.connect(self.on_patient_double_clicked)
        layout.addWidget(self.patients_list)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر اختيار
        select_btn = QPushButton("اختيار")
        select_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        select_btn.clicked.connect(self.select_patient)

        # زر إلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(select_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)

    def load_patients(self):
        """تحميل قائمة المرضى"""
        try:
            patients = self.db_handler.get_all_patients()
            self.all_patients = patients if patients else []
            self.display_patients(self.all_patients)
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل قائمة المرضى: {str(e)}")

    def display_patients(self, patients):
        """عرض قائمة المرضى"""
        self.patients_list.clear()

        for patient in patients:
            # إنشاء عنصر القائمة
            item = QListWidgetItem()

            # تنسيق النص
            name = patient.get('name', 'غير محدد')
            mobile = patient.get('mobile', 'غير محدد')
            display_text = f"{name}\nالهاتف: {mobile}"

            item.setText(display_text)
            item.setData(Qt.UserRole, patient['id'])  # حفظ معرف المريض
            item.setData(Qt.UserRole + 1, name)  # حفظ اسم المريض

            self.patients_list.addItem(item)

    def filter_patients(self):
        """تصفية المرضى حسب النص المدخل"""
        search_term = self.search_input.text().strip().lower()

        if not search_term:
            self.display_patients(self.all_patients)
            return

        # تصفية المرضى
        filtered_patients = []
        for patient in self.all_patients:
            name = patient.get('name', '').lower()
            mobile = patient.get('mobile', '').lower()

            if search_term in name or search_term in mobile:
                filtered_patients.append(patient)

        self.display_patients(filtered_patients)

    def on_patient_double_clicked(self, item):
        """معالجة النقر المزدوج على المريض"""
        self.select_patient()

    def select_patient(self):
        """اختيار المريض المحدد"""
        current_item = self.patients_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض من القائمة")
            return

        self.selected_patient_id = current_item.data(Qt.UserRole)
        self.selected_patient_name = current_item.data(Qt.UserRole + 1)

        # إرسال إشارة اختيار المريض
        self.patient_selected.emit(self.selected_patient_id, self.selected_patient_name)
        self.accept()


class TreatmentPlanWidget(QWidget):
    """حاوية خطة المعالجة السنية"""

    # إشارة لإعلام المكونات الأخرى بتغيير المريض
    patient_changed = pyqtSignal(int, str)  # patient_id, patient_name

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_handler = None
        self.selected_patient_id = None
        self.selected_patient_name = ""
        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة خطة المعالجة
        group = QGroupBox("خطة المعالجة السنية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                color: #007bff;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 10)

        # الصف الأفقي الواحد - جميع الحقول في صف واحد
        horizontal_layout = QHBoxLayout()
        horizontal_layout.setSpacing(10)

        # حقل اسم المريض - الحقل الأول
        patient_layout = QVBoxLayout()
        patient_label = QLabel("اسم المريض")
        patient_label.setAlignment(Qt.AlignCenter)
        patient_label.setFixedWidth(120)
        patient_layout.addWidget(patient_label)
        self.patient_name_edit = QLineEdit()
        self.patient_name_edit.setReadOnly(True)
        self.patient_name_edit.setPlaceholderText("لم يتم اختيار مريض")
        self.patient_name_edit.setFixedWidth(120)
        self.patient_name_edit.setAlignment(Qt.AlignCenter)
        self.patient_name_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 4px;
                padding: 5px;
                color: #495057;
            }
        """)
        patient_layout.addWidget(self.patient_name_edit)
        horizontal_layout.addLayout(patient_layout)

        # رقم خطة المعالجة السنية - عرض مطابق للعنوان
        plan_layout = QVBoxLayout()
        plan_label = QLabel("رقم الخطة")
        plan_label.setAlignment(Qt.AlignCenter)  # توسيط عنوان الحقل
        # تعيين عرض ثابت للعنوان لضمان التطابق
        plan_label.setFixedWidth(100)
        plan_layout.addWidget(plan_label)
        self.plan_number_edit = QLineEdit()
        self.plan_number_edit.setPlaceholderText("رقم الخطة")
        # تطابق العرض مع العنوان
        self.plan_number_edit.setFixedWidth(100)
        self.plan_number_edit.setAlignment(Qt.AlignCenter)  # توسيط المحتوى
        plan_layout.addWidget(self.plan_number_edit)
        horizontal_layout.addLayout(plan_layout)

        # رقم السن - عرض محسن لوضوح أكبر
        tooth_layout = QVBoxLayout()
        tooth_label = QLabel("رقم السن")
        tooth_label.setAlignment(Qt.AlignCenter)  # توسيط عنوان الحقل
        # تعيين عرض ثابت محسن للعنوان
        tooth_label.setFixedWidth(100)
        tooth_layout.addWidget(tooth_label)
        self.tooth_number_edit = QLineEdit()
        self.tooth_number_edit.setReadOnly(True)
        self.tooth_number_edit.setPlaceholderText("السن")
        # زيادة العرض لوضوح أكبر
        self.tooth_number_edit.setFixedWidth(100)
        self.tooth_number_edit.setAlignment(Qt.AlignCenter)  # توسيط المحتوى
        tooth_layout.addWidget(self.tooth_number_edit)
        horizontal_layout.addLayout(tooth_layout)

        # المعالجة السنية (حجم كبير) - إزالة أسهم التمرير
        treatment_layout = QVBoxLayout()
        treatment_label = QLabel("المعالجة السنية")
        treatment_label.setAlignment(Qt.AlignCenter)  # توسيط عنوان الحقل
        treatment_layout.addWidget(treatment_label)
        self.treatment_text = QTextEdit()
        self.treatment_text.setMaximumHeight(60)
        self.treatment_text.setPlaceholderText("تفاصيل المعالجة")
        self.treatment_text.setMinimumWidth(200)
        # إزالة أسهم التمرير العمودية
        self.treatment_text.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        # إزالة أسهم التمرير الأفقية أيضاً للحصول على مظهر أنظف
        self.treatment_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        treatment_layout.addWidget(self.treatment_text)
        horizontal_layout.addLayout(treatment_layout)

        # الكلفة - حقل فارغ بدون قيمة افتراضية
        cost_layout = QVBoxLayout()
        cost_label = QLabel("الكلفة")
        cost_label.setAlignment(Qt.AlignCenter)  # توسيط عنوان الحقل
        # تعيين عرض ثابت للعنوان لضمان التطابق
        cost_label.setFixedWidth(150)
        cost_layout.addWidget(cost_label)
        self.cost_spinbox = QSpinBox()
        self.cost_spinbox.setMinimum(0)
        self.cost_spinbox.setMaximum(999999999)
        self.cost_spinbox.setSuffix(" ل.س")
        # إظهار حقل فارغ بدلاً من الصفر
        self.cost_spinbox.setSpecialValueText("")
        # زيادة العرض لعرض القيم الكبيرة بوضوح أكبر
        self.cost_spinbox.setFixedWidth(150)
        self.cost_spinbox.setAlignment(Qt.AlignCenter)  # توسيط المحتوى
        # إزالة أسهم التحكم
        self.cost_spinbox.setButtonSymbols(QSpinBox.NoButtons)
        cost_layout.addWidget(self.cost_spinbox)
        horizontal_layout.addLayout(cost_layout)

        # التاريخ (حجم متوسط) - إزالة أسهم التحكم والتقويم المنبثق
        date_layout = QVBoxLayout()
        date_label = QLabel("التاريخ")
        date_label.setAlignment(Qt.AlignCenter)  # توسيط عنوان الحقل
        date_layout.addWidget(date_label)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())  # التاريخ التلقائي
        # إزالة التقويم المنبثق
        self.date_edit.setCalendarPopup(False)
        self.date_edit.setFixedWidth(120)
        # إزالة أسهم التحكم
        self.date_edit.setButtonSymbols(QDateEdit.NoButtons)
        date_layout.addWidget(self.date_edit)
        horizontal_layout.addLayout(date_layout)

        # إضافة مساحة مرنة في النهاية
        horizontal_layout.addStretch()

        layout.addLayout(horizontal_layout)

        # تخطيط رئيسي
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(group)

    def set_tooth_number(self, tooth_number):
        """تعيين رقم السن"""
        current_tooth = self.tooth_number_edit.text()
        if current_tooth and current_tooth != str(tooth_number):
            reply = QMessageBox.question(
                self,
                "تأكيد تغيير السن",
                f"هل تريد تغيير السن من {current_tooth} إلى {tooth_number}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return False

        self.tooth_number_edit.setText(str(tooth_number))
        return True

    def set_treatment_text(self, treatment_options):
        """تعيين نص المعالجة"""
        if treatment_options:
            self.treatment_text.setPlainText(", ".join(treatment_options))

    def get_plan_data(self):
        """الحصول على بيانات الخطة - التعامل مع القيم الفارغة"""
        # التعامل مع القيمة الفارغة للكلفة
        cost_value = self.cost_spinbox.value()
        cost = int(cost_value) if cost_value > 0 else 0

        return {
            'plan_number': self.plan_number_edit.text(),
            'tooth_number': self.tooth_number_edit.text(),
            'treatment': self.treatment_text.toPlainText(),
            'cost': cost,
            'date': self.date_edit.date().toString('yyyy-MM-dd')
        }

    def clear_form(self):
        """مسح النموذج"""
        self.plan_number_edit.clear()
        self.tooth_number_edit.clear()
        self.treatment_text.clear()
        self.cost_spinbox.setValue(0)
        self.date_edit.setDate(QDate.currentDate())
        # لا نمسح اسم المريض عند مسح النموذج

    def set_db_handler(self, db_handler):
        """تعيين معالج قاعدة البيانات"""
        self.db_handler = db_handler

    def open_patient_selection(self):
        """فتح نافذة اختيار المريض"""
        if not self.db_handler:
            QMessageBox.warning(self, "خطأ", "لم يتم تعيين معالج قاعدة البيانات")
            return

        modal = PatientSelectionModal(self.db_handler, self)
        modal.patient_selected.connect(self.on_patient_selected)
        modal.exec_()

    def on_patient_selected(self, patient_id, patient_name):
        """معالجة اختيار المريض"""
        self.selected_patient_id = patient_id
        self.selected_patient_name = patient_name
        self.patient_name_edit.setText(patient_name)

        # إرسال إشارة لإعلام المكونات الأخرى بتغيير المريض
        self.patient_changed.emit(patient_id, patient_name)

    def get_selected_patient_id(self):
        """الحصول على معرف المريض المختار"""
        return self.selected_patient_id

    def get_selected_patient_name(self):
        """الحصول على اسم المريض المختار"""
        return self.selected_patient_name

    def set_selected_patient(self, patient_id, patient_name):
        """تعيين المريض المختار من الخارج"""
        self.selected_patient_id = patient_id
        self.selected_patient_name = patient_name
        self.patient_name_edit.setText(patient_name)

class TreatmentSessionDialog(QDialog):
    """نافذة جلسات المعالجة السنية المنبثقة"""

    def __init__(self, plan_id=None, plan_number="", cost=0.0, patient_id=None, parent=None):
        super().__init__(parent)
        self.plan_id = plan_id
        self.plan_number = plan_number
        self.cost = cost
        self.patient_id = patient_id
        self.parent_tab = parent  # مرجع لتبويبة علاج الأسنان
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("جلسات المعالجة السنية")
        self.setModal(True)
        # تعديل الحجم ليطابق حجم واجهة تبويبة علاج الأسنان الرئيسية
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة على الشاشة
        self.center_on_screen()

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # مخطط الأسنان
        self.teeth_chart = CompactTeethChart()
        self.teeth_chart.tooth_selected.connect(self.on_tooth_selected)
        main_layout.addWidget(self.teeth_chart)

        # نموذج جلسة المعالجة
        self.create_session_form(main_layout)

        # أزرار التحكم
        self.create_control_buttons(main_layout)

    def create_session_form(self, parent_layout):
        """إنشاء نموذج جلسة المعالجة"""
        group = QGroupBox("بيانات جلسة المعالجة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #28a745;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #28a745;
            }
        """)

        layout = QFormLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 20, 15, 15)

        # معرف خطة المعالجة
        self.plan_id_label = QLabel(str(self.plan_id) if self.plan_id else "غير محدد")
        self.plan_id_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        layout.addRow("معرف خطة المعالجة:", self.plan_id_label)

        # رقم خطة المعالجة السنية
        self.plan_number_label = QLabel(self.plan_number)
        self.plan_number_label.setStyleSheet("font-weight: bold; color: #007bff;")
        layout.addRow("رقم خطة المعالجة السنية:", self.plan_number_label)

        # التاريخ
        self.session_date_edit = QDateEdit()
        self.session_date_edit.setDate(QDate.currentDate())
        self.session_date_edit.setCalendarPopup(True)
        layout.addRow("التاريخ:", self.session_date_edit)

        # رقم السن
        self.session_tooth_edit = QLineEdit()
        self.session_tooth_edit.setReadOnly(True)
        self.session_tooth_edit.setPlaceholderText("سيتم تعبئته من مخطط الأسنان")
        layout.addRow("رقم السن:", self.session_tooth_edit)

        # الإجراء
        self.procedure_text = QTextEdit()
        self.procedure_text.setMaximumHeight(120)
        self.procedure_text.setPlaceholderText("اكتب تفاصيل الإجراء المنفذ")
        layout.addRow("الإجراء:", self.procedure_text)

        # الكلفة (للقراءة فقط) - أرقام صحيحة
        self.cost_label = QLabel(f"{int(self.cost)} ليرة سورية")
        self.cost_label.setStyleSheet("font-weight: bold; color: #28a745; font-size: 14px;")
        layout.addRow("الكلفة:", self.cost_label)

        # دفعة - حقل فارغ بدون قيمة افتراضية
        self.payment_spinbox = QSpinBox()
        self.payment_spinbox.setMinimum(0)
        self.payment_spinbox.setMaximum(999999999)
        self.payment_spinbox.setSuffix(" ليرة سورية")
        # إظهار حقل فارغ بدلاً من الصفر
        self.payment_spinbox.setSpecialValueText("")
        # زيادة العرض لعرض القيم الكبيرة بوضوح أكبر
        self.payment_spinbox.setFixedWidth(150)
        self.payment_spinbox.setAlignment(Qt.AlignCenter)
        self.payment_spinbox.valueChanged.connect(self.calculate_remaining)
        layout.addRow("دفعة:", self.payment_spinbox)

        # مجموع الدفعات - أرقام صحيحة
        self.total_payments_label = QLabel("0 ليرة سورية")
        self.total_payments_label.setStyleSheet("font-weight: bold; color: #17a2b8;")
        layout.addRow("مجموع الدفعات:", self.total_payments_label)

        # المتبقي - أرقام صحيحة
        self.remaining_label = QLabel(f"{int(self.cost)} ليرة سورية")
        self.remaining_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        layout.addRow("المتبقي:", self.remaining_label)

        parent_layout.addWidget(group)

    def create_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_session)
        buttons_layout.addWidget(save_btn)

        # زر إلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_btn.clicked.connect(self.delete_session)
        buttons_layout.addWidget(delete_btn)

        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)

    def on_tooth_selected(self, tooth_number):
        """عند تحديد سن"""
        self.session_tooth_edit.setText(str(tooth_number))

    def calculate_remaining(self):
        """حساب المبلغ المتبقي - التعامل مع القيم الفارغة"""
        # التعامل مع القيمة الفارغة للدفعة
        payment_value = self.payment_spinbox.value()
        current_payment = int(payment_value) if payment_value > 0 else 0

        # هنا يجب جلب مجموع الدفعات السابقة من قاعدة البيانات
        total_payments = current_payment  # مؤقتاً
        remaining = int(self.cost) - total_payments

        self.total_payments_label.setText(f"{total_payments} ليرة سورية")
        self.remaining_label.setText(f"{remaining} ليرة سورية")

    def save_session(self):
        """حفظ جلسة المعالجة"""
        if not self.session_tooth_edit.text():
            QMessageBox.warning(self, "تحذير", "يرجى تحديد رقم السن")
            return

        if not self.procedure_text.toPlainText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال تفاصيل الإجراء")
            return

        try:
            # الحصول على بيانات الجلسة مع ربطها بالخطة والمريض
            session_data = self.get_session_data()

            # حفظ البيانات في قاعدة البيانات
            if hasattr(self.parent_tab, 'db_handler'):
                session_id = self.parent_tab.db_handler.save_treatment_session(session_data)

                QMessageBox.information(self, "نجح",
                                      f"تم حفظ جلسة المعالجة بنجاح\n"
                                      f"معرف الجلسة: {session_id}")
            else:
                QMessageBox.information(self, "نجح", "تم حفظ جلسة المعالجة بنجاح")

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الجلسة: {str(e)}")

    def delete_session(self):
        """حذف جلسة المعالجة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه الجلسة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # هنا يتم حذف البيانات من قاعدة البيانات
            QMessageBox.information(self, "تم الحذف", "تم حذف الجلسة بنجاح")
            self.accept()

    def get_session_data(self):
        """الحصول على بيانات الجلسة - التعامل مع القيم الفارغة"""
        # التعامل مع القيمة الفارغة للدفعة
        payment_value = self.payment_spinbox.value()
        payment = int(payment_value) if payment_value > 0 else 0

        return {
            'plan_id': self.plan_id,  # معرف خطة المعالجة
            'patient_id': self.patient_id,  # معرف المريض
            'plan_number': self.plan_number,
            'date': self.session_date_edit.date().toString('yyyy-MM-dd'),
            'tooth_number': self.session_tooth_edit.text(),
            'procedure': self.procedure_text.toPlainText(),
            'cost': int(self.cost),
            'payment': payment,
            'remaining': int(self.cost) - payment
        }

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            dialog_size = self.geometry()
            x = (screen.width() - dialog_size.width()) // 2
            y = (screen.height() - dialog_size.height()) // 2
            self.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")

class DentalTreatmentsTab(QWidget):
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.current_patient_id = None
        self.current_plan_id = None  # معرف خطة المعالجة الحالية
        self.init_ui()
        # تطبيق الترقيم التلقائي عند تحميل الواجهة
        self.apply_auto_numbering()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)

        # إنشاء محتوى التبويب
        content_widget = QWidget()
        content_widget.setStyleSheet("background-color: white; border-radius: 8px; padding: 20px;")

        # تخطيط المحتوى الرئيسي
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(20)

        # الجانب الأيمن - المحتوى الرئيسي
        main_content_layout = QVBoxLayout()
        main_content_layout.setSpacing(15)

        # مخطط الأسنان التفاعلي
        self.teeth_chart = CompactTeethChart()
        self.teeth_chart.tooth_selected.connect(self.on_tooth_selected)
        main_content_layout.addWidget(self.teeth_chart)

        # حاوية خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_treatment_options_changed)
        main_content_layout.addWidget(self.treatment_options)

        # حاوية خطة المعالجة السنية
        self.treatment_plan = TreatmentPlanWidget()
        self.treatment_plan.set_db_handler(self.db_handler)
        main_content_layout.addWidget(self.treatment_plan)

        # أزرار التحكم الرئيسية
        self.create_main_control_buttons(main_content_layout)

        content_layout.addLayout(main_content_layout)

        main_layout.addWidget(content_widget)

    def create_main_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم الرئيسية والعرض"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # أزرار التحكم الأساسية
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(self.get_button_style("#28a745", "#218838"))
        save_btn.clicked.connect(self.save_treatment_plan)
        buttons_layout.addWidget(save_btn)

        # زر إلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#6c757d", "#5a6268"))
        cancel_btn.clicked.connect(self.cancel_changes)
        buttons_layout.addWidget(cancel_btn)

        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        delete_btn.clicked.connect(self.delete_treatment_plan)
        buttons_layout.addWidget(delete_btn)

        # زر إضافة خطة معالجة سنية
        add_plan_btn = QPushButton("إضافة خطة معالجة سنية")
        add_plan_btn.setStyleSheet(self.get_button_style("#ffc107", "#e0a800"))
        add_plan_btn.clicked.connect(self.add_new_treatment_plan)
        buttons_layout.addWidget(add_plan_btn)

        # زر إضافة جلسة معالجة سنية
        add_session_btn = QPushButton("إضافة جلسة معالجة سنية")
        add_session_btn.setStyleSheet(self.get_button_style("#007bff", "#0056b3"))
        add_session_btn.clicked.connect(self.add_treatment_session)
        buttons_layout.addWidget(add_session_btn)

        # فاصل بصري
        buttons_layout.addSpacing(20)

        # أزرار العرض (المدمجة من الحاوية اليسرى)
        # زر جدول المعالجات السنية
        view_plans_btn = QPushButton("جدول المعالجات السنية")
        view_plans_btn.setStyleSheet(self.get_button_style("#17a2b8", "#138496"))
        view_plans_btn.clicked.connect(self.view_treatment_plans)
        buttons_layout.addWidget(view_plans_btn)

        # زر جدول الجلسات السنية
        view_sessions_btn = QPushButton("جدول الجلسات السنية")
        view_sessions_btn.setStyleSheet(self.get_button_style("#6f42c1", "#5a32a3"))
        view_sessions_btn.clicked.connect(self.view_treatment_sessions)
        buttons_layout.addWidget(view_sessions_btn)

        # زر تعديل أسعار علاج الأسنان
        edit_prices_btn = QPushButton("تعديل أسعار علاج الأسنان")
        edit_prices_btn.setStyleSheet(self.get_button_style("#fd7e14", "#e8690b"))
        edit_prices_btn.clicked.connect(self.edit_default_prices)
        buttons_layout.addWidget(edit_prices_btn)

        # فاصل بصري آخر
        buttons_layout.addSpacing(20)

        # زر اختيار المريض - الأول من اليمين
        select_patient_btn = QPushButton("اختر مريضاً")
        select_patient_btn.setStyleSheet(self.get_button_style("#9b59b6", "#8e44ad"))
        select_patient_btn.clicked.connect(self.open_patient_selection)
        buttons_layout.addWidget(select_patient_btn)

        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)

    def get_button_style(self, bg_color, hover_color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """

    def on_tooth_selected(self, tooth_number):
        """عند تحديد سن من المخطط"""
        success = self.treatment_plan.set_tooth_number(tooth_number)
        if success:
            # تحديث نص المعالجة بناءً على الخيارات المحددة
            self.update_treatment_text()

    def on_treatment_options_changed(self):
        """عند تغيير خيارات المعالجة"""
        self.update_treatment_text()

    def update_treatment_text(self):
        """تحديث نص المعالجة"""
        selected_options = self.treatment_options.get_selected_options()
        self.treatment_plan.set_treatment_text(selected_options)

    def open_patient_selection(self):
        """فتح نافذة اختيار المريض"""
        self.treatment_plan.open_patient_selection()

    def save_treatment_plan(self):
        """حفظ خطة المعالجة"""
        # التحقق من اختيار المريض أولاً
        selected_patient_id = self.treatment_plan.get_selected_patient_id()
        if not selected_patient_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض أولاً باستخدام زر 'اختر مريضاً'")
            return

        plan_data = self.treatment_plan.get_plan_data()

        # التحقق من صحة البيانات
        if not plan_data['plan_number']:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم خطة المعالجة")
            return

        if not plan_data['tooth_number']:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد رقم السن")
            return

        if not plan_data['treatment'].strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال تفاصيل المعالجة")
            return

        if plan_data['cost'] <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلفة صحيحة")
            return

        try:
            # إضافة معرف المريض إلى بيانات الخطة
            plan_data['patient_id'] = selected_patient_id

            # حفظ البيانات في قاعدة البيانات
            plan_id = self.db_handler.save_treatment_plan(plan_data)

            # حفظ معرف الخطة المحفوظة للاستخدام في الجلسات
            self.current_plan_id = plan_id

            QMessageBox.information(self, "نجح",
                                  f"تم حفظ خطة المعالجة بنجاح\n"
                                  f"رقم الخطة: {plan_data['plan_number']}\n"
                                  f"معرف الخطة: {plan_id}")

            # تحديث جدول خطط المعالجة في الواجهة
            self.refresh_treatment_plans_table()

            self.clear_form()

            # تحديث رقم الخطة التلقائي للخطة التالية
            self.update_plan_number_after_save()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def cancel_changes(self):
        """إلغاء التغييرات"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإلغاء",
            "هل أنت متأكد من إلغاء التغييرات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.clear_form()

    def delete_treatment_plan(self):
        """حذف خطة المعالجة"""
        plan_data = self.treatment_plan.get_plan_data()

        if not plan_data['plan_number']:
            QMessageBox.warning(self, "تحذير", "لا توجد خطة محددة للحذف")
            return

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف خطة المعالجة رقم {plan_data['plan_number']}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # هنا يتم حذف البيانات من قاعدة البيانات
                # self.db_handler.delete_treatment_plan(plan_data['plan_number'])

                QMessageBox.information(self, "تم الحذف", "تم حذف خطة المعالجة بنجاح")
                self.clear_form()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحذف: {str(e)}")

    def add_new_treatment_plan(self):
        """إضافة خطة معالجة جديدة مع ترقيم تلقائي ذكي"""
        try:
            # مسح النموذج
            self.clear_form()

            # تطبيق الترقيم التلقائي الذكي
            self.apply_auto_numbering()

            # تعيين التاريخ الحالي
            self.treatment_plan.date_edit.setDate(QDate.currentDate())

            # الحصول على الرقم المعين للعرض
            current_plan_number = self.treatment_plan.plan_number_edit.text()
            QMessageBox.information(self, "خطة جديدة", f"تم إنشاء خطة معالجة جديدة برقم {current_plan_number}")

        except Exception as e:
            print(f"خطأ في إضافة خطة معالجة جديدة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء خطة جديدة: {str(e)}")

    def get_next_plan_number(self):
        """الحصول على رقم الخطة التالي بنظام ترقيم ذكي"""
        try:
            if self.db_handler and hasattr(self.db_handler, 'get_max_plan_number'):
                # جلب أكبر رقم خطة من قاعدة البيانات الحقيقية
                max_plan_number = self.db_handler.get_max_plan_number()
                if max_plan_number is not None:
                    return max_plan_number + 1
                else:
                    # لا توجد خطط سابقة، ابدأ من 1
                    return 1
            else:
                # استخدام قاعدة البيانات الوهمية للاختبار
                max_plan_number = self.get_max_plan_number_from_mock_db()
                if max_plan_number is not None:
                    return max_plan_number + 1
                else:
                    # لا توجد خطط سابقة، ابدأ من 1
                    return 1
        except Exception as e:
            print(f"خطأ في الحصول على رقم الخطة التالي: {e}")
            # في حالة الخطأ، استخدم ترقيم محلي
            return self.get_local_next_plan_number()

    def get_local_next_plan_number(self):
        """الحصول على رقم الخطة التالي محلياً (للاختبار)"""
        # ترقيم محلي للاختبار عندما لا تتوفر قاعدة البيانات
        if not hasattr(self, '_local_plan_counter'):
            self._local_plan_counter = 1
        else:
            self._local_plan_counter += 1
        return self._local_plan_counter

    def apply_auto_numbering(self):
        """تطبيق الترقيم التلقائي لحقل رقم خطة المعالجة"""
        try:
            # الحصول على رقم الخطة التالي
            next_plan_number = self.get_next_plan_number()

            # تعيين الرقم في حقل رقم الخطة
            if hasattr(self, 'treatment_plan') and hasattr(self.treatment_plan, 'plan_number_edit'):
                self.treatment_plan.plan_number_edit.setText(str(next_plan_number))
                print(f"تم تعيين رقم الخطة التلقائي: {next_plan_number}")
            else:
                print("تحذير: لم يتم العثور على حقل رقم الخطة")

        except Exception as e:
            print(f"خطأ في تطبيق الترقيم التلقائي: {e}")

    def update_plan_number_after_save(self):
        """تحديث رقم الخطة بعد الحفظ بنجاح"""
        try:
            # الحصول على رقم الخطة التالي بعد الحفظ
            next_plan_number = self.get_next_plan_number()

            # تعيين الرقم الجديد في حقل رقم الخطة
            if hasattr(self, 'treatment_plan') and hasattr(self.treatment_plan, 'plan_number_edit'):
                self.treatment_plan.plan_number_edit.setText(str(next_plan_number))
                print(f"تم تحديث رقم الخطة بعد الحفظ: {next_plan_number}")

        except Exception as e:
            print(f"خطأ في تحديث رقم الخطة بعد الحفظ: {e}")

    def simulate_database_operations(self):
        """محاكاة عمليات قاعدة البيانات للاختبار"""
        # إنشاء قاعدة بيانات وهمية للاختبار
        if not hasattr(self, '_mock_database'):
            self._mock_database = {
                'treatment_plans': [
                    {'plan_number': 1, 'tooth_number': '11', 'treatment': 'تنظيف', 'cost': 50000},
                    {'plan_number': 2, 'tooth_number': '12', 'treatment': 'حشوة', 'cost': 75000},
                    {'plan_number': 4, 'tooth_number': '13', 'treatment': 'تاج', 'cost': 200000},
                    # لاحظ أن الرقم 3 محذوف لاختبار التعامل مع الخطط المحذوفة
                ]
            }
        return self._mock_database

    def get_max_plan_number_from_mock_db(self):
        """الحصول على أكبر رقم خطة من قاعدة البيانات الوهمية"""
        mock_db = self.simulate_database_operations()
        if mock_db['treatment_plans']:
            return max(plan['plan_number'] for plan in mock_db['treatment_plans'])
        return None

    def add_treatment_session(self):
        """إضافة جلسة معالجة سنية"""
        # التحقق من اختيار المريض أولاً
        if not self.current_patient_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض من قائمة المرضى أولاً")
            return

        # التحقق من وجود خطة معالجة محفوظة
        if not self.current_plan_id:
            # عرض خيارات للمستخدم
            reply = QMessageBox.question(
                self,
                "خطة المعالجة غير محفوظة",
                "يرجى حفظ خطة المعالجة أولاً أو اختيار خطة موجودة من جدول خطط المعالجة.\n\n"
                "ماذا تريد أن تفعل؟",
                QMessageBox.Save | QMessageBox.Open | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                # حفظ الخطة الحالية
                self.save_treatment_plan()
                # التحقق من نجاح الحفظ
                if not self.current_plan_id:
                    return  # فشل في الحفظ
            elif reply == QMessageBox.Open:
                # فتح جدول خطط المعالجة للاختيار
                self.view_treatment_plans()
                return
            else:
                return  # إلغاء العملية

        plan_data = self.treatment_plan.get_plan_data()

        # فتح نافذة جلسات المعالجة مع معرف الخطة
        dialog = TreatmentSessionDialog(
            plan_id=self.current_plan_id,
            plan_number=plan_data['plan_number'],
            cost=plan_data['cost'],
            patient_id=self.current_patient_id,
            parent=self
        )

        if dialog.exec_() == QDialog.Accepted:
            # تحديث جدول جلسات المعالجة في الواجهة
            self.refresh_treatment_sessions_table()
            QMessageBox.information(self, "نجح", "تم حفظ جلسة المعالجة بنجاح")

    def view_treatment_plans(self):
        """عرض خطط المعالجة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("خطط المعالجة السنية")
        dialog.setModal(True)
        # تعديل الحجم ليطابق حجم واجهة تبويبة علاج الأسنان الرئيسية
        dialog.setGeometry(100, 100, 1400, 900)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة على الشاشة
        self.center_dialog_on_screen(dialog)

        layout = QVBoxLayout(dialog)

        # إنشاء جدول خطط المعالجة
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels([
            "رقم الخطة", "رقم السن", "المعالجة", "الكلفة", "التاريخ", "الحالة"
        ])

        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # هنا يتم جلب البيانات من قاعدة البيانات وعرضها
        # self.load_treatment_plans_data(table)

        layout.addWidget(table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec_()

    def center_dialog_on_screen(self, dialog):
        """توسيط النافذة المنبثقة على الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            dialog_size = dialog.geometry()
            x = (screen.width() - dialog_size.width()) // 2
            y = (screen.height() - dialog_size.height()) // 2
            dialog.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة المنبثقة: {e}")

    def view_treatment_sessions(self):
        """عرض جلسات المعالجة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("جلسات المعالجة السنية")
        dialog.setModal(True)
        # تعديل الحجم ليطابق حجم واجهة تبويبة علاج الأسنان الرئيسية
        dialog.setGeometry(100, 100, 1400, 900)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة على الشاشة
        self.center_dialog_on_screen(dialog)

        layout = QVBoxLayout(dialog)

        # إنشاء جدول جلسات المعالجة
        table = QTableWidget()
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "رقم الخطة", "التاريخ", "رقم السن", "الإجراء", "الكلفة", "الدفعة", "المتبقي"
        ])

        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # هنا يتم جلب البيانات من قاعدة البيانات وعرضها
        # self.load_treatment_sessions_data(table)

        layout.addWidget(table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec_()

    def clear_form(self):
        """مسح النموذج"""
        self.treatment_plan.clear_form()
        self.treatment_options.clear_all_options()

        # إعادة تعيين معرف الخطة الحالية
        self.current_plan_id = None

        # إلغاء تحديد السن من المخطط
        if self.teeth_chart.selected_tooth:
            selected_btn = self.teeth_chart.tooth_buttons[self.teeth_chart.selected_tooth]
            selected_btn.set_selected(False)

    def refresh_treatment_plans_table(self):
        """تحديث جدول خطط المعالجة"""
        try:
            if hasattr(self, 'db_handler') and self.current_patient_id:
                # تحديث جدول خطط المعالجة للمريض الحالي
                plans = self.db_handler.get_treatment_plans_by_patient(self.current_patient_id)
                # هنا يمكن تحديث الجدول في الواجهة
                print(f"تم تحديث جدول خطط المعالجة: {len(plans)} خطة")
        except Exception as e:
            print(f"خطأ في تحديث جدول خطط المعالجة: {e}")

    def refresh_treatment_sessions_table(self):
        """تحديث جدول جلسات المعالجة"""
        try:
            if hasattr(self, 'db_handler') and self.current_plan_id:
                # تحديث جدول جلسات المعالجة للخطة الحالية
                sessions = self.db_handler.get_treatment_sessions_by_plan(self.current_plan_id)
                # هنا يمكن تحديث الجدول في الواجهة
                print(f"تم تحديث جدول جلسات المعالجة: {len(sessions)} جلسة")
        except Exception as e:
            print(f"خطأ في تحديث جدول جلسات المعالجة: {e}")

    def set_current_plan_from_table(self, plan_id):
        """تعيين الخطة الحالية من جدول خطط المعالجة"""
        try:
            if hasattr(self, 'db_handler'):
                plan_data = self.db_handler.get_treatment_plan_by_id(plan_id)
                if plan_data:
                    self.current_plan_id = plan_id
                    # تحديث النموذج ببيانات الخطة المحددة
                    self.treatment_plan.load_plan_data(plan_data)
                    print(f"تم تحديد الخطة: {plan_id}")
        except Exception as e:
            print(f"خطأ في تحديد الخطة: {e}")

    def edit_default_prices(self):
        """فتح نافذة تعديل الأسعار الافتراضية"""
        dialog = QDialog(self)
        dialog.setWindowTitle("تعديل أسعار علاج الأسنان الافتراضية")
        dialog.setFixedSize(1400, 900)
        dialog.setModal(True)

        # توسيط النافذة على الشاشة
        self.center_dialog_on_screen(dialog)

        layout = QVBoxLayout(dialog)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("تعديل الأسعار الافتراضية لعلاج الأسنان")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء حاوية خيارات المعالجة للتعديل
        prices_widget = TreatmentPricesEditWidget()

        # نسخ الأسعار الحالية إلى النافذة
        current_prices = self.treatment_options.get_custom_prices()
        prices_widget.set_prices(current_prices)

        layout.addWidget(prices_widget)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(self.get_button_style("#28a745", "#218838"))
        save_btn.clicked.connect(lambda: self.save_default_prices(dialog, prices_widget))
        buttons_layout.addWidget(save_btn)

        # زر استعادة الافتراضي
        reset_btn = QPushButton("استعادة الافتراضي")
        reset_btn.setStyleSheet(self.get_button_style("#fd7e14", "#e8690b"))
        reset_btn.clicked.connect(lambda: self.reset_to_default_prices(prices_widget))
        buttons_layout.addWidget(reset_btn)

        # زر إلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#6c757d", "#5a6268"))
        cancel_btn.clicked.connect(dialog.reject)
        buttons_layout.addWidget(cancel_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        dialog.exec_()

    def save_default_prices(self, dialog, prices_widget):
        """حفظ الأسعار الجديدة بشكل دائم"""
        try:
            # الحصول على الأسعار المحدثة من النافذة
            new_prices = prices_widget.get_prices()

            # تطبيق الأسعار الجديدة على حاوية خيارات العلاج الرئيسية
            self.treatment_options.set_custom_prices(new_prices)

            # حفظ الأسعار بشكل دائم
            success = self.treatment_options.save_custom_prices_permanently()

            if success:
                # إعادة حساب الكلفة الإجمالية
                self.treatment_options.calculate_total_cost()

                # إغلاق النافذة
                dialog.accept()

                # رسالة تأكيد
                QMessageBox.information(self, "نجح الحفظ",
                                      "تم حفظ الأسعار الافتراضية الجديدة بشكل دائم!\n"
                                      "ستبقى هذه الأسعار محفوظة عند إعادة تشغيل التطبيق.")
            else:
                QMessageBox.warning(self, "تحذير",
                                  "تم تطبيق الأسعار على الجلسة الحالية ولكن فشل في حفظها بشكل دائم.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الأسعار: {str(e)}")

    def reset_to_default_prices(self, prices_widget):
        """استعادة الأسعار الافتراضية الأصلية وحذف الملف المحفوظ"""
        try:
            # استعادة الأسعار الافتراضية في النافذة
            prices_widget.reset_to_defaults()

            # استعادة الأسعار الافتراضية في الواجهة الرئيسية وحذف الملف المحفوظ
            success = self.treatment_options.reset_prices_to_defaults()

            if success:
                # رسالة تأكيد
                QMessageBox.information(self, "تم الاستعادة",
                                      "تم استعادة الأسعار الافتراضية الأصلية!\n"
                                      "تم حذف ملف الأسعار المحفوظة.")
            else:
                QMessageBox.warning(self, "تحذير",
                                  "تم استعادة الأسعار في النافذة ولكن فشل في حذف الملف المحفوظ.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استعادة الأسعار: {str(e)}")


class TreatmentPricesEditWidget(QWidget):
    """واجهة تعديل الأسعار الافتراضية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.price_spinboxes = {}

        # إنشاء مدير الأسعار
        self.prices_manager = PricesManager()

        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة تعديل الأسعار"""
        # تخطيط أفقي للمجموعات الثمانية
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء المجموعات الثمانية بنفس ترتيب الواجهة الرئيسية
        # [لبية] [أسعار اللبية] [ترميمية] [أسعار الترميمية] [تيجان] [أسعار التيجان] [جراحة] [أسعار الجراحة]

        # مجموعة اللبية (للعرض فقط)
        endodontic_display = self.create_display_group("لبية (Endodontic)", [
            "Vital", "Necrotic", "إعادة معالجة", "متكلسة", "C shape", "ذروة مفتوحة", "أداة مكسورة"
        ])
        main_layout.addWidget(endodontic_display)

        # مجموعة أسعار اللبية (للتعديل)
        endodontic_prices = self.create_prices_group("أسعار اللبية", [
            ("Vital", 120000), ("Necrotic", 150000), ("إعادة معالجة", 200000),
            ("متكلسة", 180000), ("C shape", 250000), ("ذروة مفتوحة", 160000), ("أداة مكسورة", 300000)
        ], "endodontic")
        main_layout.addWidget(endodontic_prices)

        # مجموعة الترميمية (للعرض فقط)
        restorative_display = self.create_display_group("ترميمية (Restorative)", [
            "كومبوزت", "أملغم", "GIC", "وتد فايبر", "قلب معدني"
        ])
        main_layout.addWidget(restorative_display)

        # مجموعة أسعار الترميمية (للتعديل)
        restorative_prices = self.create_prices_group("أسعار الترميمية", [
            ("كومبوزت", 75000), ("أملغم", 50000), ("GIC", 40000),
            ("وتد فايبر", 80000), ("قلب معدني", 60000)
        ], "restorative")
        main_layout.addWidget(restorative_prices)

        # مجموعة التيجان (للعرض فقط)
        crowns_display = self.create_display_group("تيجان (Crowns)", [
            "خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس", "زيركون مغطى خزف",
            "زيركون cutback", "ستانلس", "إيماكس"
        ])
        main_layout.addWidget(crowns_display)

        # مجموعة أسعار التيجان (للتعديل)
        crowns_prices = self.create_prices_group("أسعار التيجان", [
            ("خزف معدن", 150000), ("زيركون 4D", 200000), ("زيركون مغطى إيماكس", 250000),
            ("زيركون مغطى خزف", 220000), ("زيركون cutback", 230000), ("ستانلس", 80000), ("إيماكس", 180000)
        ], "crowns")
        main_layout.addWidget(crowns_prices)

        # مجموعة الجراحة (للعرض فقط) - الخيارات الأصلية
        surgery_display = self.create_display_group("جراحة (Surgery)", [
            "قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة", "تطويل تاج", "قطع ذروة", "تضحيك"
        ])
        main_layout.addWidget(surgery_display)

        # مجموعة أسعار الجراحة (للتعديل) - الأسعار الأصلية
        surgery_prices = self.create_prices_group("أسعار الجراحة", [
            ("قلع بسيط", 30000), ("قلع جراحي", 75000), ("منحصرة", 100000),
            ("منطمرة", 120000), ("تطويل تاج", 80000), ("قطع ذروة", 90000), ("تضحيك", 150000)
        ], "surgery")
        main_layout.addWidget(surgery_prices)

        # تعيين نسب التمدد المتساوية لجميع المجموعات
        for i in range(8):
            main_layout.setStretch(i, 1)

    def create_display_group(self, title, options):
        """إنشاء مجموعة عرض للخيارات (للعرض فقط)"""
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-size: 12px;
                font-weight: bold;
                color: #007bff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #f8f9fa;
            }
        """)
        group.setMinimumWidth(160)
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للخيارات
        options_widget = QWidget()
        options_layout = QVBoxLayout(options_widget)
        options_layout.setSpacing(4)
        options_layout.setContentsMargins(0, 0, 0, 0)
        options_layout.setAlignment(Qt.AlignLeft)

        # إضافة الخيارات كتسميات
        for option in options:
            label = QLabel(f"• {option}")
            label.setStyleSheet("""
                QLabel {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    color: #495057;
                    min-height: 22px;
                }
            """)
            options_layout.addWidget(label, 0, Qt.AlignLeft)

        main_layout.addWidget(options_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_prices_group(self, title, price_options, category):
        """إنشاء مجموعة أسعار قابلة للتعديل"""
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-size: 12px;
                font-weight: bold;
                color: #28a745;
                border: 2px solid #28a745;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8fff8;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #f8fff8;
            }
        """)
        group.setMinimumWidth(160)
        group.setMinimumHeight(220)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # إنشاء حقول الأسعار
        for option, default_price in price_options:
            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(default_price)
            price_spinbox.setSuffix(" ل.س")
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #28a745;
                    border-radius: 4px;
                    background-color: white;
                }
                QSpinBox:focus {
                    border: 2px solid #28a745;
                }
            """)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"{category}_{option}"] = price_spinbox

        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def get_prices(self):
        """الحصول على جميع الأسعار المحدثة"""
        prices = {}
        for key, spinbox in self.price_spinboxes.items():
            prices[key] = spinbox.value()
        return prices

    def set_prices(self, prices):
        """تعيين الأسعار"""
        try:
            for key, price in prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)
        except Exception as e:
            print(f"خطأ في تعيين الأسعار: {e}")

    def reset_to_defaults(self):
        """إعادة تعيين الأسعار الافتراضية الأصلية"""
        try:
            # الحصول على الأسعار الافتراضية من مدير الأسعار
            default_prices = self.prices_manager.get_default_prices()

            # تطبيق الأسعار الافتراضية
            self.set_prices(default_prices)

        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار الافتراضية: {e}")
            self.teeth_chart.selected_tooth = None

    def set_current_patient(self, patient_id):
        """تعيين المريض الحالي"""
        self.current_patient_id = patient_id
        # يمكن هنا تحديث البيانات المعروضة حسب المريض