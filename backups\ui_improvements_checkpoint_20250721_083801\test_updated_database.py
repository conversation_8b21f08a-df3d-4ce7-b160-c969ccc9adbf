#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار قاعدة البيانات المحدثة والمطورة
Test updated and enhanced database
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.db_handler import DatabaseHandler
from datetime import datetime, date

def test_database_structure():
    """اختبار بنية قاعدة البيانات الجديدة"""
    print("🔧 اختبار بنية قاعدة البيانات المحدثة...")
    
    # إنشاء قاعدة بيانات اختبار
    db = DatabaseHandler('test_updated_clinic.db')
    
    try:
        # إنشاء الجداول
        if db.create_tables():
            print("✅ تم إنشاء جميع الجداول بنجاح")
        else:
            print("❌ فشل في إنشاء الجداول")
            return False
        
        # اختبار إضافة مريض
        patient_id = db.add_patient(
            name="أحمد محمد علي",
            birth_year=1985,
            mobile="**********",
            whatsapp="**********",
            general_diseases="ضغط الدم",
            medications="أسبرين",
            notes="مريض منتظم"
        )
        
        if patient_id:
            print(f"✅ تم إضافة مريض بنجاح - ID: {patient_id}")
        else:
            print("❌ فشل في إضافة المريض")
            return False
        
        # اختبار الحصول على أنواع المعالجات
        treatment_types = db.get_all_treatment_types()
        print(f"✅ تم جلب {len(treatment_types)} نوع معالجة")
        
        # عرض بعض أنواع المعالجات
        print("\n📋 أنواع المعالجات المتاحة:")
        for i, treatment_type in enumerate(treatment_types[:5]):
            print(f"   {i+1}. {treatment_type['name']} - {treatment_type['category']} - {treatment_type['default_cost']:,} ل.س")
        
        # اختبار الترقيم التلقائي
        max_plan_number = db.get_max_plan_number()
        next_plan_number = max_plan_number + 1
        print(f"✅ أكبر رقم خطة: {max_plan_number}, الرقم التالي: {next_plan_number}")
        
        # اختبار إضافة خطة معالجة
        plan_id = db.add_treatment_plan(
            patient_id=patient_id,
            plan_number=next_plan_number,
            treatment_type_id=1,  # أول نوع معالجة
            tooth_number="11",
            treatment_description="حشوة تجميلية للسن الأمامي",
            cost=75000,
            plan_date=date.today().isoformat(),
            notes="خطة معالجة تجريبية"
        )
        
        if plan_id:
            print(f"✅ تم إضافة خطة معالجة بنجاح - ID: {plan_id}")
        else:
            print("❌ فشل في إضافة خطة المعالجة")
            return False
        
        # اختبار إضافة جلسة معالجة
        session_id = db.add_treatment_session(
            treatment_plan_id=plan_id,
            session_date=date.today().isoformat(),
            tooth_number="11",
            procedure_description="تحضير السن وأخذ الطبعة",
            payment=25000,
            notes="جلسة أولى"
        )
        
        if session_id:
            print(f"✅ تم إضافة جلسة معالجة بنجاح - ID: {session_id}")
        else:
            print("❌ فشل في إضافة جلسة المعالجة")
            return False
        
        # اختبار جلب خطط المعالجة للمريض
        patient_plans = db.get_treatment_plans_by_patient(patient_id)
        print(f"✅ تم جلب {len(patient_plans)} خطة معالجة للمريض")
        
        # اختبار جلب جلسات المعالجة للخطة
        plan_sessions = db.get_treatment_sessions_by_plan(plan_id)
        print(f"✅ تم جلب {len(plan_sessions)} جلسة معالجة للخطة")
        
        # اختبار حساب إجمالي المدفوعات
        total_payments = db.get_total_payments(plan_id)
        print(f"✅ إجمالي المدفوعات: {total_payments:,} ل.س")
        
        print("\n🎉 جميع اختبارات قاعدة البيانات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False
    finally:
        db.close()

def test_treatment_types_by_category():
    """اختبار جلب أنواع المعالجات حسب الفئة"""
    print("\n🔍 اختبار جلب أنواع المعالجات حسب الفئة...")
    
    db = DatabaseHandler('test_updated_clinic.db')
    
    try:
        categories = ['علاج أسنان', 'تيجان وجسور', 'أطفال', 'تقويم', 'زراعة']
        
        for category in categories:
            types = db.get_treatment_types_by_category(category)
            print(f"📂 {category}: {len(types)} نوع معالجة")
            for treatment_type in types[:3]:  # عرض أول 3 أنواع
                print(f"   - {treatment_type['name']}: {treatment_type['default_cost']:,} ل.س")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الفئات: {e}")
        return False
    finally:
        db.close()

def test_data_integrity():
    """اختبار تكامل البيانات والعلاقات"""
    print("\n🔗 اختبار تكامل البيانات والعلاقات...")
    
    db = DatabaseHandler('test_updated_clinic.db')
    
    try:
        # جلب مريض مع خطط المعالجة
        patients = db.get_all_patients()
        if patients:
            patient = patients[0]
            patient_id = patient['id']
            
            # جلب خطط المعالجة للمريض
            plans = db.get_treatment_plans_by_patient(patient_id)
            print(f"👤 المريض: {patient['name']}")
            print(f"📋 عدد خطط المعالجة: {len(plans)}")
            
            for plan in plans:
                print(f"   📝 خطة رقم {plan['plan_number']}: {plan['treatment_description']}")
                print(f"      💰 الكلفة: {plan['cost']:,} ل.س")
                
                # جلب جلسات المعالجة للخطة
                sessions = db.get_treatment_sessions_by_plan(plan['id'])
                print(f"      🏥 عدد الجلسات: {len(sessions)}")
                
                total_payments = db.get_total_payments(plan['id'])
                remaining = plan['cost'] - total_payments
                print(f"      💳 المدفوع: {total_payments:,} ل.س")
                print(f"      💸 المتبقي: {remaining:,} ل.س")
        
        print("✅ تكامل البيانات سليم")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكامل البيانات: {e}")
        return False
    finally:
        db.close()

def display_database_summary():
    """عرض ملخص قاعدة البيانات المحدثة"""
    print("\n" + "="*60)
    print("📊 ملخص قاعدة البيانات المحدثة والمطورة")
    print("="*60)
    
    print("""
🎯 التحسينات المطبقة على قاعدة البيانات:

✅ بنية الجداول المحدثة:
   • جدول المرضى (patients) - محفوظ مع تحسينات
   • جدول أنواع المعالجات (treatment_types) - محدث بالكامل
   • جدول خطط المعالجة (treatment_plans) - محدث بالكامل
   • جدول جلسات المعالجة (treatment_sessions) - محدث بالكامل

🔧 التحسينات التقنية:
   • استخدام أرقام صحيحة (INTEGER) بدلاً من REAL للقيم المالية
   • دعم الحد الأقصى الجديد (999,999,999) للقيم المالية
   • ترقيم تلقائي ذكي لخطط المعالجة (plan_number)
   • ربط محسن بين الجداول مع Foreign Keys

📋 أنواع المعالجات المدعومة:
   • علاج أسنان عام (حشوات، علاج عصب، خلع، تنظيف)
   • تيجان وجسور (زيركون، معدني خزفي، جسور متعددة)
   • طب أسنان الأطفال (حشوات أطفال، خلع أسنان لبنية، فلورايد)
   • تقويم الأسنان (معدني، شفاف، أجهزة متحركة)
   • زراعة الأسنان (زرعات مفردة، زرعات مع تيجان)
   • أطقم متحركة (كاملة، جزئية)
   • فينير وتجميل (قشور خزفية، تبييض)

🔗 العلاقات بين الجداول:
   • مريض واحد ← عدة خطط معالجة
   • نوع معالجة واحد ← عدة خطط معالجة
   • خطة معالجة واحدة ← عدة جلسات معالجة

⚡ الوظائف الجديدة:
   • get_max_plan_number() - للترقيم التلقائي
   • get_treatment_plans_by_patient() - جلب خطط المريض
   • get_treatment_plans_by_type() - جلب خطط حسب النوع
   • get_treatment_sessions_by_plan() - جلب جلسات الخطة
   • get_all_treatment_types() - جلب جميع أنواع المعالجات
   • get_treatment_types_by_category() - جلب أنواع حسب الفئة

💾 دعم التكامل:
   • ربط تلقائي بالمريض المختار
   • حفظ واسترجاع البيانات المحدثة
   • دعم القيم الفارغة والأرقام الصحيحة
   • حسابات مالية دقيقة
    """)

def main():
    """تشغيل جميع اختبارات قاعدة البيانات"""
    print("🚀 بدء اختبار قاعدة البيانات المحدثة والمطورة")
    print("="*60)
    
    # تشغيل الاختبارات
    tests = [
        test_database_structure,
        test_treatment_types_by_category,
        test_data_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print("-" * 40)
    
    # عرض النتائج
    print(f"\n📈 نتائج الاختبارات: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع اختبارات قاعدة البيانات نجحت!")
        display_database_summary()
    else:
        print("⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء")
    
    # تنظيف ملف الاختبار
    try:
        if os.path.exists('test_updated_clinic.db'):
            os.remove('test_updated_clinic.db')
            print("\n🧹 تم حذف ملف قاعدة البيانات التجريبي")
    except:
        pass

if __name__ == "__main__":
    main()
