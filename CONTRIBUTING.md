# المساهمة في نظام إدارة العيادة السنية

نحن نرحب بمساهماتكم في تطوير نظام إدارة العيادة السنية! هذا الملف يوضح كيفية المساهمة في المشروع.

## كيفية المساهمة

1. قم بعمل Fork للمستودع
2. قم بإنشاء فرع جديد لميزتك (`git checkout -b feature/amazing-feature`)
3. قم بإجراء تغييراتك وتوثيقها
4. قم بعمل Commit للتغييرات (`git commit -m 'إضافة ميزة رائعة'`)
5. قم بدفع الفرع إلى المستودع الخاص بك (`git push origin feature/amazing-feature`)
6. قم بفتح طلب Pull Request

## معايير الكود

- اتبع معايير PEP 8 لكتابة كود Python
- قم بتوثيق الدوال والفئات باستخدام docstrings
- اكتب تعليقات توضيحية باللغة العربية
- قم بكتابة اختبارات للميزات الجديدة

## الإبلاغ عن الأخطاء

إذا وجدت خطأً، يرجى فتح Issue جديدة تتضمن:

- وصف مفصل للمشكلة
- خطوات إعادة إنتاج المشكلة
- النتيجة المتوقعة والنتيجة الفعلية
- معلومات عن بيئة التشغيل (نظام التشغيل، إصدار Python، إلخ)

## اقتراح ميزات جديدة

nإذا كان لديك اقتراح لميزة جديدة، يرجى فتح Issue جديدة تتضمن:

- وصف مفصل للميزة المقترحة
- سبب أهمية هذه الميزة
- كيفية تنفيذها (إذا كان لديك فكرة)

## الترخيص

بالمساهمة في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة بموجب [رخصة MIT](LICENSE).