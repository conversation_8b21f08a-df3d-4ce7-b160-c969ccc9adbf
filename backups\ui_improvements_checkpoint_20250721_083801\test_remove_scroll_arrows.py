#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إزالة أسهم التمرير من حقول خطة المعالجة
Test removing scroll arrows from treatment plan fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class RemoveScrollArrowsTestWindow(QMainWindow):
    """نافذة اختبار إزالة أسهم التمرير"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار إزالة أسهم التمرير من حقول خطة المعالجة")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار إزالة أسهم التمرير من حقول خطة المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 إزالة أسهم التمرير من حقول خطة المعالجة:
        
        📝 1. حقل تفاصيل المعالجة (QTextEdit):
        • إزالة أسهم التمرير العمودية: setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        • إزالة أسهم التمرير الأفقية: setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        • الحفاظ على جميع الوظائف الأساسية: الإدخال، التحرير، التحديد
        • الحفاظ على الحد الأقصى للارتفاع (60px) والعرض الأدنى (200px)
        • الحفاظ على النص التوضيحي "تفاصيل المعالجة"
        
        📅 2. حقل التاريخ (QDateEdit):
        • إزالة أسهم التحكم: setButtonSymbols(QDateEdit.NoButtons) (تم مسبقاً)
        • الحفاظ على وظيفة التقويم المنبثق (calendar popup)
        • الحفاظ على التاريخ التلقائي (تاريخ اليوم الحالي)
        • الحفاظ على العرض الثابت (120px)
        
        💰 3. حقل الكلفة (QDoubleSpinBox):
        • إزالة أسهم التحكم: setButtonSymbols(QDoubleSpinBox.NoButtons) (تم مسبقاً)
        • الحفاظ على جميع الوظائف: الإدخال، التحرير، الحد الأقصى
        • الحفاظ على اللاحقة "ل.س" والعرض الثابت (120px)
        
        ✅ الاعتبارات التقنية المحققة:
        • الحفاظ على جميع الوظائف الأساسية للحقول
        • عدم التأثير على التنسيق والمظهر الحالي
        • إزالة عناصر التحكم البصرية غير المرغوب فيها
        • الحفاظ على سهولة الاستخدام والتفاعل
        
        🎨 النتائج المحققة:
        • مظهر أنظف وأكثر بساطة للحقول
        • إزالة العناصر المربكة أو غير الضرورية
        • تحسين تجربة المستخدم
        • الحفاظ على جميع الوظائف الأساسية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع إزالة أسهم التمرير
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (بدون أسهم تمرير)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار إزالة أسهم التمرير:
        
        🔍 ما يجب ملاحظته:
        • حقل تفاصيل المعالجة بدون أسهم تمرير عمودية أو أفقية
        • حقل التاريخ بدون أسهم زيادة/نقصان (لكن مع تقويم منبثق)
        • حقل الكلفة بدون أسهم زيادة/نقصان
        • جميع الحقول تعمل بشكل طبيعي للإدخال والتحرير
        • مظهر أنظف وأكثر بساطة للواجهة
        
        🧪 اختبارات يمكن إجراؤها:
        • اكتب نص طويل في حقل تفاصيل المعالجة وتحقق من عدم ظهور أسهم تمرير
        • انقر على حقل التاريخ وتحقق من ظهور التقويم المنبثق (بدون أسهم)
        • اكتب قيم مختلفة في حقل الكلفة (بدون أسهم زيادة/نقصان)
        • تحقق من أن جميع الحقول تقبل الإدخال والتحرير بشكل طبيعي
        • لاحظ المظهر الأنظف للواجهة
        
        ✅ النتائج المتوقعة:
        • عدم ظهور أسهم تمرير في حقل تفاصيل المعالجة
        • عدم ظهور أسهم تحكم في حقول التاريخ والكلفة
        • عمل جميع الوظائف الأساسية بشكل صحيح
        • مظهر أنظف وأكثر احترافية
        • تجربة مستخدم محسنة
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم إزالة أسهم التمرير من حقول خطة المعالجة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = RemoveScrollArrowsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
