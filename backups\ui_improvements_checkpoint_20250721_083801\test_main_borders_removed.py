#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حذف الحدود الخارجية الرئيسية من حاويات واجهة علاج الأسنان
Test removal of main outer borders from dental treatment interface containers
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QScrollArea
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, CompactTeethChart, TreatmentOptionsWidget, TreatmentPlanWidget

class MainBordersRemovedTestWindow(QMainWindow):
    """نافذة اختبار حذف الحدود الخارجية الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار حذف الحدود الخارجية الرئيسية")
        self.setGeometry(100, 100, 1300, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار حذف الحدود الخارجية الرئيسية فقط")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة - حذف الحدود الخارجية الرئيسية فقط:
        
        ❌ تم حذف الحدود الخارجية الرئيسية:
        • حذف "border: 2px solid #007bff" من مخطط الأسنان التفاعلي
        • حذف "border: 2px solid #007bff" من خيارات المعالجة
        • حذف "border: 2px solid #007bff" من خطة المعالجة السنية
        
        ✅ تم الحفاظ على جميع العناصر الأخرى:
        • "border-radius: 8px" (الزوايا المدورة)
        • "background-color: #f8f9ff" (ألوان الخلفية)
        • جميع تنسيقات العناوين والنصوص
        • "border: 2px solid #dee2e6" (الحدود الداخلية للمجموعات الفرعية)
        • "border: 1px solid #dee2e6" (حدود عناوين المجموعات الفرعية)
        • جميع الوظائف والتفاعل
        
        📋 النتيجة: إزالة الحدود الخارجية فقط مع الحفاظ على التنسيق الداخلي
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 12px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إنشاء منطقة تمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى الاختبار
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # اختبار مخطط الأسنان بدون حد خارجي
        teeth_title = QLabel("🦷 مخطط الأسنان التفاعلي (بدون حد خارجي)")
        teeth_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 8px;
                background-color: #fadbd8;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(teeth_title)
        
        self.teeth_chart = CompactTeethChart()
        content_layout.addWidget(self.teeth_chart)
        
        # اختبار خطة المعالجة بدون حد خارجي
        plan_title = QLabel("📋 خطة المعالجة السنية (بدون حد خارجي)")
        plan_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #f39c12;
                padding: 8px;
                background-color: #fef9e7;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(plan_title)
        
        self.treatment_plan = TreatmentPlanWidget()
        content_layout.addWidget(self.treatment_plan)
        
        # اختبار خيارات المعالجة بدون حد خارجي
        options_title = QLabel("⚙️ خيارات المعالجة (بدون حد خارجي + حدود داخلية محفوظة)")
        options_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(options_title)
        
        self.treatment_options = TreatmentOptionsWidget()
        content_layout.addWidget(self.treatment_options)
        
        # معلومات التفاصيل
        details_label = QLabel("""
        📝 تفاصيل التغييرات:
        
        الحدود المحذوفة:
        • "border: 2px solid #007bff" من الحاويات الرئيسية الثلاث
        
        العناصر المحفوظة في الحاويات الرئيسية:
        • "border-radius: 8px" - الزوايا المدورة
        • "background-color: #f8f9ff" - لون الخلفية الفاتح
        • "margin-top: 8px" - الهامش العلوي
        • "padding-top: 8px" - الحشو العلوي
        • جميع تنسيقات العناوين والألوان
        
        العناصر المحفوظة في المجموعات الفرعية:
        • "border: 2px solid #dee2e6" - الحدود الرمادية
        • "border-radius: 8px" - الزوايا المدورة
        • "background-color: #ffffff" - خلفية بيضاء
        • "border: 1px solid #dee2e6" - حدود العناوين
        • "background-color: #f8f9fa" - خلفية العناوين
        
        📊 النتيجة الإجمالية:
        • إزالة الحدود الخارجية المشتتة
        • الحفاظ على التنظيم الداخلي
        • تحسين التركيز على المحتوى
        • الحفاظ على جميع الوظائف
        """)
        details_label.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 12px;
                border-radius: 8px;
                font-size: 10px;
                color: #2980b9;
                border: 1px solid #3498db;
                line-height: 1.3;
            }
        """)
        content_layout.addWidget(details_label)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم حذف الحدود الخارجية الرئيسية فقط بنجاح مع الحفاظ على التنسيق الداخلي!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = MainBordersRemovedTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
