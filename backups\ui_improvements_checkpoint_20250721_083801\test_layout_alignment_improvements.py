#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات التخطيط والمحاذاة في واجهة علاج الأسنان
Test layout and alignment improvements in dental treatment interface
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QScrollArea
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget, TreatmentPlanWidget

class LayoutAlignmentTestWindow(QMainWindow):
    """نافذة اختبار تحسينات التخطيط والمحاذاة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تحسينات التخطيط والمحاذاة")
        self.setGeometry(100, 100, 1300, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تحسينات التخطيط والمحاذاة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة على التخطيط والمحاذاة:
        
        📋 تحسين حاوية خيارات المعالجة (TreatmentOptionsWidget):
        • توسيط محاذاة عناوين المجموعات الأربعة باستخدام setAlignment(Qt.AlignCenter)
        • توسيط محاذاة جميع مربعات الاختيار داخل كل مجموعة
        • تطبيق layout.setAlignment(Qt.AlignCenter) لمحتوى المجموعات
        • تطبيق layout.addWidget(checkbox, 0, Qt.AlignCenter) لكل مربع اختيار
        
        📝 تحسين حاوية خطة المعالجة السنية (TreatmentPlanWidget):
        • حذف علامة النقطتين ":" من جميع عناوين الحقول:
          - "رقم الخطة:" → "رقم الخطة"
          - "رقم السن:" → "رقم السن"
          - "المعالجة السنية:" → "المعالجة السنية"
          - "الكلفة:" → "الكلفة"
          - "التاريخ:" → "التاريخ"
        • توسيط محاذاة نصوص عناوين الحقول باستخدام setAlignment(Qt.AlignCenter)
        • توسيط محاذاة المحتوى داخل حقول الإدخال والعرض
        
        ✅ النتائج المحققة:
        • مظهر أكثر تناسقاً واحترافية
        • تنظيم بصري محسن للعناصر
        • محاذاة موحدة عبر جميع المكونات
        • الحفاظ على تنسيق RTL للنصوص العربية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إنشاء منطقة تمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى الاختبار
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(25)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # اختبار خيارات المعالجة مع المحاذاة المحسنة
        options_title = QLabel("⚙️ خيارات المعالجة (محاذاة محسنة)")
        options_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(options_title)
        
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        content_layout.addWidget(self.treatment_options)
        
        # اختبار خطة المعالجة مع العناوين المحسنة
        plan_title = QLabel("📋 خطة المعالجة السنية (عناوين محسنة بدون نقطتين)")
        plan_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #f39c12;
                padding: 8px;
                background-color: #fef9e7;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(plan_title)
        
        self.treatment_plan = TreatmentPlanWidget()
        content_layout.addWidget(self.treatment_plan)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 10px;
                border-radius: 5px;
                font-size: 11px;
                color: #2980b9;
                border: 1px solid #3498db;
            }
        """)
        content_layout.addWidget(self.selected_info)
        
        # معلومات التفاصيل
        details_label = QLabel("""
        📝 تفاصيل التحسينات المطبقة:
        
        خيارات المعالجة:
        • group.setAlignment(Qt.AlignCenter) - توسيط عناوين المجموعات
        • layout.setAlignment(Qt.AlignCenter) - توسيط محتوى المجموعات
        • layout.addWidget(checkbox, 0, Qt.AlignCenter) - توسيط مربعات الاختيار
        
        خطة المعالجة:
        • حذف ":" من جميع العناوين
        • label.setAlignment(Qt.AlignCenter) - توسيط عناوين الحقول
        • widget.setAlignment(Qt.AlignCenter) - توسيط محتوى الحقول
        
        📊 النتيجة الإجمالية:
        • تنظيم بصري محسن ومتناسق
        • محاذاة موحدة عبر جميع العناصر
        • مظهر أكثر احترافية ونظافة
        • الحفاظ على جميع الوظائف الأصلية
        """)
        details_label.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 12px;
                border-radius: 8px;
                font-size: 10px;
                color: #2980b9;
                border: 1px solid #3498db;
                line-height: 1.3;
            }
        """)
        content_layout.addWidget(details_label)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تحسين التخطيط والمحاذاة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = LayoutAlignmentTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
