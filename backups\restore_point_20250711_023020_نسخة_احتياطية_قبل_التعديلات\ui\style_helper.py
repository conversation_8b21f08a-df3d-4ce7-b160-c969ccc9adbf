"""
مساعد الأنماط - لتطبيق الأنماط الحديثة على عناصر الواجهة
مستوحى من Bootstrap و Material Design
"""

from PyQt5.QtWidgets import (QPushButton, QLineEdit, QTextEdit, QComboBox, QTableWidget,
                             QFrame, QLabel, QGroupBox, QWidget, QVBoxLayout, QHBoxLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPalette

class StyleHelper:
    """مساعد لتطبيق الأنماط الحديثة مستوحى من Bootstrap"""

    # ألوان Bootstrap
    COLORS = {
        'primary': '#007bff',
        'secondary': '#6c757d',
        'success': '#28a745',
        'danger': '#dc3545',
        'warning': '#ffc107',
        'info': '#17a2b8',
        'light': '#f8f9fa',
        'dark': '#343a40',
        'white': '#ffffff',
        'muted': '#6c757d'
    }
    
    @staticmethod
    def apply_button_style(button: QPushButton, style_class: str = "primary"):
        """تطبيق نمط الزر الحديث"""
        button.setProperty("class", style_class)
        button.style().unpolish(button)
        button.style().polish(button)
    
    @staticmethod
    def apply_primary_button(button: QPushButton):
        """تطبيق نمط الزر الأساسي"""
        StyleHelper.apply_button_style(button, "primary")
    
    @staticmethod
    def apply_success_button(button: QPushButton):
        """تطبيق نمط زر النجاح"""
        StyleHelper.apply_button_style(button, "success")
    
    @staticmethod
    def apply_warning_button(button: QPushButton):
        """تطبيق نمط زر التحذير"""
        StyleHelper.apply_button_style(button, "warning")
    
    @staticmethod
    def apply_error_button(button: QPushButton):
        """تطبيق نمط زر الخطأ"""
        StyleHelper.apply_button_style(button, "error")
    
    @staticmethod
    def apply_flat_button(button: QPushButton):
        """تطبيق نمط الزر المسطح"""
        StyleHelper.apply_button_style(button, "flat")
    
    @staticmethod
    def apply_form_control_style(widget):
        """تطبيق نمط حقول الإدخال"""
        widget.setProperty("class", "form-control")
        widget.style().unpolish(widget)
        widget.style().polish(widget)
    
    @staticmethod
    def apply_table_style(table: QTableWidget, striped: bool = True, hover: bool = True):
        """تطبيق نمط الجدول"""
        classes = ["table"]
        if striped:
            classes.append("table-striped")
        if hover:
            classes.append("table-hover")
        
        table.setProperty("class", " ".join(classes))
        table.setAlternatingRowColors(striped)
        table.style().unpolish(table)
        table.style().polish(table)
    
    @staticmethod
    def apply_card_style(frame: QFrame, card_type: str = "card"):
        """تطبيق نمط البطاقة الحديثة"""
        frame.setProperty("class", card_type)
        frame.style().unpolish(frame)
        frame.style().polish(frame)
    
    @staticmethod
    def apply_card_header_style(frame: QFrame):
        """تطبيق نمط رأس البطاقة"""
        frame.setProperty("class", "card-header")
        frame.style().unpolish(frame)
        frame.style().polish(frame)
    
    @staticmethod
    def apply_title_style(label: QLabel, title_type: str = "title"):
        """تطبيق نمط العنوان"""
        label.setProperty("class", title_type)
        label.style().unpolish(label)
        label.style().polish(label)
    
    @staticmethod
    def create_primary_button(text: str, icon: str = "") -> QPushButton:
        """إنشاء زر أساسي"""
        button = QPushButton(f"{icon} {text}".strip())
        StyleHelper.apply_button_style(button, "primary")
        return button
    
    @staticmethod
    def create_success_button(text: str, icon: str = "") -> QPushButton:
        """إنشاء زر نجاح"""
        button = QPushButton(f"{icon} {text}".strip())
        StyleHelper.apply_button_style(button, "success")
        return button
    
    @staticmethod
    def create_warning_button(text: str, icon: str = "") -> QPushButton:
        """إنشاء زر تحذير"""
        button = QPushButton(f"{icon} {text}".strip())
        StyleHelper.apply_button_style(button, "warning")
        return button
    
    @staticmethod
    def create_error_button(text: str, icon: str = "") -> QPushButton:
        """إنشاء زر خطأ"""
        button = QPushButton(f"{icon} {text}".strip())
        StyleHelper.apply_button_style(button, "error")
        return button
    
    @staticmethod
    def create_flat_button(text: str, icon: str = "") -> QPushButton:
        """إنشاء زر مسطح"""
        button = QPushButton(f"{icon} {text}".strip())
        StyleHelper.apply_button_style(button, "flat")
        return button
    
    @staticmethod
    def create_form_input(placeholder: str = "") -> QLineEdit:
        """إنشاء حقل إدخال"""
        input_field = QLineEdit()
        if placeholder:
            input_field.setPlaceholderText(placeholder)
        StyleHelper.apply_form_control_style(input_field)
        return input_field
    
    @staticmethod
    def create_form_combo(items: list = None) -> QComboBox:
        """إنشاء قائمة منسدلة محسنة"""
        combo = QComboBox()
        if items:
            combo.addItems(items)
        StyleHelper.apply_form_control_style(combo)
        StyleHelper.fix_combobox_behavior(combo)
        return combo

    @staticmethod
    def fix_combobox_behavior(combo: QComboBox):
        """إصلاح سلوك القائمة المنسدلة لمنع الاختفاء"""
        # إعدادات لحل مشكلة الاختفاء
        combo.setFocusPolicy(Qt.StrongFocus)
        combo.setInsertPolicy(QComboBox.NoInsert)

        # تطبيق تنسيق محسن
        combo.setStyleSheet("""
            QComboBox {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px 12px;
                color: #495057;
                font-size: 14px;
                min-height: 20px;
                padding-right: 30px;
            }
            QComboBox:hover {
                border-color: #007bff;
                background-color: #f8f9fa;
            }
            QComboBox:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
                outline: none;
            }
            QComboBox:on {
                border-color: #007bff;
                background-color: #e3f2fd;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 30px;
                border-left: 1px solid #dee2e6;
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                background-color: #f8f9fa;
            }
            QComboBox::drop-down:hover {
                background-color: #e9ecef;
            }
            QComboBox::down-arrow {
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-top: 2px;
            }
            QComboBox::down-arrow:on {
                border-top-color: #007bff;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #007bff;
                border-radius: 4px;
                selection-background-color: #007bff;
                selection-color: white;
                padding: 4px;
                outline: none;
                show-decoration-selected: 1;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                min-height: 20px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e3f2fd;
                color: #007bff;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # إعدادات إضافية لتحسين الأداء
        combo.view().setAlternatingRowColors(False)
        combo.setMaxVisibleItems(10)

    @staticmethod
    def fix_all_comboboxes(widget):
        """إصلاح جميع القوائم المنسدلة في widget"""
        combo_boxes = widget.findChildren(QComboBox)
        for combo in combo_boxes:
            StyleHelper.fix_combobox_behavior(combo)
    
    @staticmethod
    def create_modern_table(headers: list, striped: bool = True, hover: bool = True) -> QTableWidget:
        """إنشاء جدول حديث"""
        table = QTableWidget()
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        StyleHelper.apply_table_style(table, striped, hover)
        
        # إعدادات إضافية للجدول
        table.verticalHeader().setVisible(False)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        return table
    
    @staticmethod
    def create_card_frame() -> QFrame:
        """إنشاء إطار كارت"""
        frame = QFrame()
        StyleHelper.apply_card_style(frame)
        return frame
    
    @staticmethod
    def create_card_header_frame() -> QFrame:
        """إنشاء رأس الكارت"""
        frame = QFrame()
        StyleHelper.apply_card_style(frame, "card-header")
        return frame
    
    @staticmethod
    def create_card_body_frame() -> QFrame:
        """إنشاء جسم الكارت"""
        frame = QFrame()
        StyleHelper.apply_card_style(frame, "card-body")
        return frame
    
    @staticmethod
    def create_card_footer_frame() -> QFrame:
        """إنشاء تذييل الكارت"""
        frame = QFrame()
        StyleHelper.apply_card_style(frame, "card-footer")
        return frame

    @staticmethod
    def create_title_label(text: str, level: int = 1) -> QLabel:
        """إنشاء عنوان بأحجام مختلفة"""
        label = QLabel(text)
        font = QFont()

        if level == 1:
            font.setPointSize(18)
            font.setBold(True)
        elif level == 2:
            font.setPointSize(16)
            font.setBold(True)
        elif level == 3:
            font.setPointSize(14)
            font.setBold(True)
        elif level == 4:
            font.setPointSize(12)
            font.setBold(True)
        else:
            font.setPointSize(11)
            font.setBold(True)

        label.setFont(font)
        label.setStyleSheet(f"color: {StyleHelper.COLORS['dark']}; margin: 10px 0;")
        return label

    @staticmethod
    def create_info_card(title: str, content: str, icon: str = "") -> QFrame:
        """إنشاء بطاقة معلومات"""
        card = QFrame()
        StyleHelper.apply_card_style(card)

        layout = QVBoxLayout(card)

        # العنوان مع الأيقونة
        title_label = QLabel(f"{icon} {title}".strip())
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"color: {StyleHelper.COLORS['primary']}; margin-bottom: 8px;")

        # المحتوى
        content_label = QLabel(content)
        content_label.setWordWrap(True)
        content_label.setStyleSheet(f"color: {StyleHelper.COLORS['dark']}; line-height: 1.5;")

        layout.addWidget(title_label)
        layout.addWidget(content_label)

        return card

    @staticmethod
    def create_stat_card(title: str, value: str, icon: str = "", color: str = "primary") -> QFrame:
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        StyleHelper.apply_card_style(card)

        layout = QVBoxLayout(card)

        # الأيقونة والعنوان
        header_layout = QHBoxLayout()

        if icon:
            icon_label = QLabel(icon)
            icon_label.setStyleSheet(f"font-size: 24px; color: {StyleHelper.COLORS[color]};")
            header_layout.addWidget(icon_label)

        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {StyleHelper.COLORS['muted']}; font-size: 10pt;")
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # القيمة
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(20)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"color: {StyleHelper.COLORS[color]}; margin: 8px 0;")

        layout.addLayout(header_layout)
        layout.addWidget(value_label)

        return card

    @staticmethod
    def create_action_button_group(buttons_config: list) -> QFrame:
        """إنشاء مجموعة أزرار الإجراءات"""
        frame = QFrame()
        layout = QHBoxLayout(frame)
        layout.setSpacing(8)

        for config in buttons_config:
            text = config.get('text', '')
            icon = config.get('icon', '')
            style = config.get('style', 'btn-secondary')
            callback = config.get('callback', None)

            button = QPushButton(f"{icon} {text}".strip())
            StyleHelper.apply_button_style(button, style)

            if callback:
                button.clicked.connect(callback)

            layout.addWidget(button)

        layout.addStretch()
        return frame

    @staticmethod
    def apply_modern_group_style(group: QGroupBox, title: str = ""):
        """تطبيق نمط مجموعة حديث"""
        if title:
            group.setTitle(title)

        group.setStyleSheet(f"""
            QGroupBox {{
                font-weight: 600;
                font-size: 11pt;
                border: 2px solid {StyleHelper.COLORS['light']};
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: {StyleHelper.COLORS['white']};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px;
                color: {StyleHelper.COLORS['primary']};
                background-color: {StyleHelper.COLORS['white']};
            }}
        """)

# أيقونات Unicode للاستخدام في الواجهة
class Icons:
    """أيقونات Unicode"""
    # أيقونات عامة
    ADD = "➕"
    EDIT = "✏️"
    DELETE = "🗑️"
    SAVE = "💾"
    CANCEL = "❌"
    REFRESH = "🔄"
    SEARCH = "🔍"
    PRINT = "🖨️"
    
    # أيقونات طبية
    PATIENT = "👤"
    DOCTOR = "👨‍⚕️"
    TOOTH = "🦷"
    MEDICAL = "🏥"
    LAB = "🔬"
    CALENDAR = "📅"
    PHONE = "📱"
    AGE = "🎂"
    
    # أيقونات أخرى
    MONEY = "💰"
    REPORT = "📊"
    SETTINGS = "⚙️"
    INFO = "ℹ️"
    WARNING = "⚠️"
    SUCCESS = "✅"
    ERROR = "❌"
