import os
import sqlite3
import shutil
from datetime import datetime

class DatabaseHandler:
    def __init__(self, db_path='dental_clinic.db'):
        """تهيئة معالج قاعدة البيانات"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
            
    def __enter__(self):
        """دخول المدير السياقي"""
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """خروج المدير السياقي مع ضمان إغلاق الاتصال"""
        self.close()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات المحدثة مع نظام الترقية"""
        try:
            # التحقق من وجود قاعدة البيانات القديمة وترقيتها
            self.upgrade_database_if_needed()

            # إنشاء الجداول الجديدة
            # جدول المستخدمين
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # جدول المرضى - محدث
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                birth_year INTEGER,
                mobile TEXT,
                whatsapp TEXT,
                general_diseases TEXT,
                medications TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # جدول أنواع المعالجات - محدث ومحسن
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatment_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,  -- فئة المعالجة (علاج أسنان، جسور، أطفال، إلخ)
                default_cost INTEGER DEFAULT 0,  -- أرقام صحيحة بدلاً من REAL
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # جدول خطط المعالجة - محدث بالكامل
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatment_plans (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plan_number INTEGER UNIQUE NOT NULL,  -- رقم الخطة (ترقيم تلقائي)
                patient_id INTEGER NOT NULL,
                treatment_type_id INTEGER,  -- ربط بجدول أنواع المعالجات
                tooth_number TEXT,
                treatment_description TEXT,  -- وصف المعالجة
                cost INTEGER DEFAULT 0,  -- أرقام صحيحة، حد أقصى 999,999,999
                plan_date DATE,
                status TEXT DEFAULT 'نشط',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE,
                FOREIGN KEY (treatment_type_id) REFERENCES treatment_types (id) ON DELETE SET NULL
            )
            ''')

            # جدول جلسات المعالجة - محدث بالكامل
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatment_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                treatment_plan_id INTEGER NOT NULL,
                session_date DATE NOT NULL,
                tooth_number TEXT,
                procedure_description TEXT,  -- وصف الإجراء
                payment INTEGER DEFAULT 0,  -- أرقام صحيحة، حد أقصى 999,999,999
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (treatment_plan_id) REFERENCES treatment_plans (id) ON DELETE CASCADE
            )
            ''')

            # جدول المواعيد
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS appointments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                title TEXT,
                appointment_date TIMESTAMP NOT NULL,
                duration INTEGER DEFAULT 30,  -- بالدقائق
                notes TEXT,
                status TEXT DEFAULT 'مؤكد',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE SET NULL
            )
            ''')

            # جدول طلبات المخبر - محدث
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS lab_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER NOT NULL,
                lab_name TEXT NOT NULL,
                work_type TEXT NOT NULL,
                pieces_count INTEGER DEFAULT 1,
                price INTEGER DEFAULT 0,  -- أرقام صحيحة
                notes TEXT,
                status TEXT DEFAULT 'قيد التنفيذ',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                delivery_date TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id) ON DELETE CASCADE
            )
            ''')

            # جدول المصروفات - محدث
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS expenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                description TEXT,
                amount INTEGER NOT NULL,  -- أرقام صحيحة
                expense_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # جدول المخابر
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS labs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                phone TEXT,
                address TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # جدول أنواع أعمال المخبر - محدث
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS lab_work_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                default_cost INTEGER DEFAULT 0,  -- أرقام صحيحة
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # إضافة أنواع المعالجات الافتراضية المحدثة
            self.initialize_default_treatment_types()

            # إضافة بعض المخابر الافتراضية
            default_labs = [
                ('المخبر الفني', '0123456789', 'العنوان', ''),
                ('مخبر الأسنان المتطور', '0123456789', 'العنوان', '')
            ]

            for name, phone, address, notes in default_labs:
                self.cursor.execute('''
                INSERT OR IGNORE INTO labs (name, phone, address, notes)
                VALUES (?, ?, ?, ?)
                ''', (name, phone, address, notes))

            # إضافة بعض أنواع أعمال المخبر الافتراضية - محدث
            default_lab_works = [
                ('تاج زيركون', 150000),
                ('تاج معدني خزفي', 120000),
                ('طقم كامل', 500000),
                ('طقم جزئي', 1500),
                ('جسر زيركون', 3000)
            ]
            
            for work, cost in default_lab_works:
                self.cursor.execute('''
                INSERT OR IGNORE INTO lab_work_types (name, default_cost)
                VALUES (?, ?)
                ''', (work, cost))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            return False

    def initialize_default_treatment_types(self):
        """تهيئة أنواع المعالجات الافتراضية"""
        default_treatment_types = [
            # علاج الأسنان العام
            ('حشوة عادية', 'علاج أسنان', 50000, 'حشوة أسنان عادية'),
            ('حشوة تجميلية', 'علاج أسنان', 75000, 'حشوة أسنان تجميلية'),
            ('علاج عصب', 'علاج أسنان', 150000, 'علاج عصب السن'),
            ('خلع سن', 'علاج أسنان', 30000, 'خلع سن عادي'),
            ('خلع ضرس عقل', 'علاج أسنان', 75000, 'خلع ضرس العقل'),
            ('تنظيف أسنان', 'علاج أسنان', 40000, 'تنظيف وتلميع الأسنان'),

            # التيجان والجسور
            ('تاج زيركون', 'تيجان وجسور', 200000, 'تاج زيركون عالي الجودة'),
            ('تاج معدني خزفي', 'تيجان وجسور', 150000, 'تاج معدني مغطى بالخزف'),
            ('جسر ثلاثي', 'تيجان وجسور', 450000, 'جسر من ثلاث وحدات'),
            ('جسر رباعي', 'تيجان وجسور', 600000, 'جسر من أربع وحدات'),

            # طب أسنان الأطفال
            ('حشوة أطفال', 'أطفال', 40000, 'حشوة أسنان للأطفال'),
            ('خلع سن لبني', 'أطفال', 20000, 'خلع سن لبني للأطفال'),
            ('فلورايد', 'أطفال', 25000, 'تطبيق الفلورايد للأطفال'),

            # التقويم
            ('تقويم معدني', 'تقويم', 800000, 'تقويم أسنان معدني'),
            ('تقويم شفاف', 'تقويم', 1200000, 'تقويم أسنان شفاف'),
            ('جهاز متحرك', 'تقويم', 300000, 'جهاز تقويم متحرك'),

            # الزراعة
            ('زرعة واحدة', 'زراعة', 500000, 'زراعة سن واحد'),
            ('زرعة مع تاج', 'زراعة', 700000, 'زراعة سن مع تاج'),

            # الأطقم المتحركة
            ('طقم كامل علوي', 'أطقم متحركة', 400000, 'طقم أسنان كامل علوي'),
            ('طقم كامل سفلي', 'أطقم متحركة', 400000, 'طقم أسنان كامل سفلي'),
            ('طقم جزئي', 'أطقم متحركة', 250000, 'طقم أسنان جزئي'),

            # الفينير والتجميل
            ('فينير خزفي', 'فينير وتجميل', 180000, 'قشرة خزفية تجميلية'),
            ('فينير مركب', 'فينير وتجميل', 120000, 'قشرة مركبة تجميلية'),
            ('تبييض أسنان', 'فينير وتجميل', 100000, 'تبييض الأسنان بالليزر'),
        ]

        for name, category, cost, description in default_treatment_types:
            self.cursor.execute('''
            INSERT OR IGNORE INTO treatment_types (name, category, default_cost, description)
            VALUES (?, ?, ?, ?)
            ''', (name, category, cost, description))

    def upgrade_database_if_needed(self):
        """ترقية قاعدة البيانات إذا كانت تحتوي على بنية قديمة"""
        try:
            # التحقق من وجود الجداول القديمة
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in self.cursor.fetchall()]

            # التحقق من وجود الأعمدة الجديدة في جدول treatment_plans
            if 'treatment_plans' in existing_tables:
                self.cursor.execute("PRAGMA table_info(treatment_plans)")
                columns = [column[1] for column in self.cursor.fetchall()]

                # إذا لم يكن العمود plan_number موجود، فهذا يعني أن البنية قديمة
                if 'plan_number' not in columns:
                    print("🔄 تم اكتشاف بنية قاعدة بيانات قديمة، جاري الترقية...")
                    self.backup_and_recreate_database()
                    return True

            # التحقق من وجود جدول treatment_types الجديد
            if 'treatment_types' in existing_tables:
                self.cursor.execute("PRAGMA table_info(treatment_types)")
                columns = [column[1] for column in self.cursor.fetchall()]

                # إذا لم يكن العمود category موجود، فهذا يعني أن البنية قديمة
                if 'category' not in columns:
                    print("🔄 تم اكتشاف جدول أنواع المعالجات القديم، جاري الترقية...")
                    self.backup_and_recreate_database()
                    return True

            return False

        except sqlite3.Error as e:
            print(f"خطأ في فحص ترقية قاعدة البيانات: {e}")
            return False

    def backup_and_recreate_database(self):
        """إنشاء نسخة احتياطية وإعادة إنشاء قاعدة البيانات"""
        try:
            print("📦 بدء ترقية قاعدة البيانات...")

            # إنشاء نسخة احتياطية
            backup_path = f"dental_clinic_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, backup_path)
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")

            # حفظ بيانات المرضى فقط (الأهم)
            old_patients = []
            try:
                self.cursor.execute("SELECT * FROM patients")
                old_patients = [dict(row) for row in self.cursor.fetchall()]
                print(f"📥 تم حفظ {len(old_patients)} مريض")
            except:
                print("⚠️ لم يتم العثور على بيانات مرضى للحفظ")

            # إغلاق الاتصال الحالي
            self.close()

            # حذف قاعدة البيانات القديمة
            if os.path.exists(self.db_path):
                os.remove(self.db_path)
                print("🗑️ تم حذف قاعدة البيانات القديمة")

            # إعادة الاتصال بقاعدة بيانات جديدة
            self.connect()

            # استعادة بيانات المرضى
            if old_patients:
                print(f"📥 استعادة {len(old_patients)} مريض...")
                for patient in old_patients:
                    try:
                        self.cursor.execute("""
                            INSERT INTO patients (name, birth_year, mobile, whatsapp,
                                                general_diseases, medications, notes, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            patient.get('name', ''),
                            patient.get('birth_year'),
                            patient.get('mobile', ''),
                            patient.get('whatsapp', ''),
                            patient.get('general_diseases', ''),
                            patient.get('medications', ''),
                            patient.get('notes', ''),
                            patient.get('created_at', datetime.now().isoformat())
                        ))
                    except Exception as e:
                        print(f"⚠️ خطأ في استعادة مريض: {e}")

                self.conn.commit()
                print("✅ تم استعادة بيانات المرضى")

            print("✅ تم إكمال ترقية قاعدة البيانات")
            return True

        except Exception as e:
            print(f"❌ خطأ في ترقية قاعدة البيانات: {e}")
            return False

    # ===== وظائف إدارة المستخدمين =====
    
    def check_user_exists(self, username):
        """التحقق من وجود اسم المستخدم"""
        self.cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
        return self.cursor.fetchone() is not None
    
    def add_user(self, username, password, full_name, role):
        """إضافة مستخدم جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)",
                (username, password, full_name, role)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المستخدم: {e}")
            if self.conn:
                self.conn.rollback()
            return False
    
    def verify_login(self, username, password):
        """التحقق من بيانات تسجيل الدخول"""
        try:
            self.cursor.execute(
                "SELECT id, username, role, full_name FROM users WHERE username = ? AND password = ?",
                (username, password)
            )
            user = self.cursor.fetchone()
            if user:
                return dict(user)  # تحويل كائن Row إلى قاموس
            return None
        except sqlite3.Error as e:
            print(f"خطأ في التحقق من تسجيل الدخول: {e}")
            return None
    
    def get_all_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            self.cursor.execute("SELECT id, username, full_name, role FROM users")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستخدمين: {e}")
            return []

    def get_user_by_id(self, user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        try:
            self.cursor.execute("SELECT id, username, full_name, role FROM users WHERE id = ?", (user_id,))
            row = self.cursor.fetchone()
            return dict(row) if row else None
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستخدم: {e}")
            return None
    
    def update_user(self, user_id, full_name, password=None, role=None, username=None):
        """تحديث بيانات المستخدم"""
        try:
            # بناء الاستعلام ديناميكياً
            update_fields = ["full_name = ?"]
            params = [full_name]

            if username:
                update_fields.append("username = ?")
                params.append(username)

            if password:
                update_fields.append("password = ?")
                params.append(password)

            if role is not None:
                update_fields.append("role = ?")
                params.append(role)

            params.append(user_id)

            query = f"UPDATE users SET {', '.join(update_fields)} WHERE id = ?"
            self.cursor.execute(query, params)
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث المستخدم: {e}")
            return False
    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        try:
            self.cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف المستخدم: {e}")
            return False
    
    # ===== وظائف إدارة المرضى =====
    
    def add_patient(self, name, birth_year=None, mobile=None, whatsapp=None, 
                   general_diseases=None, medications=None, notes=None):
        """إضافة مريض جديد"""
        try:
            self.cursor.execute(
                """INSERT INTO patients 
                (name, birth_year, mobile, whatsapp, general_diseases, medications, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?)""",
                (name, birth_year, mobile, whatsapp, general_diseases, medications, notes)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المريض: {e}")
            if self.conn:
                self.conn.rollback()
            return None
    
    def get_patient(self, patient_id):
        """الحصول على بيانات مريض محدد"""
        try:
            self.cursor.execute("SELECT * FROM patients WHERE id = ?", (patient_id,))
            patient = self.cursor.fetchone()
            if patient:
                return dict(patient)
            return None
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع بيانات المريض: {e}")
            return None
    
    def search_patients(self, search_term):
        """البحث عن المرضى بالاسم أو رقم الهاتف"""
        try:
            search_term = f"%{search_term}%"
            self.cursor.execute(
                """SELECT * FROM patients 
                WHERE name LIKE ? OR mobile LIKE ? OR whatsapp LIKE ?
                ORDER BY name""",
                (search_term, search_term, search_term)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في البحث عن المرضى: {e}")
            return []
    
    def get_all_patients(self):
        """الحصول على جميع المرضى"""
        try:
            self.cursor.execute("SELECT * FROM patients ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المرضى: {e}")
            return []
    
    def update_patient(self, patient_id, name, birth_year=None, mobile=None, whatsapp=None, 
                      general_diseases=None, medications=None, notes=None):
        """تحديث بيانات مريض"""
        try:
            self.cursor.execute(
                """UPDATE patients SET 
                name = ?, birth_year = ?, mobile = ?, whatsapp = ?, 
                general_diseases = ?, medications = ?, notes = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?""",
                (name, birth_year, mobile, whatsapp, general_diseases, medications, notes, patient_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث بيانات المريض: {e}")
            if self.conn:
                self.conn.rollback()
            return False
    
    def delete_patient(self, patient_id):
        """حذف مريض"""
        try:
            self.cursor.execute("DELETE FROM patients WHERE id = ?", (patient_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف المريض: {e}")
            if self.conn:
                self.conn.rollback()
            return False
    
    # ===== وظائف إدارة خطط العلاج المحدثة =====

    def add_treatment_plan(self, patient_id, plan_number, treatment_type_id=None,
                          tooth_number=None, treatment_description=None, cost=0,
                          plan_date=None, notes=None, status="نشط"):
        """إضافة خطة علاج جديدة - محدثة"""
        try:
            self.cursor.execute(
                """INSERT INTO treatment_plans
                (plan_number, patient_id, treatment_type_id, tooth_number, treatment_description,
                cost, plan_date, notes, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (plan_number, patient_id, treatment_type_id, tooth_number, treatment_description,
                 cost, plan_date, notes, status)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة خطة العلاج: {e}")
            if self.conn:
                self.conn.rollback()
            return None

    def get_treatment_plan(self, plan_id):
        """الحصول على خطة علاج محددة - محدثة"""
        try:
            self.cursor.execute(
                """SELECT tp.*, p.name as patient_name, tt.name as treatment_type_name,
                tt.category as treatment_category
                FROM treatment_plans tp
                JOIN patients p ON tp.patient_id = p.id
                LEFT JOIN treatment_types tt ON tp.treatment_type_id = tt.id
                WHERE tp.id = ?""",
                (plan_id,)
            )
            plan = self.cursor.fetchone()
            if plan:
                return dict(plan)
            return None
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع خطة العلاج: {e}")
            return None

    def get_patient_treatment_plans(self, patient_id):
        """الحصول على جميع خطط علاج مريض محدد - محدثة"""
        try:
            self.cursor.execute(
                """SELECT tp.*, tt.name as treatment_type_name, tt.category as treatment_category
                FROM treatment_plans tp
                LEFT JOIN treatment_types tt ON tp.treatment_type_id = tt.id
                WHERE tp.patient_id = ?
                ORDER BY tp.plan_number DESC""",
                (patient_id,)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع خطط علاج المريض: {e}")
            return []
    
    def update_treatment_plan(self, plan_id, tooth_number=None, treatment_type=None, is_bridge=None,
                             bridge_from=None, bridge_to=None, crowns_count=None,
                             total_cost=None, notes=None, status=None):
        """تحديث خطة علاج"""
        try:
            # الحصول على البيانات الحالية
            current = self.get_treatment_plan(plan_id)
            if not current:
                return False
            
            # تحديث القيم المقدمة فقط
            tooth_number = tooth_number if tooth_number is not None else current['tooth_number']
            treatment_type = treatment_type if treatment_type is not None else current['treatment_type']
            is_bridge = is_bridge if is_bridge is not None else current['is_bridge']
            bridge_from = bridge_from if bridge_from is not None else current['bridge_from']
            bridge_to = bridge_to if bridge_to is not None else current['bridge_to']
            crowns_count = crowns_count if crowns_count is not None else current['crowns_count']
            total_cost = total_cost if total_cost is not None else current['total_cost']
            notes = notes if notes is not None else current['notes']
            status = status if status is not None else current['status']
            
            self.cursor.execute(
                """UPDATE treatment_plans SET 
                tooth_number = ?, treatment_type = ?, is_bridge = ?, 
                bridge_from = ?, bridge_to = ?, crowns_count = ?,
                total_cost = ?, notes = ?, status = ?,
                updated_at = CURRENT_TIMESTAMP
                WHERE id = ?""",
                (tooth_number, treatment_type, is_bridge, bridge_from, bridge_to, 
                 crowns_count, total_cost, notes, status, plan_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث خطة العلاج: {e}")
            return False
    
    def delete_treatment_plan(self, plan_id):
        """حذف خطة علاج"""
        try:
            self.cursor.execute("DELETE FROM treatment_plans WHERE id = ?", (plan_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف خطة العلاج: {e}")
            return False
    
    # ===== وظائف إدارة جلسات العلاج المحدثة =====

    def add_treatment_session(self, treatment_plan_id, session_date, tooth_number=None,
                             procedure_description=None, payment=0, notes=None):
        """إضافة جلسة علاج جديدة - محدثة"""
        try:
            self.cursor.execute(
                """INSERT INTO treatment_sessions
                (treatment_plan_id, session_date, tooth_number, procedure_description, payment, notes)
                VALUES (?, ?, ?, ?, ?, ?)""",
                (treatment_plan_id, session_date, tooth_number, procedure_description, payment, notes)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة جلسة العلاج: {e}")
            if self.conn:
                self.conn.rollback()
            return None

    def get_treatment_sessions(self, treatment_plan_id):
        """الحصول على جميع جلسات علاج لخطة محددة - محدثة"""
        try:
            self.cursor.execute(
                """SELECT ts.*, tp.plan_number, tp.patient_id, p.name as patient_name
                FROM treatment_sessions ts
                JOIN treatment_plans tp ON ts.treatment_plan_id = tp.id
                JOIN patients p ON tp.patient_id = p.id
                WHERE ts.treatment_plan_id = ?
                ORDER BY ts.session_date DESC""",
                (treatment_plan_id,)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع جلسات العلاج: {e}")
            return []
    
    def update_treatment_session(self, session_id, session_details=None, session_date=None, payment=None, notes=None):
        """تحديث جلسة علاج"""
        try:
            # الحصول على البيانات الحالية
            self.cursor.execute("SELECT * FROM treatment_sessions WHERE id = ?", (session_id,))
            current = self.cursor.fetchone()
            if not current:
                return False
            
            # تحديث القيم المقدمة فقط
            session_details = session_details if session_details is not None else current['session_details']
            session_date = session_date if session_date is not None else current['session_date']
            payment = payment if payment is not None else current['payment']
            notes = notes if notes is not None else current['notes']
            
            self.cursor.execute(
                """UPDATE treatment_sessions SET 
                session_details = ?, session_date = ?, payment = ?, notes = ?
                WHERE id = ?""",
                (session_details, session_date, payment, notes, session_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث جلسة العلاج: {e}")
            return False
    
    def delete_treatment_session(self, session_id):
        """حذف جلسة علاج"""
        try:
            self.cursor.execute("DELETE FROM treatment_sessions WHERE id = ?", (session_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف جلسة العلاج: {e}")
            return False
    
    def get_total_payments(self, treatment_plan_id):
        """حساب إجمالي المدفوعات لخطة علاج محددة"""
        try:
            self.cursor.execute(
                "SELECT SUM(payment) as total FROM treatment_sessions WHERE treatment_plan_id = ?",
                (treatment_plan_id,)
            )
            result = self.cursor.fetchone()
            return result['total'] if result and result['total'] else 0
        except sqlite3.Error as e:
            print(f"خطأ في حساب إجمالي المدفوعات: {e}")
            return 0

    # ===== وظائف جديدة لدعم التحسينات المطبقة =====

    def get_max_plan_number(self):
        """الحصول على أكبر رقم خطة معالجة للترقيم التلقائي"""
        try:
            self.cursor.execute("SELECT MAX(plan_number) as max_number FROM treatment_plans")
            result = self.cursor.fetchone()
            return result['max_number'] if result and result['max_number'] else 0
        except sqlite3.Error as e:
            print(f"خطأ في الحصول على أكبر رقم خطة: {e}")
            return 0

    def get_treatment_plans_by_patient(self, patient_id):
        """جلب خطط المعالجة حسب المريض"""
        try:
            self.cursor.execute("""
                SELECT tp.*, tt.name as treatment_type_name, tt.category as treatment_category
                FROM treatment_plans tp
                LEFT JOIN treatment_types tt ON tp.treatment_type_id = tt.id
                WHERE tp.patient_id = ?
                ORDER BY tp.plan_number DESC
            """, (patient_id,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في جلب خطط المعالجة للمريض: {e}")
            return []

    def get_treatment_plans_by_type(self, patient_id, treatment_type_id):
        """جلب خطط المعالجة حسب المريض ونوع المعالجة"""
        try:
            self.cursor.execute("""
                SELECT tp.*, tt.name as treatment_type_name, tt.category as treatment_category
                FROM treatment_plans tp
                LEFT JOIN treatment_types tt ON tp.treatment_type_id = tt.id
                WHERE tp.patient_id = ? AND tp.treatment_type_id = ?
                ORDER BY tp.plan_number DESC
            """, (patient_id, treatment_type_id))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في جلب خطط المعالجة حسب النوع: {e}")
            return []

    def get_treatment_sessions_by_plan(self, treatment_plan_id):
        """جلب جلسات المعالجة حسب خطة المعالجة"""
        try:
            self.cursor.execute("""
                SELECT ts.*, tp.plan_number, tp.patient_id
                FROM treatment_sessions ts
                JOIN treatment_plans tp ON ts.treatment_plan_id = tp.id
                WHERE ts.treatment_plan_id = ?
                ORDER BY ts.session_date DESC
            """, (treatment_plan_id,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في جلب جلسات المعالجة للخطة: {e}")
            return []

    def get_all_treatment_types(self):
        """جلب جميع أنواع المعالجات"""
        try:
            self.cursor.execute("""
                SELECT * FROM treatment_types
                WHERE is_active = 1
                ORDER BY category, name
            """)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في جلب أنواع المعالجات: {e}")
            return []

    def get_treatment_types_by_category(self, category):
        """جلب أنواع المعالجات حسب الفئة"""
        try:
            self.cursor.execute("""
                SELECT * FROM treatment_types
                WHERE category = ? AND is_active = 1
                ORDER BY name
            """, (category,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في جلب أنواع المعالجات حسب الفئة: {e}")
            return []

    # ===== وظائف إدارة المواعيد =====
    
    def add_appointment(self, patient_id, title, appointment_date, duration=30, notes=None, status="مؤكد"):
        """إضافة موعد جديد"""
        try:
            self.cursor.execute(
                """INSERT INTO appointments 
                (patient_id, title, appointment_date, duration, notes, status) 
                VALUES (?, ?, ?, ?, ?, ?)""",
                (patient_id, title, appointment_date, duration, notes, status)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة الموعد: {e}")
            return None
    
    def get_appointments_by_date_range(self, start_date, end_date):
        """الحصول على المواعيد في نطاق تاريخ محدد"""
        try:
            self.cursor.execute(
                """SELECT a.*, p.name as patient_name 
                FROM appointments a
                LEFT JOIN patients p ON a.patient_id = p.id
                WHERE a.appointment_date BETWEEN ? AND ?
                ORDER BY a.appointment_date""",
                (start_date, end_date)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المواعيد: {e}")
            return []
    
    def get_patient_appointments(self, patient_id):
        """الحصول على مواعيد مريض محدد"""
        try:
            self.cursor.execute(
                """SELECT * FROM appointments 
                WHERE patient_id = ?
                ORDER BY appointment_date DESC""",
                (patient_id,)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع مواعيد المريض: {e}")
            return []
    
    def update_appointment(self, appointment_id, title=None, appointment_date=None, 
                          duration=None, notes=None, status=None):
        """تحديث موعد"""
        try:
            # الحصول على البيانات الحالية
            self.cursor.execute("SELECT * FROM appointments WHERE id = ?", (appointment_id,))
            current = self.cursor.fetchone()
            if not current:
                return False
            
            # تحديث القيم المقدمة فقط
            title = title if title is not None else current['title']
            appointment_date = appointment_date if appointment_date is not None else current['appointment_date']
            duration = duration if duration is not None else current['duration']
            notes = notes if notes is not None else current['notes']
            status = status if status is not None else current['status']
            
            self.cursor.execute(
                """UPDATE appointments SET 
                title = ?, appointment_date = ?, duration = ?, notes = ?, status = ?
                WHERE id = ?""",
                (title, appointment_date, duration, notes, status, appointment_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث الموعد: {e}")
            return False
    
    def delete_appointment(self, appointment_id):
        """حذف موعد"""
        try:
            self.cursor.execute("DELETE FROM appointments WHERE id = ?", (appointment_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف الموعد: {e}")
            return False
    
    # ===== وظائف إدارة طلبات المخبر =====
    
    def add_lab_order(self, patient_id, lab_id, work_type_id, pieces_count=1, price=0,
                     notes=None, status="قيد التنفيذ", delivery_date=None):
        """إضافة طلب مخبري جديد"""
        try:
            # الحصول على أسماء المخبر ونوع العمل من IDs
            self.cursor.execute("SELECT name FROM labs WHERE id = ?", (lab_id,))
            lab_result = self.cursor.fetchone()
            lab_name = lab_result['name'] if lab_result else f"مخبر {lab_id}"

            self.cursor.execute("SELECT name FROM lab_work_types WHERE id = ?", (work_type_id,))
            work_type_result = self.cursor.fetchone()
            work_type = work_type_result['name'] if work_type_result else f"نوع عمل {work_type_id}"

            self.cursor.execute(
                """INSERT INTO lab_orders
                (patient_id, lab_name, work_type, pieces_count, price, notes, status, delivery_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (patient_id, lab_name, work_type, pieces_count, price, notes, status, delivery_date)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة الطلب المخبري: {e}")
            return None
    
    def get_lab_orders(self, patient_id=None, lab_name=None, status=None, date_from=None, date_to=None):
        """الحصول على طلبات المخبر حسب معايير مختلفة"""
        try:
            query = """SELECT lo.*, p.name as patient_name 
                    FROM lab_orders lo
                    JOIN patients p ON lo.patient_id = p.id
                    WHERE 1=1"""
            params = []
            
            if patient_id:
                query += " AND lo.patient_id = ?"
                params.append(patient_id)
            
            if lab_name:
                query += " AND lo.lab_name = ?"
                params.append(lab_name)
            
            if status:
                query += " AND lo.status = ?"
                params.append(status)
            
            if date_from:
                query += " AND lo.created_at >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND lo.created_at <= ?"
                params.append(date_to)
            
            query += " ORDER BY lo.created_at DESC"
            
            self.cursor.execute(query, params)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع طلبات المخبر: {e}")
            return []

    def get_lab_order(self, order_id):
        """الحصول على طلب مخبري محدد"""
        try:
            self.cursor.execute(
                """SELECT lo.*, p.name as patient_name
                FROM lab_orders lo
                JOIN patients p ON lo.patient_id = p.id
                WHERE lo.id = ?""",
                (order_id,)
            )
            order = self.cursor.fetchone()
            if order:
                return dict(order)
            return None
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الطلب المخبري: {e}")
            return None

    def update_lab_order(self, order_id, patient_id=None, lab_id=None, work_type_id=None,
                        pieces_count=None, price=None, total_price=None, notes=None,
                        status=None, delivery_date=None):
        """تحديث طلب مخبري"""
        try:
            # الحصول على البيانات الحالية
            self.cursor.execute("SELECT * FROM lab_orders WHERE id = ?", (order_id,))
            current = self.cursor.fetchone()
            if not current:
                return False
            
            # تحديث القيم المقدمة فقط
            updated_patient_id = patient_id if patient_id is not None else current['patient_id']

            # تحويل lab_id إلى lab_name إذا تم تقديمه
            if lab_id is not None:
                self.cursor.execute("SELECT name FROM labs WHERE id = ?", (lab_id,))
                lab_result = self.cursor.fetchone()
                lab_name = lab_result['name'] if lab_result else current['lab_name']
            else:
                lab_name = current['lab_name']

            # تحويل work_type_id إلى work_type إذا تم تقديمه
            if work_type_id is not None:
                self.cursor.execute("SELECT name FROM lab_work_types WHERE id = ?", (work_type_id,))
                work_type_result = self.cursor.fetchone()
                work_type = work_type_result['name'] if work_type_result else current['work_type']
            else:
                work_type = current['work_type']

            pieces_count = pieces_count if pieces_count is not None else current['pieces_count']
            price = price if price is not None else current['price']
            notes = notes if notes is not None else current['notes']
            status = status if status is not None else current['status']
            delivery_date = delivery_date if delivery_date is not None else current['delivery_date']
            
            self.cursor.execute(
                """UPDATE lab_orders SET
                patient_id = ?, lab_name = ?, work_type = ?, pieces_count = ?, price = ?,
                notes = ?, status = ?, delivery_date = ?
                WHERE id = ?""",
                (updated_patient_id, lab_name, work_type, pieces_count, price, notes, status, delivery_date, order_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث الطلب المخبري: {e}")
            return False
    
    def delete_lab_order(self, order_id):
        """حذف طلب مخبري"""
        try:
            self.cursor.execute("DELETE FROM lab_orders WHERE id = ?", (order_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف الطلب المخبري: {e}")
            return False
    
    # ===== وظائف إدارة المصروفات =====
    
    def add_expense(self, category, description, amount, expense_date=None, notes=None):
        """إضافة مصروف جديد"""
        try:
            if not expense_date:
                expense_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
            self.cursor.execute(
                """INSERT INTO expenses 
                (category, description, amount, expense_date, notes) 
                VALUES (?, ?, ?, ?, ?)""",
                (category, description, amount, expense_date, notes)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المصروف: {e}")
            return None
    
    def get_expenses(self, category=None, date_from=None, date_to=None):
        """الحصول على المصروفات حسب معايير مختلفة"""
        try:
            query = "SELECT * FROM expenses WHERE 1=1"
            params = []
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if date_from:
                query += " AND expense_date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND expense_date <= ?"
                params.append(date_to)
            
            query += " ORDER BY expense_date DESC"
            
            self.cursor.execute(query, params)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المصروفات: {e}")
            return []
    
    def update_expense(self, expense_id, category=None, description=None, amount=None, 
                      expense_date=None, notes=None):
        """تحديث مصروف"""
        try:
            # الحصول على البيانات الحالية
            self.cursor.execute("SELECT * FROM expenses WHERE id = ?", (expense_id,))
            current = self.cursor.fetchone()
            if not current:
                return False
            
            # تحديث القيم المقدمة فقط
            category = category if category is not None else current['category']
            description = description if description is not None else current['description']
            amount = amount if amount is not None else current['amount']
            expense_date = expense_date if expense_date is not None else current['expense_date']
            notes = notes if notes is not None else current['notes']
            
            self.cursor.execute(
                """UPDATE expenses SET 
                category = ?, description = ?, amount = ?, expense_date = ?, notes = ?
                WHERE id = ?""",
                (category, description, amount, expense_date, notes, expense_id)
            )
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في تحديث المصروف: {e}")
            return False
    
    def delete_expense(self, expense_id):
        """حذف مصروف"""
        try:
            self.cursor.execute("DELETE FROM expenses WHERE id = ?", (expense_id,))
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في حذف المصروف: {e}")
            return False
    
    # ===== وظائف التقارير =====
    
    def get_patient_payments(self, patient_id, date_from=None, date_to=None):
        """الحصول على مدفوعات مريض محدد في فترة زمنية محددة"""
        try:
            query = """SELECT ts.*, tp.treatment_type, p.name as patient_name
                    FROM treatment_sessions ts
                    JOIN treatment_plans tp ON ts.treatment_plan_id = tp.id
                    JOIN patients p ON tp.patient_id = p.id
                    WHERE tp.patient_id = ?"""
            params = [patient_id]
            
            if date_from:
                query += " AND ts.session_date >= ?"
                params.append(date_from)
            
            if date_to:
                query += " AND ts.session_date <= ?"
                params.append(date_to)
            
            query += " ORDER BY ts.session_date"
            
            self.cursor.execute(query, params)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع مدفوعات المريض: {e}")
            return []
    
    def get_income_report(self, date_from, date_to):
        """تقرير الدخل في فترة زمنية محددة"""
        try:
            self.cursor.execute(
                """SELECT SUM(payment) as total_income
                FROM treatment_sessions
                WHERE session_date BETWEEN ? AND ?""",
                (date_from, date_to)
            )
            result = self.cursor.fetchone()
            return result['total_income'] if result and result['total_income'] else 0
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تقرير الدخل: {e}")
            return 0
    
    def get_expenses_report(self, date_from, date_to):
        """تقرير المصروفات في فترة زمنية محددة"""
        try:
            self.cursor.execute(
                """SELECT SUM(amount) as total_expenses
                FROM expenses
                WHERE expense_date BETWEEN ? AND ?""",
                (date_from, date_to)
            )
            result = self.cursor.fetchone()
            return result['total_expenses'] if result and result['total_expenses'] else 0
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تقرير المصروفات: {e}")
            return 0
    
    def get_lab_expenses_report(self, date_from, date_to):
        """تقرير مصروفات المخبر في فترة زمنية محددة"""
        try:
            self.cursor.execute(
                """SELECT SUM(price) as total_lab_expenses
                FROM lab_orders
                WHERE created_at BETWEEN ? AND ?""",
                (date_from, date_to)
            )
            result = self.cursor.fetchone()
            return result['total_lab_expenses'] if result and result['total_lab_expenses'] else 0
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع تقرير مصروفات المخبر: {e}")
            return 0
    
    def get_profit_report(self, date_from, date_to):
        """تقرير الأرباح في فترة زمنية محددة"""
        income = self.get_income_report(date_from, date_to)
        expenses = self.get_expenses_report(date_from, date_to)
        lab_expenses = self.get_lab_expenses_report(date_from, date_to)
        
        return income - expenses - lab_expenses
    
    # ===== وظائف النسخ الاحتياطي واستعادة البيانات =====
    
    def backup_database(self, backup_path):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            self.close()  # إغلاق الاتصال قبل النسخ
            shutil.copy2(self.db_path, backup_path)
            self.connect()  # إعادة فتح الاتصال
            return True
        except Exception as e:
            print(f"خطأ في إنشاء نسخة احتياطية: {e}")
            self.connect()  # إعادة فتح الاتصال في حالة الخطأ
            return False
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            self.close()  # إغلاق الاتصال قبل الاستعادة
            shutil.copy2(backup_path, self.db_path)
            self.connect()  # إعادة فتح الاتصال
            return True
        except Exception as e:
            print(f"خطأ في استعادة قاعدة البيانات: {e}")
            self.connect()  # إعادة فتح الاتصال في حالة الخطأ
            return False
    
    # ===== وظائف إضافية =====
    
    def get_treatment_types(self):
        """الحصول على جميع أنواع العلاج"""
        try:
            self.cursor.execute("SELECT * FROM treatment_types ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع أنواع العلاج: {e}")
            return []
    
    def get_labs(self):
        """الحصول على جميع المخابر"""
        try:
            self.cursor.execute("SELECT * FROM labs ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المخابر: {e}")
            return []
    
    def get_lab_work_types(self):
        """الحصول على جميع أنواع أعمال المخبر"""
        try:
            self.cursor.execute("SELECT * FROM lab_work_types ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع أنواع أعمال المخبر: {e}")
            return []
    
    def add_treatment_type(self, name, default_cost=0):
        """إضافة نوع علاج جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO treatment_types (name, default_cost) VALUES (?, ?)",
                (name, default_cost)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة نوع العلاج: {e}")
            return None
    
    def add_lab(self, name, phone=None, address=None, notes=None):
        """إضافة مخبر جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO labs (name, phone, address, notes) VALUES (?, ?, ?, ?)",
                (name, phone, address, notes)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة المخبر: {e}")
            return None
    
    def add_lab_work_type(self, name, default_cost=0):
        """إضافة نوع عمل مخبري جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO lab_work_types (name, default_cost) VALUES (?, ?)",
                (name, default_cost)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة نوع العمل المخبري: {e}")
            return None