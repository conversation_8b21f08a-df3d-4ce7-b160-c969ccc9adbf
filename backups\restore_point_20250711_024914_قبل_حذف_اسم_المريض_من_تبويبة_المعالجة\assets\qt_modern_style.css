/* تصميم حديث متوافق مع PyQt5 - دعم العربية من اليمين إلى اليسار */

/* النافذة الرئيسية */
QMainWindow {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #fafafa, stop: 1 #f0f0f0);
    color: #212121;
    font-family: "Segoe UI", "Tahoma", "Arabic UI Text", sans-serif;
    font-size: 14px;
    border-radius: 0px;
}

/* شريط العنوان */
QFrame[objectName="titleBar"] {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1e88e5, stop: 1 #1976d2);
    border: none;
    border-bottom: 1px solid #1565c0;
    min-height: 64px;
    max-height: 64px;
}

QFrame[objectName="titleBar"] QLabel {
    color: white;
    font-size: 20px;
    font-weight: bold;
    padding: 0 24px;
}

QFrame[objectName="titleBar"] QPushButton {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: white;
    padding: 8px 16px;
    font-weight: 500;
    font-size: 14px;
    min-width: 80px;
    margin: 0 4px;
}

QFrame[objectName="titleBar"] QPushButton:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

/* التبويبات الحديثة */
QTabWidget {
    background-color: transparent;
    border: none;
}

QTabWidget::pane {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-top: 0;
}

QTabBar {
    background-color: transparent;
    border: none;
    margin: 0;
    padding: 0 16px;
}

QTabBar::tab {
    background-color: transparent;
    border: none;
    border-radius: 8px 8px 0 0;
    padding: 16px 24px;
    margin: 0 4px;
    color: #757575;
    font-weight: 500;
    font-size: 14px;
    min-width: 120px;
    text-align: right;
    direction: rtl;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #2196f3, stop: 1 #1976d2);
    color: white;
    font-weight: 600;
    border: 1px solid #1976d2;
    border-bottom: 1px solid white;
    margin-top: -1px;
}

QTabBar::tab:hover:!selected {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* الأزرار الحديثة */
QPushButton {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 8px 16px;
    color: #212121;
}