#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تقليل ارتفاع المجموعات الفرعية في حاوية خيارات المعالجة
Test reduced heights of subgroups in treatment options container
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QScrollArea
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class ReducedGroupHeightsTestWindow(QMainWindow):
    """نافذة اختبار تقليل ارتفاع المجموعات الفرعية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تقليل ارتفاع المجموعات الفرعية")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تقليل ارتفاع المجموعات الفرعية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة - تقليل ارتفاع المجموعات الفرعية:
        
        📉 تم تقليل ارتفاع المجموعات الأربعة:
        • مجموعة اللبية (Endodontic): من 290px إلى 220px
        • مجموعة الترميمية (Restorative): من 290px إلى 220px
        • مجموعة التيجان (Crowns): من 290px إلى 220px
        • مجموعة الجراحة (Surgery): من 290px إلى 220px
        
        📉 تم تقليل الحد الأدنى في CSS:
        • min-height في get_group_style(): من 280px إلى 210px
        
        ✅ الفوائد المحققة:
        • ضمان ظهور الإطار المحيط (border: 2px solid #dee2e6) بالكامل
        • تجنب قطع أو إخفاء أي جزء من حدود المجموعات
        • تحسين توزيع المساحة العمودية في الحاوية
        • الحفاظ على وضوح جميع مربعات الاختيار
        
        📋 النتيجة: عرض محسن للمجموعات مع ظهور جميع الحدود بوضوح
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 12px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إنشاء منطقة تمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى الاختبار
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # اختبار خيارات المعالجة مع الارتفاع المقلل
        options_title = QLabel("⚙️ خيارات المعالجة (ارتفاع مقلل للمجموعات)")
        options_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(options_title)
        
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        content_layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 10px;
                border-radius: 5px;
                font-size: 11px;
                color: #2980b9;
                border: 1px solid #3498db;
            }
        """)
        content_layout.addWidget(self.selected_info)
        
        # معلومات التفاصيل
        details_label = QLabel("""
        📝 تفاصيل التغييرات:
        
        القيم الجديدة:
        • setMinimumHeight() للمجموعات الأربعة: 220px (كان 290px)
        • min-height في CSS: 210px (كان 280px)
        • توفير: 70px لكل مجموعة
        
        العناصر المحفوظة:
        • border: 2px solid #dee2e6 (الحدود الرمادية)
        • border-radius: 8px (الزوايا المدورة)
        • background-color: #ffffff (الخلفية البيضاء)
        • جميع مربعات الاختيار ووظائفها
        • التخطيط الشبكي 2x2
        • تنسيق العناوين والألوان
        
        📊 النتيجة الإجمالية:
        • ظهور كامل للحدود والإطارات
        • توزيع أفضل للمساحة العمودية
        • عدم قطع أو إخفاء أي عناصر
        • الحفاظ على جميع الوظائف والتفاعل
        """)
        details_label.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 12px;
                border-radius: 8px;
                font-size: 10px;
                color: #2980b9;
                border: 1px solid #3498db;
                line-height: 1.3;
            }
        """)
        content_layout.addWidget(details_label)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        from PyQt5.QtWidgets import QPushButton
        import random
        
        # زر تحديد عشوائي
        random_btn = QPushButton("تحديد خيارات عشوائية")
        random_btn.clicked.connect(self.select_random_options)
        random_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(random_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        content_layout.addLayout(buttons_layout)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تقليل ارتفاع المجموعات بنجاح مع ضمان ظهور جميع الحدود!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_random_options(self):
        """تحديد خيارات عشوائية للاختبار"""
        import random
        checkboxes = list(self.treatment_options.checkboxes.values())
        # تحديد 3-6 خيارات عشوائياً
        num_to_select = random.randint(3, 6)
        selected_checkboxes = random.sample(checkboxes, min(num_to_select, len(checkboxes)))
        
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات العشوائية
        for checkbox in selected_checkboxes:
            checkbox.setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ReducedGroupHeightsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
