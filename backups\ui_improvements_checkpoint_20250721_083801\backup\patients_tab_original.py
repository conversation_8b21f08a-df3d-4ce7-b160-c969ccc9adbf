import sys
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate
from PyQt5.QtGui import QIcon, QFont
from ui.style_helper import StyleHelper

class PatientForm(QWidget):
    """نموذج إضافة/تعديل بيانات المريض"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # حقول الإدخال
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المريض")
        
        self.birth_year_input = QSpinBox()
        self.birth_year_input.setRange(1900, datetime.now().year)
        self.birth_year_input.setValue(1990)
        self.birth_year_input.valueChanged.connect(self.calculate_age)
        
        self.age_input = QSpinBox()
        self.age_input.setRange(0, 120)
        self.age_input.setValue(datetime.now().year - 1990)
        self.age_input.valueChanged.connect(self.calculate_birth_year)
        
        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("أدخل رقم الموبايل")
        
        self.whatsapp_input = QLineEdit()
        self.whatsapp_input.setPlaceholderText("أدخل رقم الواتساب")
        
        self.general_diseases_input = QTextEdit()
        self.general_diseases_input.setPlaceholderText("أدخل الأمراض العامة")
        self.general_diseases_input.setMaximumHeight(80)
        
        self.medications_input = QTextEdit()
        self.medications_input.setPlaceholderText("أدخل الأدوية")
        self.medications_input.setMaximumHeight(80)
        
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل الملاحظات")
        self.notes_input.setMaximumHeight(80)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("اسم المريض:", self.name_input)
        
        # تخطيط العمر وسنة الولادة
        age_layout = QHBoxLayout()
        age_layout.addWidget(self.birth_year_input)
        age_layout.addWidget(QLabel("العمر:"))
        age_layout.addWidget(self.age_input)
        form_layout.addRow("سنة الولادة:", age_layout)
        
        form_layout.addRow("رقم الموبايل:", self.mobile_input)
        form_layout.addRow("رقم الواتساب:", self.whatsapp_input)
        form_layout.addRow("الأمراض العامة:", self.general_diseases_input)
        form_layout.addRow("الأدوية:", self.medications_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        # إضافة النموذج إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setMinimumWidth(100)
        self.save_button.setProperty("class", "success")
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setMinimumWidth(100)
        self.cancel_button.setProperty("class", "flat")
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
    
    def calculate_age(self):
        """حساب العمر من سنة الولادة"""
        birth_year = self.birth_year_input.value()
        current_year = datetime.now().year
        age = current_year - birth_year
        self.age_input.blockSignals(True)
        self.age_input.setValue(age)
        self.age_input.blockSignals(False)
    
    def calculate_birth_year(self):
        """حساب سنة الولادة من العمر"""
        age = self.age_input.value()
        current_year = datetime.now().year
        birth_year = current_year - age
        self.birth_year_input.blockSignals(True)
        self.birth_year_input.setValue(birth_year)
        self.birth_year_input.blockSignals(False)
    
    def set_patient_data(self, patient_data):
        """تعيين بيانات المريض في النموذج"""
        if patient_data:
            self.name_input.setText(patient_data.get('name', ''))
            
            birth_year = patient_data.get('birth_year')
            if birth_year:
                self.birth_year_input.setValue(int(birth_year))
                self.calculate_age()
            
            self.mobile_input.setText(patient_data.get('mobile', ''))
            self.whatsapp_input.setText(patient_data.get('whatsapp', ''))
            self.general_diseases_input.setText(patient_data.get('general_diseases', ''))
            self.medications_input.setText(patient_data.get('medications', ''))
            self.notes_input.setText(patient_data.get('notes', ''))
    
    def get_patient_data(self):
        """الحصول على بيانات المريض من النموذج"""
        return {
            'name': self.name_input.text().strip(),
            'birth_year': self.birth_year_input.value(),
            'mobile': self.mobile_input.text().strip(),
            'whatsapp': self.whatsapp_input.text().strip(),
            'general_diseases': self.general_diseases_input.toPlainText().strip(),
            'medications': self.medications_input.toPlainText().strip(),
            'notes': self.notes_input.toPlainText().strip()
        }
    
    def clear_form(self):
        """مسح النموذج"""
        self.name_input.clear()
        self.birth_year_input.setValue(1990)
        self.mobile_input.clear()
        self.whatsapp_input.clear()
        self.general_diseases_input.clear()
        self.medications_input.clear()
        self.notes_input.clear()
        self.calculate_age()

class PatientDialog(QDialog):
    """نافذة حوار إضافة/تعديل المريض"""
    def __init__(self, parent=None, patient_data=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.init_ui()
    
    def init_ui(self):
        # عنوان النافذة
        self.setWindowTitle("إضافة مريض جديد" if not self.patient_data else "تعديل بيانات المريض")
        self.setMinimumWidth(500)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج بيانات المريض
        self.patient_form = PatientForm()
        main_layout.addWidget(self.patient_form)
        
        # تعيين بيانات المريض إذا كانت متوفرة
        if self.patient_data:
            self.patient_form.set_patient_data(self.patient_data)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # ربط أزرار النموذج
        self.patient_form.save_button.clicked.connect(self.accept)
        self.patient_form.cancel_button.clicked.connect(self.reject)
    
    def get_patient_data(self):
        """الحصول على بيانات المريض من النموذج"""
        return self.patient_form.get_patient_data()



class PatientsTab(QWidget):
    # إشارة اختيار المريض
    patient_selected = pyqtSignal(int)  # معرف المريض
    
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.current_patient_id = None
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # الحاوية اليسرى: أدوات البحث والإضافة
        left_container = QVBoxLayout()
        left_container.setContentsMargins(0, 0, 0, 0)
        left_container.setSpacing(15)
        
        # تخطيط البحث والإضافة - إطار علوي أنيق
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)
        
        # عنوان قسم البحث
        search_title = QLabel("🔍 البحث والإضافة")
        search_title_font = QFont()
        search_title_font.setPointSize(11)
        search_title_font.setBold(True)
        search_title.setFont(search_title_font)
        search_title.setStyleSheet("color: #495057; margin-bottom: 5px;")
        search_layout.addWidget(search_title)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث بالاسم أو رقم الموبايل...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
        self.search_input.textChanged.connect(self.search_patients)
        search_layout.addWidget(self.search_input)

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة مريض جديد")
        self.add_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #28a745, stop: 1 #1e7e34);
                border: 1px solid #1e7e34;
                color: white;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #1e7e34, stop: 1 #155724);
                border-color: #155724;
            }
            QPushButton:pressed {
                background: #155724;
            }
        """)
        self.add_button.clicked.connect(self.add_patient)
        search_layout.addWidget(self.add_button)

        # زر تحديث القائمة
        self.refresh_button = QPushButton("🔄 تحديث القائمة")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #6c757d, stop: 1 #545b62);
                border: 1px solid #545b62;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #545b62, stop: 1 #495057);
                border-color: #495057;
            }
        """)
        self.refresh_button.clicked.connect(self.load_patients)
        search_layout.addWidget(self.refresh_button)
        
        left_container.addWidget(search_frame)
        
        # إضافة حاوية ملخص المريض المحدد - إطار معلومات سريعة
        quick_info_frame = QFrame()
        quick_info_frame.setFrameShape(QFrame.StyledPanel)
        quick_info_frame.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border: 1px solid #bbdefb;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        quick_info_layout = QVBoxLayout(quick_info_frame)
        quick_info_layout.setSpacing(8)
        
        # عنوان القسم
        quick_info_title = QLabel("📋 المريض المحدد")
        quick_info_title_font = QFont()
        quick_info_title_font.setPointSize(11)
        quick_info_title_font.setBold(True)
        quick_info_title.setFont(quick_info_title_font)
        quick_info_title.setStyleSheet("color: #1976d2; margin-bottom: 5px;")
        quick_info_layout.addWidget(quick_info_title)
        
        # معلومات المريض السريعة
        self.patient_name_label = QLabel("الاسم: لم يتم اختيار مريض")
        self.patient_mobile_label = QLabel("الموبايل: --")
        self.patient_age_label = QLabel("العمر: --")
        
        for label in [self.patient_name_label, self.patient_mobile_label, self.patient_age_label]:
            label.setStyleSheet("""
                QLabel {
                    color: #1565c0;
                    font-size: 13px;
                    font-weight: 500;
                    padding: 2px 0px;
                }
            """)
            label.setWordWrap(True)
            quick_info_layout.addWidget(label)
            
        left_container.addWidget(quick_info_frame)
        left_container.addStretch()
        
        # الحاوية الوسطى: قائمة المرضى
        patients_widget = QWidget()
        patients_layout = QVBoxLayout(patients_widget)
        patients_layout.setContentsMargins(0, 0, 0, 0)
        patients_layout.setSpacing(10)
        
        # عنوان قائمة المرضى
        patients_title = QLabel("👥 قائمة المرضى")
        patients_title_font = QFont()
        patients_title_font.setPointSize(12)
        patients_title_font.setBold(True)
        patients_title.setFont(patients_title_font)
        patients_title.setStyleSheet("color: #495057; margin-bottom: 10px;")
        patients_layout.addWidget(patients_title)
        
        self.patients_table = QTableWidget()
        self.patients_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                gridline-color: #dee2e6;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                color: #495057;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #e3f2fd;
            }
        """)
        self.patients_table.setColumnCount(3)
        self.patients_table.setHorizontalHeaderLabels(["👤 الاسم", "📱 رقم الموبايل", "🎂 العمر"])
        self.patients_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.patients_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.patients_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.patients_table.verticalHeader().setVisible(False)
        self.patients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.patients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.patients_table.itemClicked.connect(self.on_patient_selected)
        self.patients_table.setAlternatingRowColors(True)
        self.patients_table.setLayoutDirection(Qt.RightToLeft)
        
        patients_layout.addWidget(self.patients_table)
        
        # الحاوية اليمنى: تفاصيل المريض (أكثر جمالية ومرونة)
        self.details_container = self.create_patient_details_container()
        
        # إضافة الحاويات إلى التخطيط الرئيسي
        main_layout.addLayout(left_container, 1)  # نسبة 1
        main_layout.addWidget(patients_widget, 2)  # نسبة 2
        main_layout.addWidget(self.details_container, 2)  # نسبة 2
    
    def create_patient_details_container(self):
        """إنشاء حاوية تفاصيل المريض بتصميم جميل ومرن"""
        details_widget = QWidget()
        details_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        main_layout = QVBoxLayout(details_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # رأس التفاصيل مع الأزرار
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        self.patient_name_detail_label = QLabel("📋 تفاصيل المريض")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        self.patient_name_detail_label.setFont(title_font)
        self.patient_name_detail_label.setStyleSheet("""
            QLabel {
                color: #495057;
                padding: 5px 0px;
            }
        """)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffc107, stop: 1 #e0a800);
                border: 1px solid #e0a800;
                color: #212529;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e0a800, stop: 1 #d39e00);
                border-color: #d39e00;
            }
            QPushButton:disabled {
                background: #e9ecef;
                border-color: #ced4da;
                color: #6c757d;
            }
        """)
        self.edit_button.setEnabled(False)
        self.edit_button.clicked.connect(self.edit_patient)

        self.delete_button = QPushButton("🗑️ حذف")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #dc3545, stop: 1 #c82333);
                border: 1px solid #c82333;
                color: white;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #c82333, stop: 1 #bd2130);
                border-color: #bd2130;
            }
            QPushButton:disabled {
                background: #e9ecef;
                border-color: #ced4da;
                color: #6c757d;
            }
        """)
        self.delete_button.setEnabled(False)
        self.delete_button.clicked.connect(self.delete_patient)
        
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        
        header_layout.addWidget(self.patient_name_detail_label)
        header_layout.addStretch()
        header_layout.addLayout(buttons_layout)
        
        main_layout.addLayout(header_layout)
        
        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #dee2e6;")
        main_layout.addWidget(separator)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
        """)
        
        # محتوى التفاصيل
        details_content = QWidget()
        details_content_layout = QVBoxLayout(details_content)
        details_content_layout.setContentsMargins(5, 5, 5, 5)
        details_content_layout.setSpacing(12)
        
        # إنشاء حقول التفاصيل بتصميم جميل
        self.mobile_label = self.create_detail_field("📱 رقم الموبايل", "")
        self.whatsapp_label = self.create_detail_field("💬 رقم الواتساب", "")
        self.birth_year_label = self.create_detail_field("🎂 سنة الولادة", "")
        self.age_label = self.create_detail_field("👤 العمر", "")
        self.general_diseases_label = self.create_detail_field("🏥 الأمراض العامة", "", is_long_text=True)
        self.medications_label = self.create_detail_field("💊 الأدوية", "", is_long_text=True)
        self.notes_label = self.create_detail_field("📝 ملاحظات", "", is_long_text=True)
        
        # إضافة الحقول إلى التخطيط
        for field in [self.mobile_label, self.whatsapp_label, self.birth_year_label, 
                     self.age_label, self.general_diseases_label, 
                     self.medications_label, self.notes_label]:
            details_content_layout.addWidget(field)
        
        details_content_layout.addStretch()
        
        scroll_area.setWidget(details_content)
        main_layout.addWidget(scroll_area)
        
        return details_widget
    
    def create_detail_field(self, title, content, is_long_text=False):
        """إنشاء حقل تفاصيل بتصميم جميل"""
        container = QFrame()
        container.setFrameShape(QFrame.StyledPanel)
        container.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-weight: bold;
                font-size: 13px;
                margin-bottom: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # المحتوى
        content_label = QLabel(content if content else "لا توجد بيانات")
        content_label.setStyleSheet("""
            QLabel {
                color: #212529;
                font-size: 14px;
                padding: 4px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        content_label.setWordWrap(True)
        if is_long_text:
            content_label.setMinimumHeight(60)
            content_label.setAlignment(Qt.AlignTop)
        
        layout.addWidget(content_label)
        
        # حفظ مرجع للمحتوى للتحديث لاحقاً
        container.content_label = content_label
        
        return container
    
    def set_patient_details(self, patient_id):
        """تعيين تفاصيل المريض المحدد"""
        self.current_patient_id = patient_id
        patient_data = self.db_handler.get_patient(patient_id)
        
        if patient_data:
            # تحديث المعلومات السريعة في الحاوية اليسرى
            self.patient_name_label.setText(f"الاسم: {patient_data['name']}")
            self.patient_mobile_label.setText(f"الموبايل: {patient_data.get('mobile', 'غير محدد')}")
            
            birth_year = patient_data.get('birth_year')
            if birth_year:
                current_year = datetime.now().year
                age = current_year - int(birth_year)
                self.patient_age_label.setText(f"العمر: {age} سنة")
            else:
                self.patient_age_label.setText("العمر: غير محدد")
            
            # تحديث عنوان التفاصيل
            self.patient_name_detail_label.setText(f"📋 تفاصيل المريض: {patient_data['name']}")
            
            # تحديث حقول التفاصيل
            self.mobile_label.content_label.setText(patient_data.get('mobile', 'غير محدد'))
            self.whatsapp_label.content_label.setText(patient_data.get('whatsapp', 'غير محدد'))
            
            if birth_year:
                self.birth_year_label.content_label.setText(str(birth_year))
                self.age_label.content_label.setText(f"{age} سنة")
            else:
                self.birth_year_label.content_label.setText("غير محدد")
                self.age_label.content_label.setText("غير محدد")
            
            self.general_diseases_label.content_label.setText(
                patient_data.get('general_diseases', 'لا توجد أمراض مسجلة')
            )
            self.medications_label.content_label.setText(
                patient_data.get('medications', 'لا توجد أدوية مسجلة')
            )
            self.notes_label.content_label.setText(
                patient_data.get('notes', 'لا توجد ملاحظات')
            )
            
            # تفعيل الأزرار
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
        else:
            self.clear_patient_details()
    
    def clear_patient_details(self):
        """مسح تفاصيل المريض"""
        self.current_patient_id = None
        
        # مسح المعلومات السريعة
        self.patient_name_label.setText("الاسم: لم يتم اختيار مريض")
        self.patient_mobile_label.setText("الموبايل: --")
        self.patient_age_label.setText("العمر: --")
        
        # مسح عنوان التفاصيل
        self.patient_name_detail_label.setText("📋 تفاصيل المريض")
        
        # مسح حقول التفاصيل
        self.mobile_label.content_label.setText("لا توجد بيانات")
        self.whatsapp_label.content_label.setText("لا توجد بيانات")
        self.birth_year_label.content_label.setText("لا توجد بيانات")
        self.age_label.content_label.setText("لا توجد بيانات")
        self.general_diseases_label.content_label.setText("لا توجد بيانات")
        self.medications_label.content_label.setText("لا توجد بيانات")
        self.notes_label.content_label.setText("لا توجد بيانات")
        
        # تعطيل الأزرار
        self.edit_button.setEnabled(False)
        self.delete_button.setEnabled(False)
    
    def edit_patient(self):
        """تعديل بيانات المريض"""
        if not hasattr(self, 'current_patient_id') or not self.current_patient_id:
            return
        
        patient_data = self.db_handler.get_patient(self.current_patient_id)
        if not patient_data:
            return
        
        dialog = PatientDialog(self, patient_data)
        if dialog.exec_() == QDialog.Accepted:
            updated_data = dialog.get_patient_data()
            if self.db_handler.update_patient(
                self.current_patient_id,
                updated_data['name'],
                updated_data['birth_year'],
                updated_data['mobile'],
                updated_data['whatsapp'],
                updated_data['general_diseases'],
                updated_data['medications'],
                updated_data['notes']
            ):
                QMessageBox.information(self, "نجاح", "تم تحديث بيانات المريض بنجاح")
                self.load_patients()
                self.set_patient_details(self.current_patient_id)
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحديث بيانات المريض")
    
    def delete_patient(self):
        """حذف المريض"""
        if not hasattr(self, 'current_patient_id') or not self.current_patient_id:
            return
        
        # الحصول على بيانات المريض للتأكيد
        patient_data = self.db_handler.get_patient(self.current_patient_id)
        if not patient_data:
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المريض '{patient_data['name']}' وجميع بياناته المرتبطة؟\n\n"
            "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.db_handler.delete_patient(self.current_patient_id):
                QMessageBox.information(self, "نجاح", "تم حذف المريض بنجاح")
                self.load_patients()
                self.clear_patient_details()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حذف المريض")
    
    def load_patients(self):
        """تحميل قائمة المرضى"""
        patients = self.db_handler.get_all_patients()
        self.update_patients_table(patients)
    
    def search_patients(self):
        """البحث عن المرضى"""
        search_term = self.search_input.text().strip()
        if search_term:
            patients = self.db_handler.search_patients(search_term)
        else:
            patients = self.db_handler.get_all_patients()
        
        self.update_patients_table(patients)
    
    def update_patients_table(self, patients):
        """تحديث جدول المرضى"""
        self.patients_table.setRowCount(0)
        
        for row, patient in enumerate(patients):
            self.patients_table.insertRow(row)
            
            # اسم المريض
            name_item = QTableWidgetItem(patient['name'])
            name_item.setData(Qt.UserRole, patient['id'])  # تخزين معرف المريض
            self.patients_table.setItem(row, 0, name_item)
            
            # رقم الموبايل
            mobile_item = QTableWidgetItem(patient.get('mobile', ''))
            self.patients_table.setItem(row, 1, mobile_item)
            
            # العمر
            age = ""
            if patient.get('birth_year'):
                current_year = datetime.now().year
                age = str(current_year - int(patient['birth_year']))
            
            age_item = QTableWidgetItem(age)
            self.patients_table.setItem(row, 2, age_item)
    
    def on_patient_selected(self, item):
        """معالجة حدث اختيار المريض"""
        row = item.row()
        patient_id = self.patients_table.item(row, 0).data(Qt.UserRole)
        
        # تحديث تفاصيل المريض
        self.set_patient_details(patient_id)
        
        # إرسال إشارة اختيار المريض
        self.patient_selected.emit(patient_id)
    
    def add_patient(self):
        """إضافة مريض جديد"""
        dialog = PatientDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            patient_data = dialog.get_patient_data()
            
            # التحقق من صحة البيانات
            if not patient_data['name']:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المريض")
                return
            
            # إضافة المريض إلى قاعدة البيانات
            patient_id = self.db_handler.add_patient(
                patient_data['name'],
                patient_data['birth_year'],
                patient_data['mobile'],
                patient_data['whatsapp'],
                patient_data['general_diseases'],
                patient_data['medications'],
                patient_data['notes']
            )
            
            if patient_id:
                QMessageBox.information(self, "نجاح", "تمت إضافة المريض بنجاح")
                self.load_patients()
                
                # تحديد المريض الجديد في الجدول وعرض تفاصيله
                for row in range(self.patients_table.rowCount()):
                    if self.patients_table.item(row, 0).data(Qt.UserRole) == patient_id:
                        self.patients_table.selectRow(row)
                        self.set_patient_details(patient_id)
                        self.patient_selected.emit(patient_id)
                        break
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة المريض")