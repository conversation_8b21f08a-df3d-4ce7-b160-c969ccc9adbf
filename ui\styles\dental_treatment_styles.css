/* تنسيقات واجهة إدارة المعالجة السنية */

/* تنسيق عام للواجهة */
.dental-treatment-tab {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-family: 'Aria<PERSON>', 'Ta<PERSON><PERSON>', sans-serif;
    direction: rtl;
}

/* تنسيق مخطط الأسنان المدمج */
.compact-teeth-chart {
    background: white;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.compact-teeth-chart .title {
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 8px;
}

/* تنسيق أزرار الأسنان المدمجة */
.tooth-button-compact {
    background: linear-gradient(145deg, #ffffff, #f1f3f4);
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 8px;
    font-weight: bold;
    color: #495057;
    min-width: 25px;
    max-width: 25px;
    min-height: 30px;
    max-height: 30px;
    margin: 1px;
    transition: all 0.2s ease;
}

.tooth-button-compact:hover {
    background: linear-gradient(145deg, #e9ecef, #dee2e6);
    border-color: #007bff;
    transform: scale(1.05);
}

.tooth-button-compact.selected {
    background: linear-gradient(145deg, #007bff, #0056b3);
    color: white;
    border-color: #0056b3;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.4);
}

/* تنسيق خيارات المعالجة */
.treatment-options {
    background: white;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 10px 0;
}

.treatment-options .title {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

/* تنسيق مجموعات الخيارات */
.treatment-group {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin: 5px;
}

.treatment-group .group-title {
    font-weight: bold;
    font-size: 11px;
    color: #495057;
    margin-bottom: 8px;
    padding: 5px 10px;
    background: white;
    border-radius: 4px;
    text-align: center;
}

.treatment-group .checkbox {
    font-size: 10px;
    margin: 3px 0;
    color: #495057;
}

.treatment-group .checkbox:checked {
    color: #007bff;
    font-weight: bold;
}

/* تنسيق خطة المعالجة */
.treatment-plan {
    background: linear-gradient(145deg, #f8f9ff, #ffffff);
    border: 2px solid #007bff;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.1);
}

.treatment-plan .title {
    font-size: 14px;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 15px;
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #007bff;
}

.treatment-plan .form-row {
    margin: 10px 0;
    display: flex;
    align-items: center;
}

.treatment-plan .form-label {
    font-weight: bold;
    color: #495057;
    min-width: 150px;
    margin-left: 10px;
}

.treatment-plan .form-input {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    font-size: 12px;
}

.treatment-plan .form-input:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.treatment-plan .form-input:read-only {
    background-color: #f8f9fa;
    color: #6c757d;
}

.treatment-plan .total-cost {
    font-weight: bold;
    color: #28a745;
    font-size: 14px;
    text-align: center;
    padding: 8px;
    background: #d4edda;
    border-radius: 6px;
    border: 1px solid #c3e6cb;
}

/* تنسيق أزرار التحكم الرئيسية */
.main-control-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-start;
    margin: 20px 0;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.control-button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    font-size: 12px;
    min-width: 120px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-button.save {
    background: linear-gradient(145deg, #28a745, #20c997);
    color: white;
}

.control-button.save:hover {
    background: linear-gradient(145deg, #218838, #1e7e34);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.control-button.cancel {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: white;
}

.control-button.cancel:hover {
    background: linear-gradient(145deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.control-button.delete {
    background: linear-gradient(145deg, #dc3545, #c82333);
    color: white;
}

.control-button.delete:hover {
    background: linear-gradient(145deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.control-button.add-session {
    background: linear-gradient(145deg, #007bff, #0056b3);
    color: white;
}

.control-button.add-session:hover {
    background: linear-gradient(145deg, #0056b3, #004085);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* تنسيق الأزرار الجانبية */
.side-panel {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.side-panel .title {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 15px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.side-button {
    width: 100%;
    padding: 12px 15px;
    margin: 5px 0;
    background: linear-gradient(145deg, #17a2b8, #138496);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.side-button:hover {
    background: linear-gradient(145deg, #138496, #117a8b);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

/* تنسيق نافذة الجلسات */
.session-dialog {
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.session-dialog .header {
    background: linear-gradient(145deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border-radius: 10px 10px 0 0;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
}

.session-form {
    background: linear-gradient(145deg, #f8fff8, #ffffff);
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 20px;
    margin: 15px;
}

.session-form .title {
    font-size: 14px;
    font-weight: bold;
    color: #28a745;
    margin-bottom: 15px;
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #28a745;
}

/* تنسيق الجداول */
.data-table {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.data-table th {
    background: linear-gradient(145deg, #e9ecef, #f8f9fa);
    color: #495057;
    font-weight: bold;
    padding: 12px;
    text-align: center;
    border-bottom: 2px solid #dee2e6;
}

.data-table td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #f8f9fa;
}

.data-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.data-table tr:hover {
    background-color: #e3f2fd;
    cursor: pointer;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* تنسيق متجاوب */
@media (max-width: 1024px) {
    .treatment-options {
        flex-direction: column;
    }
    
    .treatment-group {
        margin: 5px 0;
    }
    
    .main-control-buttons {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .control-button {
        min-width: 100px;
        font-size: 11px;
        padding: 8px 15px;
    }
}

@media (max-width: 768px) {
    .tooth-button-compact {
        min-width: 20px;
        max-width: 20px;
        min-height: 25px;
        max-height: 25px;
        font-size: 7px;
    }
    
    .treatment-plan .form-label {
        min-width: 120px;
        font-size: 11px;
    }
    
    .treatment-plan .form-input {
        font-size: 11px;
        padding: 6px 10px;
    }
}
