#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام حفظ الأسعار الافتراضية الدائم
Test persistent default prices saving system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, PricesManager

class PersistentPricesTestWindow(QMainWindow):
    """نافذة اختبار نظام حفظ الأسعار الدائم"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار نظام حفظ الأسعار الافتراضية الدائم")
        self.setGeometry(50, 50, 1800, 1000)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار نظام حفظ الأسعار الافتراضية الدائم")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات النظام الجديد
        info_label = QLabel("""
        🎯 نظام حفظ الأسعار الافتراضية الدائم:
        
        ✅ المشكلة المحلولة:
        • قبل الإصلاح: الأسعار المعدلة تعود للقيم الافتراضية عند إعادة تشغيل التطبيق
        • بعد الإصلاح: الأسعار المعدلة تبقى محفوظة بشكل دائم عبر جميع جلسات التطبيق
        
        ✅ النظام الجديد المطبق:
        
        🔧 1. كلاس PricesManager:
        • إدارة شاملة لحفظ وتحميل الأسعار
        • ملف JSON لحفظ الأسعار: "dental_prices_config.json"
        • دوال: load_prices(), save_prices(), reset_to_defaults(), get_default_prices()
        
        💾 2. حفظ دائم للأسعار:
        • عند النقر على "حفظ" في نافذة تعديل الأسعار
        • حفظ فوري في ملف JSON مع ترميز UTF-8
        • الأسعار المحفوظة تصبح الأسعار الافتراضية الجديدة
        
        📂 3. تحميل الأسعار عند بدء التطبيق:
        • تحميل تلقائي للأسعار المحفوظة من ملف JSON
        • إذا لم يوجد ملف محفوظ، استخدام الأسعار الافتراضية المبرمجة
        • تطبيق الأسعار على جميع مجموعات العلاج (اللبية، الترميمية، التيجان، الجراحة)
        
        🔄 4. استعادة الأسعار الافتراضية:
        • زر "استعادة الافتراضي" يحذف ملف الأسعار المحفوظة
        • إعادة تعيين جميع الأسعار للقيم المبرمجة الأصلية
        • تطبيق فوري على الواجهة الرئيسية ونافذة التعديل
        
        ✅ الميزات المحققة:
        
        🎯 الثبات عبر الجلسات:
        • الأسعار المعدلة تبقى ثابتة عند إغلاق وإعادة فتح التطبيق
        • الأسعار المعدلة تبقى ثابتة عند إضافة خطط معالجة جديدة
        • الأسعار المعدلة تبقى ثابتة عند التنقل بين التبويبات
        
        🔧 التطبيق على جميع الفئات:
        • اللبية (Endodontic): 7 خيارات
        • الترميمية (Restorative): 5 خيارات  
        • التيجان (Crowns): 7 خيارات
        • الجراحة (Surgery): 7 خيارات (الخيارات الأصلية)
        
        💡 الوظائف المحفوظة:
        • زر "استعادة الافتراضي" يعيد القيم المبرمجة الأصلية
        • نظام الحساب التلقائي للكلفة
        • جميع التحسينات المطبقة مسبقاً (التخطيط الأفقي، نافذة تعديل الأسعار، إلخ)
        
        📁 ملف التكوين:
        • الاسم: "dental_prices_config.json"
        • الموقع: نفس مجلد التطبيق
        • التنسيق: JSON مع ترميز UTF-8
        • المحتوى: جميع أسعار الفئات الأربع مع مفاتيحها
        
        🔍 مثال على محتوى ملف التكوين:
        {
            "endodontic_Vital": 120000,
            "endodontic_Necrotic": 150000,
            "restorative_كومبوزت": 75000,
            "crowns_زيركون 4D": 200000,
            "surgery_قلع بسيط": 30000,
            ...
        }
        
        🎨 الفوائد المحققة:
        • حل نهائي لمشكلة فقدان الأسعار المعدلة
        • مرونة كاملة في تخصيص الأسعار لكل عيادة
        • نظام احترافي لإدارة الأسعار الافتراضية
        • ثبات الأسعار عبر جميع جلسات التطبيق
        • سهولة الاستعادة للأسعار الأصلية عند الحاجة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 10px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان
        treatment_title = QLabel("⚙️ تبويبة علاج الأسنان (مع نظام حفظ الأسعار الدائم)")
        treatment_title.setAlignment(Qt.AlignCenter)
        treatment_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(treatment_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab()
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار تعديل الأسعار
        edit_prices_btn = QPushButton("فتح نافذة تعديل الأسعار")
        edit_prices_btn.clicked.connect(self.open_prices_editor)
        edit_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(edit_prices_btn)
        
        # زر اختبار حفظ الأسعار
        save_prices_btn = QPushButton("حفظ الأسعار الحالية")
        save_prices_btn.clicked.connect(self.save_current_prices)
        save_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(save_prices_btn)
        
        # زر اختبار تحميل الأسعار
        load_prices_btn = QPushButton("تحميل الأسعار المحفوظة")
        load_prices_btn.clicked.connect(self.load_saved_prices)
        load_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(load_prices_btn)
        
        # زر اختبار استعادة الافتراضي
        reset_prices_btn = QPushButton("استعادة الأسعار الافتراضية")
        reset_prices_btn.clicked.connect(self.reset_to_defaults)
        reset_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(reset_prices_btn)
        
        # زر فحص ملف التكوين
        check_file_btn = QPushButton("فحص ملف التكوين")
        check_file_btn.clicked.connect(self.check_config_file)
        check_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        buttons_layout.addWidget(check_file_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار نظام حفظ الأسعار الدائم:
        
        🔍 ما يجب اختباره:
        • افتح "نافذة تعديل الأسعار" وعدل بعض الأسعار ثم احفظ
        • أغلق التطبيق وأعد فتحه - يجب أن تبقى الأسعار المعدلة
        • استخدم "حفظ الأسعار الحالية" لحفظ الأسعار الحالية
        • استخدم "تحميل الأسعار المحفوظة" لإعادة تحميل الأسعار
        • استخدم "استعادة الأسعار الافتراضية" لحذف الملف المحفوظ
        • استخدم "فحص ملف التكوين" لرؤية محتوى الملف المحفوظ
        
        🧪 سيناريوهات الاختبار:
        1. عدل أسعار → احفظ → أغلق التطبيق → أعد فتحه → تحقق من الأسعار
        2. استعد الافتراضي → تحقق من حذف الملف → أعد فتح التطبيق
        3. عدل أسعار → احفظ → استعد الافتراضي → تحقق من الاستعادة
        4. فحص ملف التكوين قبل وبعد التعديلات
        
        ✅ النتائج المتوقعة:
        • الأسعار المعدلة تبقى محفوظة عبر جلسات التطبيق
        • ملف "dental_prices_config.json" يتم إنشاؤه عند الحفظ
        • استعادة الافتراضي تحذف الملف وتعيد الأسعار الأصلية
        • تحميل الأسعار يطبق الأسعار المحفوظة فوراً
        • نظام الحساب التلقائي يعمل مع الأسعار الجديدة
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تطبيق نظام حفظ الأسعار الافتراضية الدائم بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def open_prices_editor(self):
        """فتح نافذة تعديل الأسعار"""
        self.dental_tab.edit_default_prices()
        
    def save_current_prices(self):
        """حفظ الأسعار الحالية"""
        success = self.dental_tab.treatment_options.save_custom_prices_permanently()
        if success:
            print("✅ تم حفظ الأسعار الحالية بنجاح")
        else:
            print("❌ فشل في حفظ الأسعار الحالية")
            
    def load_saved_prices(self):
        """تحميل الأسعار المحفوظة"""
        success = self.dental_tab.treatment_options.load_saved_prices()
        if success:
            print("✅ تم تحميل الأسعار المحفوظة بنجاح")
        else:
            print("❌ فشل في تحميل الأسعار المحفوظة")
            
    def reset_to_defaults(self):
        """استعادة الأسعار الافتراضية"""
        success = self.dental_tab.treatment_options.reset_prices_to_defaults()
        if success:
            print("✅ تم استعادة الأسعار الافتراضية بنجاح")
        else:
            print("❌ فشل في استعادة الأسعار الافتراضية")
            
    def check_config_file(self):
        """فحص ملف التكوين"""
        prices_manager = PricesManager()
        if os.path.exists(prices_manager.prices_file):
            try:
                with open(prices_manager.prices_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"📁 محتوى ملف التكوين ({prices_manager.prices_file}):")
                print(content)
            except Exception as e:
                print(f"❌ خطأ في قراءة ملف التكوين: {e}")
        else:
            print("📁 ملف التكوين غير موجود")

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = PersistentPricesTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
