from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QFrame, QLineEdit, QTextEdit,
                             QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QGroupBox, QScrollArea, QDialog, QTableWidget,
                             QTableWidgetItem, QHeaderView, QMessageBox,
                             QSplitter, QFormLayout, QSizePolicy, QDesktopWidget,
                             QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QPainter, QColor, QPen, QBrush, QFont
from datetime import datetime
import json
import os


class PricesManager:
    """مدير الأسعار الافتراضية - حفظ وتحميل الأسعار بشكل دائم"""

    def __init__(self):
        # مسار ملف حفظ الأسعار
        self.prices_file = "dental_prices_config.json"

        # الأسعار الافتراضية المبرمجة (للاستعادة)
        self.default_prices = {
            # اللبية
            "endodontic_Vital": 120000,
            "endodontic_Necrotic": 150000,
            "endodontic_إعادة معالجة": 200000,
            "endodontic_متكلسة": 180000,
            "endodontic_C shape": 250000,
            "endodontic_ذروة مفتوحة": 160000,
            "endodontic_أداة مكسورة": 300000,
            "endodontic_منحنية بشدة": 150000,  # خيار جديد

            # الترميمية
            "restorative_كومبوزت": 75000,
            "restorative_أملغم": 50000,
            "restorative_GIC": 40000,
            "restorative_وتد فايبر": 80000,
            "restorative_قلب معدني": 60000,
            "restorative_Onlay": 200000,  # خيار جديد
            "restorative_Inlay": 180000,  # خيار جديد
            "restorative_Rebond": 50000,  # خيار جديد

            # التيجان
            "crowns_خزف معدن": 150000,
            "crowns_زيركون 4D": 200000,
            "crowns_زيركون مغطى إيماكس": 250000,
            "crowns_زيركون مغطى خزف": 220000,
            "crowns_زيركون cutback": 230000,
            "crowns_ستانلس": 80000,
            "crowns_إيماكس": 180000,
            "crowns_زيركون Full Anatomy": 400000,  # خيار جديد

            # الجراحة - الخيارات الأصلية
            "surgery_قلع بسيط": 30000,
            "surgery_قلع جراحي": 75000,
            "surgery_منحصرة": 100000,
            "surgery_منطمرة": 120000,
            "surgery_تطويل تاج": 80000,
            "surgery_قطع ذروة": 90000,
            "surgery_تضحيك": 150000,
            "surgery_بتر جذر": 100000  # خيار جديد
        }

    def load_prices(self):
        """تحميل الأسعار المحفوظة أو الافتراضية"""
        try:
            if os.path.exists(self.prices_file):
                with open(self.prices_file, 'r', encoding='utf-8') as f:
                    saved_prices = json.load(f)

                # التحقق من وجود جميع المفاتيح المطلوبة
                for key in self.default_prices:
                    if key not in saved_prices:
                        saved_prices[key] = self.default_prices[key]

                print(f"تم تحميل الأسعار المحفوظة من {self.prices_file}")
                return saved_prices
            else:
                print("لم يتم العثور على ملف أسعار محفوظ، استخدام الأسعار الافتراضية")
                return self.default_prices.copy()

        except Exception as e:
            print(f"خطأ في تحميل الأسعار: {e}")
            print("استخدام الأسعار الافتراضية")
            return self.default_prices.copy()

    def save_prices(self, prices):
        """حفظ الأسعار بشكل دائم"""
        try:
            with open(self.prices_file, 'w', encoding='utf-8') as f:
                json.dump(prices, f, ensure_ascii=False, indent=4)
            print(f"تم حفظ الأسعار في {self.prices_file}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ الأسعار: {e}")
            return False

    def reset_to_defaults(self):
        """إعادة تعيين الأسعار للقيم الافتراضية المبرمجة"""
        try:
            if os.path.exists(self.prices_file):
                os.remove(self.prices_file)
                print("تم حذف ملف الأسعار المحفوظة")
            return self.default_prices.copy()
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار: {e}")
            return self.default_prices.copy()

    def get_default_prices(self):
        """الحصول على الأسعار الافتراضية المبرمجة"""
        return self.default_prices.copy()


class ToothButton(QPushButton):
    """زر سن تفاعلي"""
    tooth_selected = pyqtSignal(int)

    def __init__(self, tooth_number, parent=None):
        super().__init__(parent)
        self.tooth_number = tooth_number
        self.is_selected = False
        self.setFixedSize(33, 40)  # تكبير إضافي لوضوح أفضل للأرقام
        self.setText(str(tooth_number))
        self.setCheckable(True)
        self.clicked.connect(self.on_clicked)
        self.update_style()

    def on_clicked(self):
        """عند النقر على السن"""
        self.is_selected = not self.is_selected
        self.tooth_selected.emit(self.tooth_number)
        self.update_style()

    def set_selected(self, selected):
        """تعيين حالة التحديد"""
        self.is_selected = selected
        self.setChecked(selected)
        self.update_style()

    def update_style(self):
        """تحديث تنسيق الزر مع تحسين وضوح الأرقام"""
        if self.is_selected:
            self.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: 2px solid #0056b3;
                    border-radius: 5px;
                    font-size: 11px;
                    font-weight: bold;
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    padding: 2px;
                }
            """)
        else:
            self.setStyleSheet("""
                QPushButton {
                    background-color: #f8f9fa;
                    color: #0000FF; /* Blue color for labels */
                    border: 1px solid #dee2e6;
                    border-radius: 5px;
                    font-size: 11px;
                    font-weight: bold;
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: #e9ecef;
                    border-color: #007bff;
                    color: #007bff;
                }
            """)

class CompactTeethChart(QWidget):
    """مخطط أسنان مدمج للبالغين"""
    tooth_selected = pyqtSignal(int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_tooth = None
        self.tooth_buttons = {}
        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة مخطط الأسنان بدون عنوان نصي
        group = QGroupBox()
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                right: 10px;
                padding: 2px 8px 2px 8px;
                color: #28a745;
                text-align: right;
            }
        """)

        # تخطيط المجموعة (هوامش مقللة لتوفير المساحة)
        group_layout = QVBoxLayout(group)
        group_layout.setContentsMargins(8, 12, 8, 8)  # تقليل الهوامش
        group_layout.setSpacing(3)  # تقليل المسافات

        # حاوية المخطط مع الخطوط الفاصلة
        chart_container = QWidget()
        chart_container.setFixedSize(560, 90)

        # إنشاء تخطيط مطلق للتحكم في موضع الخطوط والأزرار
        chart_layout = QGridLayout(chart_container)
        chart_layout.setContentsMargins(2, 2, 2, 2)
        chart_layout.setSpacing(2)

        # إضافة الخطوط الفاصلة
        self.add_separator_lines(chart_container)

        # إنشاء أزرار الأسنان
        self.create_teeth_buttons(chart_layout)

        # إضافة حاوية المخطط إلى المجموعة
        group_layout.addWidget(chart_container, 0, Qt.AlignCenter)

        # إضافة المجموعة إلى التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(group)

    def create_teeth_buttons(self, layout):
        """إنشاء أزرار الأسنان"""
        # الفك العلوي (من اليمين إلى اليسار)
        upper_teeth = [18, 17, 16, 15, 14, 13, 12, 11, 21, 22, 23, 24, 25, 26, 27, 28]
        for i, tooth in enumerate(upper_teeth):
            btn = ToothButton(tooth)
            btn.tooth_selected.connect(self.on_tooth_selected)
            self.tooth_buttons[tooth] = btn
            layout.addWidget(btn, 0, i)

        # الفك السفلي (من اليمين إلى اليسار)
        lower_teeth = [48, 47, 46, 45, 44, 43, 42, 41, 31, 32, 33, 34, 35, 36, 37, 38]
        for i, tooth in enumerate(lower_teeth):
            btn = ToothButton(tooth)
            btn.tooth_selected.connect(self.on_tooth_selected)
            self.tooth_buttons[tooth] = btn
            layout.addWidget(btn, 1, i)

    def on_tooth_selected(self, tooth_number):
        """معالجة اختيار السن"""
        # إلغاء تحديد السن السابق
        if self.selected_tooth and self.selected_tooth != tooth_number:
            self.tooth_buttons[self.selected_tooth].set_selected(False)

        self.selected_tooth = tooth_number
        self.tooth_selected.emit(tooth_number)

    def get_selected_tooth(self):
        """الحصول على السن المحدد"""
        return self.selected_tooth

    def add_separator_lines(self, container):
        """إضافة الخطوط الفاصلة في مخطط الأسنان"""
        # إنشاء خط عمودي يفصل بين اليمين واليسار
        vertical_line = QFrame(container)
        vertical_line.setFrameShape(QFrame.VLine)
        vertical_line.setFrameShadow(QFrame.Sunken)
        vertical_line.setStyleSheet("""
            QFrame {
                color: #dee2e6;
                background-color: #dee2e6;
                border: none;
                max-width: 2px;
            }
        """)
        vertical_line.setGeometry(278, 5, 2, 80)  # وسط المخطط عمودياً

        # إنشاء خط أفقي يفصل بين العلوي والسفلي
        horizontal_line = QFrame(container)
        horizontal_line.setFrameShape(QFrame.HLine)
        horizontal_line.setFrameShadow(QFrame.Sunken)
        horizontal_line.setStyleSheet("""
            QFrame {
                color: #dee2e6;
                background-color: #dee2e6;
                border: none;
                max-height: 2px;
            }
        """)
        horizontal_line.setGeometry(5, 43, 550, 2)  # وسط المخطط أفقياً

class TreatmentOptionsWidget(QWidget):
    """حاوية خيارات المعالجة"""
    options_changed = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.checkboxes = {}
        self.price_spinboxes = {}  # قاموس لحفظ حقول الأسعار

        # إنشاء مدير الأسعار
        self.prices_manager = PricesManager()

        # تحميل الأسعار والأسماء المحفوظة
        self.current_prices = self.prices_manager.load_prices()
        self.current_names = self.load_treatment_names()

        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة خيارات المعالجة مع نفس تنسيق خطة المعالجة
        group = QGroupBox("خيارات المعالجة")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                color: #28a745;
            }
        """)

        # تخطيط أفقي مباشرة داخل المجموعة (صف واحد)
        self.horizontal_layout = QHBoxLayout(group)
        self.horizontal_layout.setSpacing(10)  # مسافة أقل بين المجموعات
        self.horizontal_layout.setContentsMargins(10, 25, 10, 15)

        # إنشاء المجموعات الأربعة مع عرض محسن
        self.endodontic_group = self.create_endodontic_group()
        self.restorative_group = self.create_restorative_group()
        self.crowns_group = self.create_crowns_group()
        self.surgery_group = self.create_surgery_group()

        # إنشاء مجموعات الأسعار المقابلة
        self.endodontic_prices_group = self.create_endodontic_prices_group()
        self.restorative_prices_group = self.create_restorative_prices_group()
        self.crowns_prices_group = self.create_crowns_prices_group()
        self.surgery_prices_group = self.create_surgery_prices_group()

        # ترتيب المجموعات في صف أفقي واحد من اليمين إلى اليسار
        # [لبية] [أسعار اللبية] [ترميمية] [أسعار الترميمية] [تيجان] [أسعار التيجان] [جراحة] [أسعار الجراحة]
        self.horizontal_layout.addWidget(self.endodontic_group)
        self.horizontal_layout.addWidget(self.endodontic_prices_group)
        self.horizontal_layout.addWidget(self.restorative_group)
        self.horizontal_layout.addWidget(self.restorative_prices_group)
        self.horizontal_layout.addWidget(self.crowns_group)
        self.horizontal_layout.addWidget(self.crowns_prices_group)
        self.horizontal_layout.addWidget(self.surgery_group)
        self.horizontal_layout.addWidget(self.surgery_prices_group)

        # تعيين نسب التمدد المتساوية لجميع المجموعات الثمانية
        for i in range(8):
            self.horizontal_layout.setStretch(i, 1)

        # إضافة المجموعة إلى التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(group)

        # تطبيق التخطيط الأولي بناءً على الحجم الحالي
        self.adjust_layout_for_width(self.width())

    def load_treatment_names(self):
        """تحميل أسماء المعالجات من ملف التكوين"""
        try:
            config_file = "dental_prices_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get('treatment_names', {})
            return {}
        except Exception as e:
            print(f"خطأ في تحميل أسماء المعالجات: {e}")
            return {}

    def get_treatment_name(self, category, default_name):
        """الحصول على اسم المعالجة (مخصص أو افتراضي)"""
        key = f"{category}_{default_name}"
        return self.current_names.get(key, default_name)

    def update_prices(self, updated_prices):
        """تحديث الأسعار في الواجهة"""
        try:
            self.current_prices.update(updated_prices)

            # تطبيق الأسعار الجديدة على حقول الأسعار
            for key, price in updated_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            # إعادة حساب الكلفة الإجمالية
            self.calculate_total_cost()
            print("تم تحديث الأسعار في واجهة خيارات المعالجة")
        except Exception as e:
            print(f"خطأ في تحديث الأسعار: {e}")

    def refresh_options(self):
        """تحديث خيارات المعالجة من ملف التكوين"""
        try:
            # إعادة تحميل البيانات
            self.current_prices = self.prices_manager.load_prices()
            self.current_names = self.load_treatment_names()

            # تحديث أسماء المعالجات في مربعات الاختيار
            self.update_treatment_names()

            # تحديث الأسعار في حقول الأسعار
            for key, price in self.current_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            # إعادة حساب الكلفة
            self.calculate_total_cost()
            print("تم تحديث خيارات المعالجة من ملف التكوين")
        except Exception as e:
            print(f"خطأ في تحديث خيارات المعالجة: {e}")

    def update_treatment_names(self):
        """تحديث أسماء المعالجات في مربعات الاختيار"""
        try:
            for key, checkbox in self.checkboxes.items():
                if '_' in key:
                    category, default_name = key.split('_', 1)
                    custom_name = self.get_treatment_name(category, default_name)
                    checkbox.setText(custom_name)
        except Exception as e:
            print(f"خطأ في تحديث أسماء المعالجات: {e}")

    def create_endodontic_group(self):
        """إنشاء مجموعة اللبية"""
        group = QGroupBox("لبية (Endodontic)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        default_options = ["Vital", "Necrotic", "إعادة معالجة", "متكلسة",
                          "C shape", "ذروة مفتوحة", "أداة مكسورة", "منحنية بشدة"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار مع عرض محسن
        for default_option in default_options:
            # استخدام الاسم المخصص أو الافتراضي
            display_name = self.get_treatment_name("endodontic", default_option)
            checkbox = QCheckBox(display_name)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع عرض أكبر لضمان ظهور جميع النصوص بالكامل
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"endodontic_{default_option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_endodontic_prices_group(self):
        """إنشاء مجموعة أسعار اللبية"""
        group = QGroupBox("أسعار اللبية")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات اللبية مع الأسعار المحفوظة
        endodontic_options = ["Vital", "Necrotic", "إعادة معالجة", "متكلسة",
                             "C shape", "ذروة مفتوحة", "أداة مكسورة", "منحنية بشدة"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in endodontic_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"endodontic_{option}"
            saved_price = self.current_prices.get(price_key, 0)
            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"endodontic_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_restorative_group(self):
        """إنشاء مجموعة الترميمية"""
        group = QGroupBox("ترميمية (Restorative)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        default_options = ["كومبوزت", "أملغم", "GIC", "وتد فايبر", "قلب معدني", "Onlay", "Inlay", "Rebond"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for default_option in default_options:
            # استخدام الاسم المخصص أو الافتراضي
            display_name = self.get_treatment_name("restorative", default_option)
            checkbox = QCheckBox(display_name)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"restorative_{default_option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_restorative_prices_group(self):
        """إنشاء مجموعة أسعار الترميمية"""
        group = QGroupBox("أسعار الترميمية")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات الترميمية مع الأسعار المحفوظة
        restorative_options = ["كومبوزت", "أملغم", "GIC", "وتد فايبر", "قلب معدني", "Onlay", "Inlay", "Rebond"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in restorative_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"restorative_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"restorative_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_crowns_group(self):
        """إنشاء مجموعة التيجان"""
        group = QGroupBox("تيجان (Crowns)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(170)  # عرض أكبر قليلاً للنصوص الطويلة مثل "زيركون مغطى إيماكس"
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        default_options = ["خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس",
                          "زيركون مغطى خزف", "زيركون cutback", "ستانلس", "إيماكس", "زيركون Full Anatomy"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for default_option in default_options:
            # استخدام الاسم المخصص أو الافتراضي
            display_name = self.get_treatment_name("crowns", default_option)
            checkbox = QCheckBox(display_name)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 140px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"crowns_{default_option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_crowns_prices_group(self):
        """إنشاء مجموعة أسعار التيجان"""
        group = QGroupBox("أسعار التيجان")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(170)  # عرض أكبر قليلاً لمطابقة مجموعة التيجان
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات التيجان مع الأسعار المحفوظة
        crowns_options = ["خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس",
                         "زيركون مغطى خزف", "زيركون cutback", "ستانلس", "إيماكس", "زيركون Full Anatomy"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in crowns_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"crowns_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"crowns_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_surgery_group(self):
        """إنشاء مجموعة الجراحة"""
        group = QGroupBox("جراحة (Surgery)")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        group.setAlignment(Qt.AlignCenter)  # توسيط عنوان المجموعة

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للمربعات مع تخطيط عمودي ومحاذاة نص موحدة
        checkboxes_widget = QWidget()
        checkboxes_layout = QVBoxLayout(checkboxes_widget)
        checkboxes_layout.setSpacing(4)  # مسافة موحدة بين المربعات
        checkboxes_layout.setContentsMargins(0, 0, 0, 0)
        checkboxes_layout.setAlignment(Qt.AlignLeft)  # محاذاة يسار لتوحيد نقطة البداية

        default_options = ["قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة",
                          "تطويل تاج", "قطع ذروة", "تضحيك", "بتر جذر"]

        # تطبيق محاذاة نص موحدة لجميع مربعات الاختيار
        for default_option in default_options:
            # استخدام الاسم المخصص أو الافتراضي
            display_name = self.get_treatment_name("surgery", default_option)
            checkbox = QCheckBox(display_name)
            checkbox.stateChanged.connect(self.on_option_changed)
            # تنسيق محسن مع خط كبير لأقصى وضوح
            checkbox.setStyleSheet("""
                QCheckBox {
                    font-size: 14px;
                    margin: 0px;
                    padding: 3px;
                    text-align: left;
                    min-width: 130px;
                    min-height: 22px;
                }
            """)
            # محاذاة يسار لتوحيد نقطة بداية النص
            checkboxes_layout.addWidget(checkbox, 0, Qt.AlignLeft)
            self.checkboxes[f"surgery_{default_option}"] = checkbox

        # إضافة حاوية المربعات إلى التخطيط الرئيسي
        main_layout.addWidget(checkboxes_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def create_surgery_prices_group(self):
        """إنشاء مجموعة أسعار الجراحة"""
        group = QGroupBox("أسعار الجراحة")
        group.setStyleSheet(self.get_group_style())
        group.setMinimumWidth(160)  # عرض محسن لاستغلال المساحة الأفقية
        group.setMinimumHeight(470)  # ارتفاع محسن لعرض 13 خيار علاجي
        group.setMaximumHeight(480)  # حد أقصى معدل لمنع التوسع المفرط
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        # تخطيط رئيسي لتوسيط العمود
        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(10, 15, 10, 10)
        main_layout.setAlignment(Qt.AlignCenter)

        # حاوية للحقول مع تخطيط عمودي
        prices_widget = QWidget()
        prices_layout = QVBoxLayout(prices_widget)
        prices_layout.setSpacing(4)
        prices_layout.setContentsMargins(0, 0, 0, 0)
        prices_layout.setAlignment(Qt.AlignLeft)

        # خيارات الجراحة الأصلية مع الأسعار المحفوظة
        surgery_options = ["قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة",
                          "تطويل تاج", "قطع ذروة", "تضحيك", "بتر جذر"]

        # إنشاء حقول الأسعار باستخدام الأسعار المحفوظة
        for option in surgery_options:
            # الحصول على السعر المحفوظ أو الافتراضي
            price_key = f"surgery_{option}"
            saved_price = self.current_prices.get(price_key, 0)

            price_spinbox = QSpinBox()
            price_spinbox.setMinimum(0)
            price_spinbox.setMaximum(999999999)
            price_spinbox.setValue(saved_price)
            price_spinbox.setButtonSymbols(QSpinBox.NoButtons)
            price_spinbox.setAlignment(Qt.AlignCenter)
            price_spinbox.setStyleSheet("""
                QSpinBox {
                    font-size: 11px;
                    margin: 0px;
                    padding: 3px;
                    min-width: 120px;
                    min-height: 22px;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: white;
                }
            """)

            # ربط تغيير السعر بإعادة حساب الكلفة
            price_spinbox.valueChanged.connect(self.calculate_total_cost)

            prices_layout.addWidget(price_spinbox, 0, Qt.AlignLeft)
            self.price_spinboxes[f"surgery_{option}"] = price_spinbox

        # إضافة حاوية الأسعار إلى التخطيط الرئيسي
        main_layout.addWidget(prices_widget, 0, Qt.AlignCenter)
        main_layout.addStretch()
        return group

    def get_group_style(self):
        """الحصول على تنسيق المجموعة المحسن"""
        return """
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 12px;
                background-color: #ffffff;
                min-height: 210px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                background-color: #f8f9fa;
                border-radius: 4px;
                color: #007bff;
            }
            QCheckBox {
                font-size: 14px;
                margin: 2px 5px;
                padding: 2px;
                color: #0000FF;
            }
            QCheckBox:hover {
                background-color: #f8f9fa;
                border-radius: 3px;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border-radius: 2px;
                border: 1px solid #ced4da;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #007bff;
                border-color: #007bff;
            }
        """

    def on_option_changed(self):
        """عند تغيير أي خيار - حساب الكلفة التلقائي"""
        self.calculate_total_cost()
        self.options_changed.emit()

    def calculate_total_cost(self):
        """حساب الكلفة الإجمالية بناءً على الخيارات المحددة والأسعار"""
        try:
            total_cost = 0

            # التحقق من جميع مربعات الاختيار المحددة
            for checkbox_key, checkbox in self.checkboxes.items():
                if checkbox.isChecked():
                    # البحث عن السعر المقابل في حقول الأسعار
                    if checkbox_key in self.price_spinboxes:
                        price = self.price_spinboxes[checkbox_key].value()
                        total_cost += price

            # تحديث حقل الكلفة في خطة المعالجة
            self.update_cost_field(total_cost)

        except Exception as e:
            print(f"خطأ في حساب الكلفة الإجمالية: {e}")

    def update_cost_field(self, total_cost):
        """تحديث حقل الكلفة في خطة المعالجة"""
        try:
            # البحث عن حقل الكلفة في الواجهة الرئيسية
            parent_widget = self.parent()
            while parent_widget:
                if hasattr(parent_widget, 'treatment_plan'):
                    cost_spinbox = parent_widget.treatment_plan.cost_spinbox
                    cost_spinbox.setValue(total_cost)
                    break
                parent_widget = parent_widget.parent()
        except Exception as e:
            print(f"خطأ في تحديث حقل الكلفة: {e}")

    def get_custom_prices(self):
        """الحصول على الأسعار المخصصة"""
        custom_prices = {}
        for key, spinbox in self.price_spinboxes.items():
            custom_prices[key] = spinbox.value()
        return custom_prices

    def save_custom_prices_permanently(self):
        """حفظ الأسعار المخصصة بشكل دائم"""
        try:
            current_prices = self.get_custom_prices()
            success = self.prices_manager.save_prices(current_prices)
            if success:
                # تحديث الأسعار الحالية
                self.current_prices = current_prices
                print("تم حفظ الأسعار المخصصة بشكل دائم")
                return True
            else:
                print("فشل في حفظ الأسعار المخصصة")
                return False
        except Exception as e:
            print(f"خطأ في حفظ الأسعار المخصصة: {e}")
            return False

    def load_saved_prices(self):
        """تحميل الأسعار المحفوظة وتطبيقها"""
        try:
            # تحميل الأسعار المحفوظة
            self.current_prices = self.prices_manager.load_prices()

            # تطبيق الأسعار على حقول الأسعار
            for key, price in self.current_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            print("تم تحميل الأسعار المحفوظة وتطبيقها")
            return True
        except Exception as e:
            print(f"خطأ في تحميل الأسعار المحفوظة: {e}")
            return False

    def reset_prices_to_defaults(self):
        """إعادة تعيين الأسعار للقيم الافتراضية المبرمجة"""
        try:
            # إعادة تعيين الأسعار للافتراضية
            default_prices = self.prices_manager.reset_to_defaults()

            # تطبيق الأسعار الافتراضية على حقول الأسعار
            for key, price in default_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)

            # تحديث الأسعار الحالية
            self.current_prices = default_prices

            print("تم إعادة تعيين الأسعار للقيم الافتراضية")
            return True
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار: {e}")
            return False

    def set_custom_prices(self, custom_prices):
        """تعيين الأسعار المخصصة"""
        try:
            for key, price in custom_prices.items():
                if key in self.price_spinboxes:
                    self.price_spinboxes[key].setValue(price)
        except Exception as e:
            print(f"خطأ في تعيين الأسعار المخصصة: {e}")

    def reset_to_default_prices(self):
        """إعادة تعيين الأسعار الافتراضية"""
        try:
            # أسعار افتراضية لجميع الفئات
            default_prices = {
                # اللبية
                "endodontic_Vital": 120000,
                "endodontic_Necrotic": 150000,
                "endodontic_إعادة معالجة": 200000,
                "endodontic_متكلسة": 180000,
                "endodontic_C shape": 250000,
                "endodontic_ذروة مفتوحة": 160000,
                "endodontic_أداة مكسورة": 300000,

                # الترميمية
                "restorative_كومبوزت": 75000,
                "restorative_أملغم": 50000,
                "restorative_GIC": 40000,
                "restorative_وتد فايبر": 80000,
                "restorative_قلب معدني": 60000,

                # التيجان
                "crowns_خزف معدن": 150000,
                "crowns_زيركون 4D": 200000,
                "crowns_زيركون مغطى إيماكس": 250000,
                "crowns_زيركون مغطى خزف": 220000,
                "crowns_زيركون cutback": 230000,
                "crowns_ستانلس": 80000,
                "crowns_إيماكس": 180000,

                # الجراحة - الخيارات الأصلية
                "surgery_قلع بسيط": 30000,
                "surgery_قلع جراحي": 75000,
                "surgery_منحصرة": 100000,
                "surgery_منطمرة": 120000,
                "surgery_تطويل تاج": 80000,
                "surgery_قطع ذروة": 90000,
                "surgery_تضحيك": 150000
            }

            self.set_custom_prices(default_prices)
        except Exception as e:
            print(f"خطأ في إعادة تعيين الأسعار الافتراضية: {e}")

    def get_selected_options(self):
        """الحصول على الخيارات المحددة"""
        selected = []
        for checkbox in self.checkboxes.values():
            if checkbox.isChecked():
                selected.append(checkbox.text())
        return selected

    def clear_all_options(self):
        """مسح جميع الخيارات"""
        for checkbox in self.checkboxes.values():
            checkbox.setChecked(False)

    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة لضمان الاستجابة"""
        super().resizeEvent(event)

        # تعديل التخطيط بناءً على العرض الجديد
        self.adjust_layout_for_width(self.width())

        # إجبار إعادة حساب التخطيط
        self.updateGeometry()

    def adjust_layout_for_width(self, width):
        """تعديل التخطيط بناءً على عرض النافذة"""
        if not hasattr(self, 'grid_layout'):
            return

        # إذا كان العرض صغيراً، رتب المجموعات في عمود واحد
        if width < 800:
            # ترتيب عمودي (4x1)
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 1, 0)
            self.grid_layout.addWidget(self.crowns_group, 2, 0)
            self.grid_layout.addWidget(self.surgery_group, 3, 0)

            # إعادة تعيين نسب التمدد
            for i in range(4):
                self.grid_layout.setRowStretch(i, 1)
            self.grid_layout.setColumnStretch(0, 1)

        elif width < 1200:
            # ترتيب في عمودين (2x2)
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 0, 1)
            self.grid_layout.addWidget(self.crowns_group, 1, 0)
            self.grid_layout.addWidget(self.surgery_group, 1, 1)

            # إعادة تعيين نسب التمدد
            self.grid_layout.setColumnStretch(0, 1)
            self.grid_layout.setColumnStretch(1, 1)
            self.grid_layout.setRowStretch(0, 1)
            self.grid_layout.setRowStretch(1, 1)

        else:
            # ترتيب أفقي (1x4) للشاشات الكبيرة
            self.grid_layout.addWidget(self.endodontic_group, 0, 0)
            self.grid_layout.addWidget(self.restorative_group, 0, 1)
            self.grid_layout.addWidget(self.crowns_group, 0, 2)
            self.grid_layout.addWidget(self.surgery_group, 0, 3)

            # إعادة تعيين نسب التمدد
            for i in range(4):
                self.grid_layout.setColumnStretch(i, 1)
            self.grid_layout.setRowStretch(0, 1)

    def showEvent(self, event):
        """عند عرض الواجهة لأول مرة"""
        super().showEvent(event)
        # تطبيق التخطيط المناسب بناءً على الحجم الحالي
        self.adjust_layout_for_width(self.width())

class TreatmentPlanWidget(QWidget):
    """حاوية خطة المعالجة السنية"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # إنشاء مجموعة أزرار الحالة
        self.status_button_group = None
        self.active_radio = None
        self.completed_radio = None
        self.init_ui()

    def init_ui(self):
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء مجموعة خطة المعالجة
        group = QGroupBox("خطة المعالجة السنية")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                color: #28a745;
            }
        """)

        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 10)

        # الصف الأفقي الواحد - جميع الحقول في صف واحد
        horizontal_layout = QHBoxLayout()
        horizontal_layout.setSpacing(10)

        # رقم السن - ارتفاع موحد
        tooth_layout = QVBoxLayout()
        tooth_label = QLabel("رقم السن")
        tooth_label.setAlignment(Qt.AlignCenter)
        tooth_label.setFixedWidth(100)
        tooth_label.setFixedHeight(20)  # ارتفاع موحد للتسمية
        tooth_layout.addWidget(tooth_label)
        self.tooth_number_edit = QLineEdit()
        self.tooth_number_edit.setReadOnly(True)
        self.tooth_number_edit.setPlaceholderText("السن")
        self.tooth_number_edit.setFixedWidth(100)
        self.tooth_number_edit.setFixedHeight(32)  # ارتفاع موحد
        self.tooth_number_edit.setAlignment(Qt.AlignCenter)
        tooth_layout.addWidget(self.tooth_number_edit)
        horizontal_layout.addLayout(tooth_layout)

        # المعالجة السنية - ارتفاع موحد
        treatment_layout = QVBoxLayout()
        treatment_label = QLabel("المعالجة السنية")
        treatment_label.setAlignment(Qt.AlignCenter)
        treatment_label.setFixedHeight(20)  # ارتفاع موحد للتسمية
        treatment_layout.addWidget(treatment_label)
        self.treatment_text = QTextEdit()
        self.treatment_text.setFixedHeight(32)  # ارتفاع موحد مع باقي الحقول
        self.treatment_text.setPlaceholderText("تفاصيل المعالجة")
        self.treatment_text.setMinimumWidth(200)
        # إزالة أسهم التمرير
        self.treatment_text.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.treatment_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        treatment_layout.addWidget(self.treatment_text)
        horizontal_layout.addLayout(treatment_layout)

        # الكلفة - ارتفاع موحد
        cost_layout = QVBoxLayout()
        cost_label = QLabel("الكلفة")
        cost_label.setAlignment(Qt.AlignCenter)
        cost_label.setFixedWidth(150)
        cost_label.setFixedHeight(20)  # ارتفاع موحد للتسمية
        cost_layout.addWidget(cost_label)
        self.cost_spinbox = QSpinBox()
        self.cost_spinbox.setMinimum(0)
        self.cost_spinbox.setMaximum(999999999)
        self.cost_spinbox.setFixedWidth(150)
        self.cost_spinbox.setFixedHeight(32)  # ارتفاع موحد
        self.cost_spinbox.setAlignment(Qt.AlignCenter)
        self.cost_spinbox.setButtonSymbols(QSpinBox.NoButtons)
        # Configure empty display - this makes 0 appear as empty
        self.cost_spinbox.setSpecialValueText("")
        self.cost_spinbox.setValue(0)
        # Force empty display by clearing the line edit text
        self.cost_spinbox.lineEdit().clear()
        cost_layout.addWidget(self.cost_spinbox)
        horizontal_layout.addLayout(cost_layout)

        # التاريخ - ارتفاع موحد
        date_layout = QVBoxLayout()
        date_label = QLabel("التاريخ")
        date_label.setAlignment(Qt.AlignCenter)
        date_label.setFixedHeight(20)  # ارتفاع موحد للتسمية
        date_layout.addWidget(date_label)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(False)
        self.date_edit.setFixedWidth(120)
        self.date_edit.setFixedHeight(32)  # ارتفاع موحد
        self.date_edit.setButtonSymbols(QDateEdit.NoButtons)
        date_layout.addWidget(self.date_edit)
        horizontal_layout.addLayout(date_layout)

        # إضافة مربعي اختيار حالة خطة المعالجة في نفس الصف
        self.create_status_radio_buttons_inline(horizontal_layout)

        # إضافة مساحة مرنة في النهاية
        horizontal_layout.addStretch()

        layout.addLayout(horizontal_layout)

        # تطبيق تنسيق الحدود المستقيمة الموحد على جميع الحقول والتسميات
        straight_border_style = """
            QLineEdit, QTextEdit, QSpinBox, QDateEdit {
                border: 2px solid #ced4da;
                border-radius: 0px;
                padding: 6px;
                font-size: 14px;
                font-weight: normal;
                background-color: white;
                min-height: 22px;
                max-height: 38px;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDateEdit:focus {
                border: 2px solid #007bff;
                background-color: #f8f9ff;
                outline: none;
            }
            QLineEdit:hover, QTextEdit:hover, QSpinBox:hover, QDateEdit:hover {
                border: 2px solid #adb5bd;
            }
        """

        # تنسيق التسميات بحدود مستقيمة متطابقة مع الحقول
        labels_straight_border_style = """
            QLabel {
                border: 2px solid #ced4da;
                border-radius: 0px;
                padding: 4px;
                font-size: 14px;
                font-weight: bold;
                background-color: #f8f9fa;
                color: #007bff;
            }
        """

        # تطبيق التنسيق على جميع الحقول
        self.tooth_number_edit.setStyleSheet(straight_border_style)
        self.treatment_text.setStyleSheet(straight_border_style)
        self.cost_spinbox.setStyleSheet(straight_border_style)
        self.date_edit.setStyleSheet(straight_border_style)

        # تطبيق التنسيق على جميع التسميات الخمس
        tooth_label.setStyleSheet(labels_straight_border_style)
        treatment_label.setStyleSheet(labels_straight_border_style)
        cost_label.setStyleSheet(labels_straight_border_style)
        date_label.setStyleSheet(labels_straight_border_style)

        # تخطيط رئيسي
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(group)

    def create_status_radio_buttons_inline(self, horizontal_layout):
        """إنشاء مربعي اختيار حالة خطة المعالجة في نفس الصف الأفقي"""

        # إنشاء مجموعة الأزرار لضمان اختيار واحد فقط
        self.status_button_group = QButtonGroup()

        # حاوية مربعي الاختيار مع تسمية
        status_layout = QVBoxLayout()
        status_layout.setSpacing(5)
        status_layout.setContentsMargins(0, 0, 0, 0)

        # تسمية الحالة
        status_label = QLabel("الحالة")
        status_label.setAlignment(Qt.AlignCenter)
        status_label.setFixedHeight(20)  # ارتفاع موحد مع باقي التسميات
        status_label.setStyleSheet("""
            QLabel {
                border: 2px solid #ced4da;
                border-radius: 0px;
                padding: 4px;
                font-size: 14px;
                font-weight: bold;
                background-color: #f8f9fa;
                color: #007bff;
            }
        """)
        status_layout.addWidget(status_label)

        # حاوية مربعي الاختيار
        radio_container = QWidget()
        radio_container.setFixedHeight(32)  # ارتفاع موحد مع باقي الحقول
        radio_layout = QHBoxLayout(radio_container)
        radio_layout.setSpacing(5)
        radio_layout.setContentsMargins(5, 0, 5, 0)

        # مربع الاختيار الأول: نشطة (أخضر)
        self.active_radio = QRadioButton("نشطة")
        self.active_radio.setChecked(True)  # محدد افتراضياً
        self.active_radio.setStyleSheet("""
            QRadioButton {
                font-size: 12px;
                font-weight: bold;
                color: #28a745;
                padding: 2px;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 14px;
                height: 14px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #28a745;
                border-radius: 7px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #28a745;
                border-radius: 7px;
                background-color: #28a745;
            }
        """)
        self.status_button_group.addButton(self.active_radio, 0)
        radio_layout.addWidget(self.active_radio)

        # مربع الاختيار الثاني: مكتملة (أحمر)
        self.completed_radio = QRadioButton("مكتملة")
        self.completed_radio.setStyleSheet("""
            QRadioButton {
                font-size: 12px;
                font-weight: bold;
                color: #dc3545;
                padding: 2px;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 14px;
                height: 14px;
            }
            QRadioButton::indicator:unchecked {
                border: 2px solid #dc3545;
                border-radius: 7px;
                background-color: white;
            }
            QRadioButton::indicator:checked {
                border: 2px solid #dc3545;
                border-radius: 7px;
                background-color: #dc3545;
            }
        """)
        self.status_button_group.addButton(self.completed_radio, 1)
        radio_layout.addWidget(self.completed_radio)

        # إضافة الحاوية إلى التخطيط العمودي
        status_layout.addWidget(radio_container)

        # إضافة التخطيط العمودي إلى التخطيط الأفقي الرئيسي
        horizontal_layout.addLayout(status_layout)

    def set_tooth_number(self, tooth_number):
        """تعيين رقم السن"""
        current_tooth = self.tooth_number_edit.text()
        if current_tooth and current_tooth != str(tooth_number):
            reply = QMessageBox.question(
                self,
                "تأكيد تغيير السن",
                f"هل تريد تغيير السن من {current_tooth} إلى {tooth_number}؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return False

        self.tooth_number_edit.setText(str(tooth_number))
        return True

    def set_treatment_text(self, treatment_options):
        """تعيين نص المعالجة"""
        if treatment_options:
            self.treatment_text.setPlainText(", ".join(treatment_options))

    def get_plan_data(self):
        """الحصول على بيانات الخطة - التعامل مع القيم الفارغة (بدون plan_number)"""
        # التعامل مع القيمة الفارغة للكلفة
        cost_value = self.cost_spinbox.value()
        cost = int(cost_value) if cost_value > 0 else 0

        # تحديد حالة خطة المعالجة
        status = "نشطة" if self.active_radio.isChecked() else "مكتملة"

        return {
            'tooth_number': self.tooth_number_edit.text(),
            'treatment': self.treatment_text.toPlainText(),
            'cost': cost,
            'date': self.date_edit.date().toString('yyyy-MM-dd'),
            'status': status
        }

    def clear_form(self):
        """مسح النموذج (بدون plan_number)"""
        self.tooth_number_edit.clear()
        self.treatment_text.clear()
        self.cost_spinbox.setValue(0)
        self.cost_spinbox.lineEdit().clear()  # Force empty display
        self.date_edit.setDate(QDate.currentDate())
        # إعادة تعيين حالة خطة المعالجة إلى "نشطة" (الافتراضي)
        self.active_radio.setChecked(True)

    def load_plan_data(self, plan_data):
        """تحميل بيانات خطة معالجة في النموذج (بدون plan_number)"""
        try:
            # تحميل رقم السن
            if 'tooth_number' in plan_data:
                self.tooth_number_edit.setText(str(plan_data['tooth_number']))

            # تحميل وصف المعالجة
            if 'treatment_description' in plan_data:
                self.treatment_text.setPlainText(str(plan_data['treatment_description']))

            # تحميل الكلفة
            if 'cost' in plan_data:
                cost = plan_data['cost']
                if cost is not None:
                    self.cost_spinbox.setValue(int(cost))
                else:
                    self.cost_spinbox.setValue(0)

            # تحميل التاريخ
            if 'plan_date' in plan_data and plan_data['plan_date']:
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(plan_data['plan_date'], '%Y-%m-%d')
                    self.date_edit.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
                except:
                    # في حالة فشل تحويل التاريخ، استخدم التاريخ الحالي
                    self.date_edit.setDate(QDate.currentDate())
            else:
                self.date_edit.setDate(QDate.currentDate())

            # تحميل حالة خطة المعالجة
            if 'status' in plan_data:
                status = plan_data['status']
                if status == "مكتملة":
                    self.completed_radio.setChecked(True)
                else:
                    self.active_radio.setChecked(True)  # الافتراضي "نشطة"
            else:
                self.active_radio.setChecked(True)  # الافتراضي "نشطة"

            print(f"تم تحميل بيانات الخطة: السن {plan_data.get('tooth_number', 'غير محدد')}, الحالة: {plan_data.get('status', 'نشطة')}")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الخطة: {e}")
            # في حالة الخطأ، امسح النموذج
            self.clear_form()

class TreatmentSessionDialog(QDialog):
    """نافذة جلسات المعالجة السنية المنبثقة"""

    def __init__(self, plan_id=None, cost=0.0, patient_id=None, session_data=None, parent=None):
        super().__init__(parent)
        self.plan_id = plan_id
        self.cost = cost
        self.patient_id = patient_id
        self.session_data = session_data
        self.is_edit_mode = session_data is not None
        self.parent_tab = parent  # مرجع لتبويبة علاج الأسنان
        self.tooth_number = None  # سيتم تحديده من خطة المعالجة

        # جلب بيانات خطة المعالجة للحصول على رقم السن
        self.load_plan_data()

        self.init_ui()

        # تحميل بيانات الجلسة في حالة التعديل
        if self.is_edit_mode and self.session_data:
            self.load_session_data()

        # ضمان العرض الصحيح للنافذة
        self.ensure_proper_display()

    def load_plan_data(self):
        """جلب بيانات خطة المعالجة من قاعدة البيانات"""
        if self.plan_id and self.parent_tab and hasattr(self.parent_tab, 'db_handler'):
            try:
                plan_data = self.parent_tab.db_handler.get_treatment_plan(self.plan_id)
                if plan_data:
                    self.tooth_number = plan_data.get('tooth_number', '')
                    print(f"تم جلب رقم السن من خطة المعالجة: {self.tooth_number}")
                else:
                    print("لم يتم العثور على بيانات خطة المعالجة")
                    self.tooth_number = ''
            except Exception as e:
                print(f"خطأ في جلب بيانات خطة المعالجة: {e}")
                self.tooth_number = ''
        else:
            self.tooth_number = ''

    def init_ui(self):
        title = "تعديل جلسة المعالجة" if self.is_edit_mode else "إضافة جلسة معالجة جديدة"
        self.setWindowTitle(title)
        self.setModal(True)

        # إعداد حجم النافذة المُحسَّن والمضغوط أكثر
        self.setMinimumSize(1000, 700)  # Increased height to prevent truncation
        self.resize(1000, 700)
        # إضافة أعلام النافذة لضمان العرض الصحيح
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setLayoutDirection(Qt.RightToLeft)

        # ضمان أن النافذة تظهر بالحجم الصحيح فوراً
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        # تطبيق تنسيق بسيط ونظيف للنافذة - نظام ألوان أزرق ورمادي فاتح
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f7fa;
                border-radius: 12px;
                border: 1px solid #d1d9e6;
            }
        """)

        # توسيط النافذة على الشاشة بعد تعيين الحجم
        self.center_on_screen()

        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(18)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # نموذج جلسة المعالجة المحسن - مع استغلال المساحة المحررة من العنوان
        self.create_session_form(main_layout)

        # أزرار التحكم المحسنة
        self.create_control_buttons(main_layout)



    def create_session_form(self, parent_layout):
        """إنشاء نموذج جلسة المعالجة المحسن"""
        # إطار رئيسي للنموذج بتصميم بسيط ونظيف
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 10px;
                padding: 8px;
            }
            QFrame:hover {
                border-color: #4a90e2;
            }
        """)
        # حجم مُحسَّن ومضغوط أكثر للنموذج مع الحفاظ على وضوح العناصر
        form_frame.setMinimumHeight(500)  # تقليل من 520 إلى 500
        form_frame.setMaximumHeight(500)
        form_frame.setMinimumWidth(940)
        form_frame.setMaximumWidth(940)
        # تعيين سياسة الحجم لضمان الاستقرار
        form_frame.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        main_form_layout = QVBoxLayout(form_frame)
        # مسافات مُحسَّنة ومضغوطة لتوفير المساحة
        main_form_layout.setSpacing(22)  # تقليل من 28 إلى 22
        main_form_layout.setContentsMargins(22, 25, 22, 25)  # تقليل الهوامش

        # قسم معلومات الخطة
        self.create_plan_info_section(main_form_layout)

        # تم إزالة الخط الفاصل لتحسين التخطيط وتوزيع المساحة

        # قسم بيانات الجلسة
        self.create_session_data_section(main_form_layout)

        parent_layout.addWidget(form_frame)

    def create_plan_info_section(self, parent_layout):
        """إنشاء قسم معلومات خطة المعالجة - بدون عنوان لاستغلال المساحة"""
        # تخطيط أفقي مُحسَّن ومضغوط
        plan_info_layout = QHBoxLayout()
        plan_info_layout.setSpacing(20)  # تقليل من 25 إلى 20
        plan_info_layout.setContentsMargins(0, 20, 0, 22)  # تقليل الهوامش

        # رقم السن - عرض مرن
        tooth_container = self.create_field_container("رقم السن", self.create_tooth_field())
        tooth_container.setMinimumWidth(200)
        tooth_container.setMaximumWidth(280)
        plan_info_layout.addWidget(tooth_container, 1)  # نسبة مرونة 1

        # التاريخ - عرض مرن
        date_container = self.create_field_container("تاريخ الجلسة", self.create_date_field())
        date_container.setMinimumWidth(250)
        date_container.setMaximumWidth(350)
        plan_info_layout.addWidget(date_container, 1)  # نسبة مرونة 1

        # مساحة صغيرة للتوازن
        plan_info_layout.addStretch(1)

        parent_layout.addLayout(plan_info_layout)

    def create_session_data_section(self, parent_layout):
        """إنشاء قسم بيانات الجلسة - بدون عنوان لاستغلال المساحة"""
        # تخطيط مُحسَّن ومضغوط للإجراء والدفعة
        session_content_layout = QVBoxLayout()
        session_content_layout.setSpacing(20)  # تقليل من 25 إلى 20
        session_content_layout.setContentsMargins(0, 20, 0, 20)  # تقليل الهوامش

        # الإجراء - ارتفاع مُحسَّن ومضغوط أكثر
        procedure_container = self.create_field_container("تفاصيل الإجراء", self.create_procedure_field())
        procedure_container.setMinimumHeight(120)  # تقليل من 140 إلى 120
        procedure_container.setMaximumHeight(130)  # تقليل من 150 إلى 130
        session_content_layout.addWidget(procedure_container)

        # تخطيط أفقي مُحسَّن ومضغوط للدفعة
        payment_layout = QHBoxLayout()
        payment_layout.setSpacing(18)  # تقليل من 20 إلى 18
        payment_layout.setContentsMargins(0, 12, 0, 12)  # تقليل من 15 إلى 12

        # الدفعة - عرض مرن
        payment_container = self.create_field_container("مبلغ الدفعة", self.create_payment_field())
        payment_container.setMinimumWidth(250)
        payment_container.setMaximumWidth(400)
        payment_layout.addWidget(payment_container, 1)

        # مساحة للتوازن
        payment_layout.addStretch(1)

        session_content_layout.addLayout(payment_layout)
        parent_layout.addLayout(session_content_layout)

    def create_field_container(self, label_text, field_widget):
        """إنشاء حاوية محسنة للحقل مع التسمية"""
        container = QFrame()
        container.setStyleSheet("""
            QFrame {
                background-color: #f8fafb;
                border: 1px solid #d1d9e6;
                border-radius: 8px;
                padding: 5px;
            }
            QFrame:hover {
                border-color: #4a90e2;
                background-color: #f0f4f8;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setSpacing(6)  # تقليل المسافة من 8 إلى 6
        layout.setContentsMargins(14, 10, 14, 10)  # تقليل الهوامش لتوفير المساحة

        # تسمية الحقل محسنة ومضغوطة لعرض النص العربي بوضوح
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #495057;
                background: transparent;
                border: none;
                padding: 2px 6px;
                margin: 0px;
                text-align: right;
                qproperty-alignment: AlignRight;
                min-height: 28px; /* Increased min-height to prevent truncation */
            }
        """)
        label.setFixedHeight(28)  # Adjusted height to prevent truncation
        label.setWordWrap(True)  # Allow text wrapping for Arabic text
        label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)  # Right alignment for Arabic text
        layout.addWidget(label)
        layout.addWidget(field_widget)

        return container

    def create_tooth_field(self):
        """إنشاء حقل رقم السن المحسن"""
        self.session_tooth_edit = QLineEdit()
        self.session_tooth_edit.setReadOnly(True)
        self.session_tooth_edit.setText(str(self.tooth_number) if self.tooth_number else "غير محدد")
        # تطبيق التنسيق من اليمين إلى اليسار للنص العربي
        self.session_tooth_edit.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.session_tooth_edit.setLayoutDirection(Qt.RightToLeft)
        self.session_tooth_edit.setStyleSheet("""
            QLineEdit {
                background-color: #f8fafb;
                border: 1px solid #c5d2e0;
                border-radius: 6px;
                padding: 8px;
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                text-align: right;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
                background-color: #ffffff;
            }
        """)
        # ارتفاع مُحسَّن ومضغوط لتوفير المساحة
        self.session_tooth_edit.setFixedHeight(45)  # Increased height to prevent truncation
        return self.session_tooth_edit

    def create_date_field(self):
        """إنشاء حقل التاريخ المحسن"""
        self.session_date_edit = QDateEdit()
        self.session_date_edit.setDate(QDate.currentDate())
        self.session_date_edit.setCalendarPopup(True)
        # تطبيق التنسيق من اليمين إلى اليسار للنص العربي
        self.session_date_edit.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.session_date_edit.setLayoutDirection(Qt.RightToLeft)
        self.session_date_edit.setStyleSheet("""
            QDateEdit {
                background-color: #ffffff;
                border: 1px solid #c5d2e0;
                border-radius: 6px;
                padding: 7px;
                font-size: 13px;
                font-weight: 500;
                color: #2c3e50;
                text-align: right;
            }
            QDateEdit:focus {
                border-color: #4a90e2;
                background-color: #f8fafb;
            }
            QDateEdit:hover {
                border-color: #6ba3f5;
            }
            QDateEdit::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top left;
                width: 25px;
                border-right: 1px solid #c5d2e0;
                border-radius: 6px 0px 0px 6px;
                background-color: #4a90e2;
            }
            QDateEdit::drop-down:hover {
                background-color: #357abd;
            }
        """)
        self.session_date_edit.setFixedHeight(45)  # Increased height to prevent truncation
        return self.session_date_edit

    def create_procedure_field(self):
        """إنشاء حقل الإجراء المحسن"""
        self.procedure_text = QTextEdit()
        # ارتفاع مُحسَّن ومضغوط أكثر لتوفير المساحة
        self.procedure_text.setFixedHeight(100)  # Increased height to prevent truncation
        self.procedure_text.setPlaceholderText("✍️ اكتب تفاصيل الإجراء المنفذ في هذه الجلسة...")
        # تطبيق التنسيق من اليمين إلى اليسار للنص العربي
        self.procedure_text.setLayoutDirection(Qt.RightToLeft)
        # تعيين محاذاة النص إلى اليمين
        cursor = self.procedure_text.textCursor()
        format = cursor.blockFormat()
        format.setAlignment(Qt.AlignRight)
        cursor.setBlockFormat(format)
        self.procedure_text.setTextCursor(cursor)
        self.procedure_text.setStyleSheet("""
            QTextEdit {
                background-color: #ffffff;
                border: 1px solid #c5d2e0;
                border-radius: 6px;
                padding: 10px;
                font-size: 13px;
                color: #2c3e50;
                line-height: 1.5;
                selection-background-color: #4a90e2;
                selection-color: white;
                text-align: right;
            }
            QTextEdit:focus {
                border-color: #4a90e2;
                background-color: #f8fafb;
            }
            QTextEdit:hover {
                border-color: #6ba3f5;
            }
        """)
        return self.procedure_text

    def create_payment_field(self):
        """إنشاء حقل الدفعة المحسن"""
        self.payment_spinbox = QSpinBox()
        self.payment_spinbox.setMinimum(0)
        self.payment_spinbox.setMaximum(999999999)
        # تطبيق التنسيق من اليمين إلى اليسار للأرقام العربية
        self.payment_spinbox.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.payment_spinbox.setLayoutDirection(Qt.RightToLeft)
        self.payment_spinbox.setStyleSheet("""
            QSpinBox {
                background-color: #ffffff;
                border: 1px solid #c5d2e0;
                border-radius: 6px;
                padding: 8px;
                font-size: 15px;
                font-weight: bold;
                color: #2c3e50;
                selection-background-color: #4a90e2;
                selection-color: white;
                text-align: right;
            }
            QSpinBox:focus {
                border-color: #4a90e2;
                background-color: #f8fafb;
            }
            QSpinBox:hover {
                border-color: #6ba3f5;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #4a90e2;
                border: none;
                border-radius: 3px;
                width: 20px;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #357abd;
            }
        """)
        # إظهار حقل فارغ بدلاً من الصفر
        self.payment_spinbox.setSpecialValueText("")
        self.payment_spinbox.setValue(0)
        self.payment_spinbox.lineEdit().clear()
        self.payment_spinbox.setFixedHeight(45)  # Increased height to prevent truncation
        return self.payment_spinbox

    def create_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم المحسنة"""
        # إطار للأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                border-radius: 0px 0px 12px 12px;
                padding: 3px;
            }
        """)
        # حجم مُحسَّن ومضغوط لمنطقة الأزرار
        buttons_frame.setFixedHeight(70)  # تقليل من 80 إلى 70
        buttons_frame.setFixedWidth(940)
        buttons_frame.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار
        buttons_layout.setContentsMargins(20, 12, 20, 12)  # تقليل الهوامش

        # ترتيب الأزرار من اليمين إلى اليسار للتخطيط العربي
        # زر الحفظ أولاً (الأهم) في أقصى اليمين
        save_text = "💾 حفظ التعديلات" if self.is_edit_mode else "💾 حفظ الجلسة"
        save_btn = QPushButton(save_text)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a90e2;
                color: white;
                border: 1px solid #357abd;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 140px;
            }
            QPushButton:hover {
                background-color: #357abd;
                border-color: #2968a3;
            }
            QPushButton:pressed {
                background-color: #2968a3;
            }
        """)
        save_btn.clicked.connect(self.save_session)
        save_btn.setDefault(True)  # جعله الزر الافتراضي
        buttons_layout.addWidget(save_btn)

        # زر الإلغاء في الوسط
        cancel_btn = QPushButton("✖ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: 1px solid #c82333;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 6px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #c82333;
                border-color: #bd2130;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)

        # إضافة زر الحذف فقط في وضع التحرير (في أقصى اليسار)
        if self.is_edit_mode:
            delete_btn = QPushButton("🗑️ حذف الجلسة")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: 1px solid #c82333;
                    padding: 10px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    border-radius: 6px;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #c82333;
                    border-color: #bd2130;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)
            delete_btn.clicked.connect(self.delete_session)
            buttons_layout.addWidget(delete_btn)

        # إضافة مساحة مرنة في النهاية لدفع الأزرار إلى اليمين
        buttons_layout.addStretch()

        parent_layout.addWidget(buttons_frame)





    def save_session(self):
        """حفظ أو تحديث جلسة المعالجة"""
        if not self.session_tooth_edit.text():
            QMessageBox.warning(self, "تحذير", "يرجى تحديد رقم السن")
            return

        if not self.procedure_text.toPlainText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال تفاصيل الإجراء")
            return

        try:
            # الحصول على بيانات الجلسة
            session_data = self.get_session_data()

            # حفظ أو تحديث البيانات في قاعدة البيانات
            if hasattr(self.parent_tab, 'db_handler'):
                if self.is_edit_mode and self.session_data:
                    # تحديث جلسة موجودة
                    session_id = self.session_data.get('id')
                    success = self.parent_tab.db_handler.update_treatment_session(
                        session_id,
                        session_date=session_data['date'],
                        tooth_number=session_data['tooth_number'],
                        procedure_description=session_data['procedure'],
                        payment=session_data['payment']
                    )

                    if success:
                        QMessageBox.information(self, "نجح", "تم تحديث جلسة المعالجة بنجاح")
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في تحديث جلسة المعالجة")
                        return
                else:
                    # إضافة جلسة جديدة
                    session_id = self.parent_tab.db_handler.add_treatment_session(
                        treatment_plan_id=session_data['plan_id'],
                        session_date=session_data['date'],
                        tooth_number=session_data['tooth_number'],
                        procedure_description=session_data['procedure'],
                        payment=session_data['payment']
                    )

                    if session_id:
                        QMessageBox.information(self, "نجح", f"تم حفظ جلسة المعالجة بنجاح\nمعرف الجلسة: {session_id}")
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في حفظ جلسة المعالجة")
                        return
            else:
                QMessageBox.information(self, "نجح", "تم حفظ جلسة المعالجة بنجاح")

            self.accept()

        except Exception as e:
            print(f"خطأ في حفظ جلسة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الجلسة: {str(e)}")

    def delete_session(self):
        """حذف جلسة المعالجة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه الجلسة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # هنا يتم حذف البيانات من قاعدة البيانات
            QMessageBox.information(self, "تم الحذف", "تم حذف الجلسة بنجاح")
            self.accept()

    def get_session_data(self):
        """الحصول على بيانات الجلسة - التعامل مع القيم الفارغة"""
        # التعامل مع القيمة الفارغة للدفعة
        payment_value = self.payment_spinbox.value()
        payment = int(payment_value) if payment_value > 0 else 0

        return {
            'plan_id': self.plan_id,  # معرف خطة المعالجة
            'patient_id': self.patient_id,  # معرف المريض
            'date': self.session_date_edit.date().toString('yyyy-MM-dd'),
            'tooth_number': self.tooth_number,  # رقم السن من خطة المعالجة
            'procedure': self.procedure_text.toPlainText(),
            'cost': int(self.cost),
            'payment': payment,
            'remaining': int(self.cost) - payment
        }

    def center_on_screen(self):
        """توسيط النافذة على الشاشة مع ضمان العرض الصحيح"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget, QApplication
            # الحصول على معلومات الشاشة
            screen = QDesktopWidget().screenGeometry()
            # استخدام الحجم المُحسَّن والمضغوط أكثر للنافذة
            dialog_width = 1000
            dialog_height = 640  # الحجم الجديد المضغوط أكثر

            # حساب الموقع المركزي
            x = (screen.width() - dialog_width) // 2
            y = (screen.height() - dialog_height) // 2

            # تعيين الموقع والحجم معاً
            self.setGeometry(x, y, dialog_width, dialog_height)

            # التأكد من أن النافذة مرئية وفي المقدمة
            self.show()
            self.raise_()
            self.activateWindow()

        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")
            # في حالة الخطأ، استخدم الموقع الافتراضي
            self.setGeometry(100, 100, 1000, 640)

    def ensure_proper_display(self):
        """ضمان العرض الصحيح للنافذة من البداية"""
        try:
            # التأكد من أن النافذة بالحجم الصحيح المُحسَّن والمضغوط أكثر
            self.resize(1000, 640)

            # إجبار النافذة على إعادة حساب التخطيط
            self.adjustSize()

            # التأكد من أن جميع العناصر مرئية
            self.update()
            self.repaint()

            # تطبيق التخطيط على جميع العناصر الفرعية
            for widget in self.findChildren(QWidget):
                widget.update()

        except Exception as e:
            print(f"خطأ في ضمان العرض الصحيح: {e}")

    def load_session_data(self):
        """تحميل بيانات الجلسة في حالة التعديل"""
        if self.session_data:
            # تحميل التاريخ
            session_date = self.session_data.get('session_date', '')
            if session_date:
                try:
                    from datetime import datetime
                    date_obj = datetime.strptime(session_date, '%Y-%m-%d')
                    self.session_date_edit.setDate(QDate(date_obj.year, date_obj.month, date_obj.day))
                except:
                    pass

            # تحميل وصف الإجراء
            procedure = self.session_data.get('procedure_description', '')
            self.procedure_text.setPlainText(procedure)

            # تحميل الدفعة
            payment = self.session_data.get('payment', 0)
            self.payment_spinbox.setValue(payment)

class TreatmentPlanDialog(QDialog):
    """نافذة إضافة/تعديل خطة المعالجة السنية"""

    def __init__(self, db_handler, patient_id=None, plan_id=None, plan_data=None, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.patient_id = patient_id
        self.plan_id = plan_id
        self.plan_data = plan_data
        self.is_edit_mode = plan_id is not None

        self.init_ui()

        # تحميل البيانات في حالة التعديل
        if self.is_edit_mode and self.plan_data:
            self.load_plan_data()

    def init_ui(self):
        """إنشاء واجهة النافذة المنبثقة المحسنة"""
        # تحديث العنوان ليتطابق مع المتطلبات
        self.setWindowTitle("خطة المعالجة السنية")
        self.setModal(True)
        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد النافذة بحجم الشاشة كاملة مع أزرار التحكم
        self.setWindowFlags(Qt.Dialog | Qt.WindowMaximizeButtonHint |
                           Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)
        self.setWindowState(Qt.WindowMaximized)
        self.setMinimumSize(1000, 700)

        # تطبيق التنسيق العام المحسن
        self.apply_enhanced_styling()

        # إنشاء التخطيط العمودي الجديد
        self.create_vertical_layout()

    def apply_enhanced_styling(self):
        """تطبيق التنسيق المحسن للواجهة المبسطة"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                color: #212529;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            }
            QLabel {
                color: #495057;
                font-size: 12px;
                min-height: 22px;
            }
            QLineEdit, QTextEdit, QSpinBox, QDateEdit {
                border: 2px solid #ced4da;
                border-radius: 0px;
                padding: 6px;
                font-size: 12px;
                background-color: white;
                min-height: 22px;
                max-height: 38px;
            }
            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
                box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
            }
            QTextEdit {
                min-height: 55px;
                max-height: 90px;
            }
        """)

    def create_vertical_layout(self):
        """إنشاء التخطيط العمودي المحسن للنافذة مع إطارات وتلوين احترافي"""
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(12)  # مسافة متوازنة بين الأقسام (12px)
        main_layout.setContentsMargins(20, 20, 20, 20)  # هوامش مناسبة

        # 1. مخطط الأسنان (في الأعلى) - مع إطار بدون عنوان
        teeth_chart_frame = self.create_section_frame()
        teeth_chart_layout = QVBoxLayout(teeth_chart_frame)
        teeth_chart_layout.setContentsMargins(10, 10, 10, 10)
        self.create_teeth_chart_section(teeth_chart_layout)
        main_layout.addWidget(teeth_chart_frame)

        # 2. خيارات المعالجة (في الوسط) - مع إطار
        treatment_options_frame = self.create_section_frame()
        treatment_options_frame_layout = QVBoxLayout(treatment_options_frame)
        treatment_options_frame_layout.setContentsMargins(10, 10, 10, 10)

        # حاوية خيارات المعالجة مع ارتفاع محسن لعرض 13 خيار لكل مجموعة
        treatment_options_container = QWidget()
        treatment_options_container.setMinimumHeight(480)  # ارتفاع محسن لعرض 13 خيار لكل مجموعة
        treatment_options_container.setMaximumHeight(490)  # حد أقصى معدل لضمان التحكم في الارتفاع
        treatment_options_layout = QVBoxLayout(treatment_options_container)
        treatment_options_layout.setContentsMargins(0, 0, 0, 0)
        self.create_treatment_options_section(treatment_options_layout)
        treatment_options_frame_layout.addWidget(treatment_options_container)
        main_layout.addWidget(treatment_options_frame)

        # 3. بيانات خطة المعالجة السنية (في الأسفل) - مع إطار
        treatment_plan_frame = self.create_section_frame()
        treatment_plan_frame_layout = QVBoxLayout(treatment_plan_frame)
        treatment_plan_frame_layout.setContentsMargins(10, 10, 10, 10)

        # حاوية خطة المعالجة
        treatment_plan_container = QWidget()
        treatment_plan_container.setMinimumHeight(180)  # ارتفاع محسن لضمان ظهور جميع الحقول والتسميات بوضوح
        treatment_plan_container.setMaximumHeight(190)  # حد أقصى معدل لتحقيق توازن أفضل
        treatment_plan_layout = QVBoxLayout(treatment_plan_container)
        treatment_plan_layout.setContentsMargins(0, 0, 0, 0)
        self.create_treatment_plan_section(treatment_plan_layout)
        treatment_plan_frame_layout.addWidget(treatment_plan_container)
        main_layout.addWidget(treatment_plan_frame)

        # أزرار التحكم المحسنة
        self.create_enhanced_control_buttons(main_layout)

    def create_section_frame(self):
        """إنشاء إطار احترافي للأقسام"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        frame.setLineWidth(1)
        frame.setStyleSheet("""
            QFrame {
                border: 1px solid #d0d0d0;
                border-radius: 8px;
                background-color: white;
                margin: 2px;
            }
        """)
        return frame

    def create_teeth_chart_section(self, parent_layout):
        """إنشاء قسم مخطط الأسنان"""
        self.teeth_chart = CompactTeethChart()
        self.teeth_chart.tooth_selected.connect(self.on_tooth_selected)
        parent_layout.addWidget(self.teeth_chart)

    def create_treatment_options_section(self, parent_layout):
        """إنشاء قسم خيارات المعالجة"""
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_treatment_options_changed)
        parent_layout.addWidget(self.treatment_options)

    def create_treatment_plan_section(self, parent_layout):
        """إنشاء قسم بيانات خطة المعالجة السنية"""
        self.treatment_plan = TreatmentPlanWidget()
        parent_layout.addWidget(self.treatment_plan)

    def create_enhanced_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم المحسنة (بدون زر الإغلاق)"""
        # حاوية الأزرار مع مسافات مقللة
        buttons_container = QWidget()
        buttons_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border-top: 2px solid #007bff;
                padding: 15px;
                margin-top: 10px;
                border-radius: 0 0 10px 10px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setSpacing(15)  # تقليل المسافة بين الأزرار

        # زر حفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(self.get_enhanced_button_style("#28a745", "#218838"))
        save_btn.clicked.connect(self.save_plan)
        save_btn.setMinimumSize(120, 45)  # تقليل حجم الأزرار قليلاً
        save_btn.setToolTip("حفظ خطة المعالجة في قاعدة البيانات")
        buttons_layout.addWidget(save_btn)

        # زر إلغاء (تفريغ الحقول) - لون أحمر لاتباع معايير واجهة المستخدم
        cancel_btn = QPushButton("🔄 إلغاء")
        cancel_btn.setStyleSheet(self.get_enhanced_button_style("#dc3545", "#c82333"))
        cancel_btn.clicked.connect(self.clear_form)
        cancel_btn.setMinimumSize(120, 45)
        cancel_btn.setToolTip("تفريغ جميع الحقول وإعادة تهيئتها")
        buttons_layout.addWidget(cancel_btn)

        # زر تعديل أسعار علاج الأسنان
        prices_btn = QPushButton("💰 تعديل أسعار علاج الأسنان")
        prices_btn.setStyleSheet(self.get_enhanced_button_style("#17a2b8", "#138496"))
        prices_btn.clicked.connect(self.edit_dental_prices)
        prices_btn.setMinimumSize(200, 45)
        prices_btn.setToolTip("فتح نافذة تعديل أسعار وخيارات المعالجة")
        buttons_layout.addWidget(prices_btn)

        buttons_layout.addStretch()
        parent_layout.addWidget(buttons_container)





    def get_enhanced_button_style(self, bg_color, hover_color):
        """الحصول على تنسيق الأزرار المحسن"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                transform: translateY(0px);
            }}
        """

    def clear_form(self):
        """تفريغ جميع الحقول وإعادة تهيئتها"""
        try:
            # مسح تحديد السن في المخطط
            if hasattr(self.teeth_chart, 'clear_selection'):
                self.teeth_chart.clear_selection()

            # مسح خيارات المعالجة
            if hasattr(self.treatment_options, 'clear_selections'):
                self.treatment_options.clear_selections()

            # مسح نموذج خطة المعالجة
            if hasattr(self.treatment_plan, 'clear_form'):
                self.treatment_plan.clear_form()

            print("تم تفريغ جميع الحقول بنجاح")

        except Exception as e:
            print(f"خطأ في تفريغ الحقول: {e}")
            QMessageBox.warning(self, "تحذير", f"حدث خطأ أثناء تفريغ الحقول:\n{str(e)}")

    def get_button_style(self, bg_color, hover_color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            dialog_size = self.geometry()
            x = (screen.width() - dialog_size.width()) // 2
            y = (screen.height() - dialog_size.height()) // 2
            self.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")

    def on_tooth_selected(self, tooth_number):
        """عند تحديد سن من المخطط - محسن مع معالجة الأخطاء"""
        try:
            print(f"تم تحديد السن: {tooth_number}")

            if hasattr(self.treatment_plan, 'set_tooth_number'):
                success = self.treatment_plan.set_tooth_number(tooth_number)
                if success:
                    print(f"تم تعيين رقم السن بنجاح: {tooth_number}")
                    # تحديث نص المعالجة بناءً على الخيارات المحددة
                    self.update_treatment_text()

                    # إظهار رسالة تأكيد للمستخدم
                    self.show_status_message(f"تم تحديد السن رقم {tooth_number}")
                else:
                    print(f"فشل في تعيين رقم السن: {tooth_number}")
            else:
                print("تحذير: دالة set_tooth_number غير متوفرة في TreatmentPlanWidget")

        except Exception as e:
            print(f"خطأ في معالجة تحديد السن: {e}")
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحديد السن:\n{str(e)}")

    def on_treatment_options_changed(self):
        """عند تغيير خيارات المعالجة - محسن مع معالجة الأخطاء"""
        try:
            print("تم تغيير خيارات المعالجة")

            # الحصول على الخيارات المحددة من widget
            if hasattr(self.treatment_options, 'get_selected_options'):
                selected_options = self.treatment_options.get_selected_options()
                print(f"الخيارات المحددة: {selected_options}")

                if hasattr(self.treatment_plan, 'set_treatment_text'):
                    self.treatment_plan.set_treatment_text(selected_options)
                    print("تم تحديث نص المعالجة بناءً على الخيارات")

                    # إظهار رسالة تأكيد للمستخدم
                    if selected_options:
                        self.show_status_message("تم تحديث خيارات المعالجة")
                else:
                    print("تحذير: دالة set_treatment_text غير متوفرة في TreatmentPlanWidget")
            else:
                print("تحذير: دالة get_selected_options غير متوفرة في TreatmentOptionsWidget")

        except Exception as e:
            print(f"خطأ في معالجة تغيير خيارات المعالجة: {e}")

    def update_treatment_text(self):
        """تحديث نص المعالجة بناءً على الخيارات المحددة - محسن"""
        try:
            if hasattr(self.treatment_options, 'get_selected_options'):
                selected_options = self.treatment_options.get_selected_options()
                print(f"الخيارات المحددة: {selected_options}")

                if hasattr(self.treatment_plan, 'set_treatment_text'):
                    self.treatment_plan.set_treatment_text(selected_options)
                    print("تم تحديث نص المعالجة")
            else:
                print("تحذير: دالة get_selected_options غير متوفرة في TreatmentOptionsWidget")

        except Exception as e:
            print(f"خطأ في تحديث نص المعالجة: {e}")

    def show_status_message(self, message):
        """إظهار رسالة حالة مؤقتة للمستخدم"""
        try:
            # يمكن إضافة status bar أو tooltip هنا
            print(f"رسالة الحالة: {message}")
        except Exception as e:
            print(f"خطأ في إظهار رسالة الحالة: {e}")

    def load_plan_data(self):
        """تحميل بيانات الخطة في حالة التعديل - محسن مع معالجة الأخطاء"""
        if not self.plan_data:
            print("لا توجد بيانات خطة للتحميل")
            return

        try:
            print(f"تحميل بيانات الخطة: {self.plan_data}")

            # تحديد السن في المخطط
            tooth_number = self.plan_data.get('tooth_number')
            if tooth_number:
                try:
                    tooth_num = int(tooth_number)
                    print(f"تحديد السن في المخطط: {tooth_num}")

                    # تحديد السن في المخطط
                    if hasattr(self.teeth_chart, 'select_tooth'):
                        self.teeth_chart.select_tooth(tooth_num)
                        print(f"تم تحديد السن {tooth_num} في المخطط")

                    # تعيين رقم السن في النموذج
                    if hasattr(self.treatment_plan, 'set_tooth_number'):
                        self.treatment_plan.set_tooth_number(tooth_num)
                        print(f"تم تعيين رقم السن {tooth_num} في النموذج")

                except ValueError as e:
                    print(f"خطأ في تحويل رقم السن: {tooth_number} - {e}")
                except Exception as e:
                    print(f"خطأ في تحديد السن: {e}")

            # تحميل بيانات الخطة في النموذج
            if hasattr(self.treatment_plan, 'load_plan_data'):
                self.treatment_plan.load_plan_data(self.plan_data)
                print("تم تحميل بيانات الخطة في النموذج")
            else:
                print("تحذير: دالة load_plan_data غير متوفرة في TreatmentPlanWidget")

            # إظهار رسالة نجاح
            self.show_status_message("تم تحميل بيانات الخطة بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل بيانات الخطة: {e}")
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الخطة:\n{str(e)}")

    def save_plan(self):
        """حفظ خطة المعالجة - محسن مع التحقق الشامل"""
        try:
            print("بدء عملية حفظ خطة المعالجة...")

            # التحقق من وجود معرف المريض
            if not self.patient_id:
                QMessageBox.warning(self, "خطأ", "معرف المريض غير متوفر")
                return

            # الحصول على بيانات الخطة
            if not hasattr(self.treatment_plan, 'get_plan_data'):
                QMessageBox.critical(self, "خطأ", "دالة get_plan_data غير متوفرة في TreatmentPlanWidget")
                return

            plan_data = self.treatment_plan.get_plan_data()
            print(f"بيانات الخطة المستخرجة: {plan_data}")

            # التحقق من صحة البيانات
            validation_errors = []

            if not plan_data.get('tooth_number'):
                validation_errors.append("يرجى تحديد رقم السن من المخطط")

            if not plan_data.get('treatment', '').strip():
                validation_errors.append("يرجى إدخال وصف المعالجة أو اختيار خيارات المعالجة")

            if plan_data.get('cost', 0) < 0:
                validation_errors.append("الكلفة لا يمكن أن تكون سالبة")

            if validation_errors:
                error_message = "\n".join([f"• {error}" for error in validation_errors])
                QMessageBox.warning(
                    self,
                    "تحذير - يرجى تصحيح الأخطاء التالية",
                    f"{error_message}\n\nتأكد من:\n• النقر على السن في المخطط\n• اختيار خيارات المعالجة أو كتابة وصف المعالجة"
                )
                return

            # إعداد البيانات للحفظ
            save_data = {
                'patient_id': self.patient_id,
                'tooth_number': str(plan_data['tooth_number']),
                'treatment': plan_data['treatment'].strip(),  # تصحيح اسم الحقل
                'cost': int(plan_data.get('cost', 0)),
                'date': plan_data.get('date', QDate.currentDate().toString('yyyy-MM-dd')),  # تصحيح اسم الحقل
                'status': plan_data.get('status', 'نشطة')  # استخدام الحالة من النموذج
            }

            print(f"البيانات المعدة للحفظ: {save_data}")

            # حفظ أو تحديث البيانات
            if self.is_edit_mode:
                print(f"تحديث خطة موجودة - معرف: {self.plan_id}")
                success = self.db_handler.update_treatment_plan_new(
                    plan_id=self.plan_id,
                    patient_id=save_data['patient_id'],
                    tooth_number=save_data['tooth_number'],
                    treatment=save_data['treatment'],
                    cost=save_data['cost'],
                    date=save_data['date'],
                    status=save_data['status']
                )
                operation = "تحديث"
            else:
                print("إضافة خطة جديدة")
                success = self.db_handler.save_treatment_plan(save_data)
                operation = "حفظ"

            if success:
                QMessageBox.information(
                    self,
                    "✅ تم بنجاح",
                    f"تم {operation} خطة المعالجة بنجاح\n\n"
                    f"🦷 رقم السن: {save_data['tooth_number']}\n"
                    f"⚕️ المعالجة: {save_data['treatment']}\n"
                    f"💰 الكلفة: {save_data['cost']:,}\n"
                    f"📅 التاريخ: {save_data['date']}"
                )
                self.accept()
            else:
                QMessageBox.critical(self, "❌ خطأ", f"فشل في {operation} خطة المعالجة\n\nيرجى المحاولة مرة أخرى")

        except Exception as e:
            print(f"خطأ في حفظ خطة المعالجة: {e}")
            QMessageBox.critical(
                self,
                "❌ خطأ في النظام",
                f"حدث خطأ أثناء حفظ خطة المعالجة:\n\n{str(e)}\n\n"
                f"يرجى المحاولة مرة أخرى أو الاتصال بالدعم التقني."
            )

    def edit_dental_prices(self):
        """فتح نافذة تعديل أسعار وخيارات علاج الأسنان"""
        try:
            # إنشاء النافذة الجديدة لتعديل الأسعار والخيارات
            dialog = ComprehensiveTreatmentPricingDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث خيارات المعالجة إذا تم تعديلها
                if hasattr(self.treatment_options, 'refresh_options'):
                    self.treatment_options.refresh_options()
                    print("تم تحديث خيارات المعالجة")

        except Exception as e:
            print(f"خطأ في فتح نافذة الأسعار: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة تعديل الأسعار:\n{str(e)}")





class DentalTreatmentsTab(QWidget):
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.main_window = parent  # مرجع للنافذة الرئيسية للحصول على المريض المختار
        self.current_plan_id = None  # معرف خطة المعالجة الحالية المحددة
        self.current_session_id = None  # معرف جلسة المعالجة المحددة
        self.selected_plan_data = None  # بيانات خطة المعالجة المحددة
        self.patient_id = None # Initialize patient_id
        self.init_ui()

    def init_ui(self):
        """إنشاء الواجهة الجديدة المقسمة إلى حاويتين"""
        self.setLayoutDirection(Qt.RightToLeft)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # إنشاء QSplitter لتقسيم الواجهة إلى حاويتين متساويتين
        splitter = QSplitter(Qt.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #dee2e6;
                width: 3px;
            }
            QSplitter::handle:hover {
                background-color: #007bff;
            }
        """)

        # الحاوية اليمنى - خطط المعالجة السنية
        self.create_treatment_plans_container(splitter)

        # الحاوية اليسرى - جلسات المعالجة السنية
        self.create_treatment_sessions_container(splitter)

        # تعيين الأحجام المتساوية للحاويتين
        splitter.setSizes([400, 400])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)

        main_layout.addWidget(splitter)

        # تحميل البيانات الأولية عند إنشاء التبويبة
        self.load_treatment_plans_data()

    def create_treatment_plans_container(self, parent_splitter):
        """إنشاء حاوية خطط المعالجة السنية (الحاوية اليمنى)"""
        container = QWidget()
        container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان الحاوية
        title_label = QLabel("خطط المعالجة السنية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول خطط المعالجة
        self.treatment_plans_table = QTableWidget()
        self.setup_treatment_plans_table()
        layout.addWidget(self.treatment_plans_table)

        # أزرار التحكم في خطط المعالجة
        self.create_treatment_plans_buttons(layout)

        parent_splitter.addWidget(container)

    def create_treatment_sessions_container(self, parent_splitter):
        """إنشاء حاوية جلسات المعالجة السنية (الحاوية اليسرى)"""
        container = QWidget()
        container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(container)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان الحاوية
        title_label = QLabel("جلسات المعالجة السنية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #28a745;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # جدول جلسات المعالجة
        self.treatment_sessions_table = QTableWidget()
        self.setup_treatment_sessions_table()
        layout.addWidget(self.treatment_sessions_table)

        # أزرار التحكم في جلسات المعالجة
        self.create_treatment_sessions_buttons(layout)

        parent_splitter.addWidget(container)

    def setup_treatment_plans_table(self):
        """إعداد جدول خطط المعالجة"""
        self.treatment_plans_table.setColumnCount(5)
        self.treatment_plans_table.setHorizontalHeaderLabels([
            "رقم السن", "المعالجة", "الكلفة", "التاريخ", "الحالة"
        ])

        # تنسيق الجدول
        self.treatment_plans_table.horizontalHeader().setStretchLastSection(True)
        self.treatment_plans_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.treatment_plans_table.setSelectionMode(QTableWidget.SingleSelection)
        self.treatment_plans_table.setAlternatingRowColors(True)

        # ربط إشارة التحديد
        self.treatment_plans_table.selectionModel().selectionChanged.connect(
            self.on_treatment_plan_selected
        )

        # تنسيق CSS
        self.treatment_plans_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def setup_treatment_sessions_table(self):
        """إعداد جدول جلسات المعالجة"""
        self.treatment_sessions_table.setColumnCount(7)
        self.treatment_sessions_table.setHorizontalHeaderLabels([
            "التاريخ", "رقم السن", "الإجراء", "الكلفة", "الدفعة", "مجموع الدفعات", "المتبقي"
        ])

        # تنسيق الجدول
        self.treatment_sessions_table.horizontalHeader().setStretchLastSection(True)
        self.treatment_sessions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.treatment_sessions_table.setSelectionMode(QTableWidget.SingleSelection)
        self.treatment_sessions_table.setAlternatingRowColors(True)

        # ربط إشارة التحديد
        self.treatment_sessions_table.selectionModel().selectionChanged.connect(
            self.on_treatment_session_selected
        )

        # تنسيق CSS
        self.treatment_sessions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #28a745;
                selection-color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

    def create_treatment_plans_buttons(self, parent_layout):
        """إنشاء أزرار التحكم في خطط المعالجة"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر إضافة خطة
        add_plan_btn = QPushButton("إضافة خطة")
        add_plan_btn.setStyleSheet(self.get_button_style("#007bff", "#0056b3"))
        add_plan_btn.clicked.connect(self.add_treatment_plan)
        buttons_layout.addWidget(add_plan_btn)

        # زر تعديل خطة
        edit_plan_btn = QPushButton("تعديل خطة")
        edit_plan_btn.setStyleSheet(self.get_button_style("#ffc107", "#e0a800"))
        edit_plan_btn.clicked.connect(self.edit_treatment_plan)
        buttons_layout.addWidget(edit_plan_btn)

        # زر حذف خطة
        delete_plan_btn = QPushButton("حذف خطة")
        delete_plan_btn.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        delete_plan_btn.clicked.connect(self.delete_treatment_plan)
        buttons_layout.addWidget(delete_plan_btn)

        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)

    def create_treatment_sessions_buttons(self, parent_layout):
        """إنشاء أزرار التحكم في جلسات المعالجة"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر إضافة جلسة
        add_session_btn = QPushButton("إضافة جلسة")
        add_session_btn.setStyleSheet(self.get_button_style("#28a745", "#218838"))
        add_session_btn.clicked.connect(self.add_treatment_session)
        buttons_layout.addWidget(add_session_btn)

        # زر تعديل جلسة
        edit_session_btn = QPushButton("تعديل جلسة")
        edit_session_btn.setStyleSheet(self.get_button_style("#17a2b8", "#138496"))
        edit_session_btn.clicked.connect(self.edit_treatment_session)
        buttons_layout.addWidget(edit_session_btn)

        # زر حذف جلسة
        delete_session_btn = QPushButton("حذف جلسة")
        delete_session_btn.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        delete_session_btn.clicked.connect(self.delete_treatment_session)
        buttons_layout.addWidget(delete_session_btn)

        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)

    def on_treatment_plan_selected(self, selected, deselected):
        """معالجة تحديد خطة معالجة من الجدول"""
        try:
            selected_indexes = selected.indexes()
            if selected_indexes:
                row = selected_indexes[0].row()

                # الحصول على معرف الخطة من البيانات المخفية
                plan_id_item = self.treatment_plans_table.item(row, 0)
                if plan_id_item:
                    self.current_plan_id = plan_id_item.data(Qt.UserRole)

                    # جلب بيانات الخطة المحددة
                    if self.current_plan_id:
                        self.selected_plan_data = self.db_handler.get_treatment_plan(self.current_plan_id)

                        # تحديث جدول جلسات المعالجة
                        self.load_treatment_sessions_data()

                        print(f"تم تحديد خطة المعالجة: {self.current_plan_id}")
            else:
                # إلغاء التحديد
                self.current_plan_id = None
                self.selected_plan_data = None
                self.clear_treatment_sessions_table()

        except Exception as e:
            print(f"خطأ في تحديد خطة المعالجة: {e}")

    def on_treatment_session_selected(self, selected, deselected):
        """معالجة تحديد جلسة معالجة من الجدول"""
        try:
            selected_indexes = selected.indexes()
            if selected_indexes:
                row = selected_indexes[0].row()

                # الحصول على معرف الجلسة من البيانات المخفية
                session_id_item = self.treatment_sessions_table.item(row, 0)
                if session_id_item:
                    session_id = session_id_item.data(Qt.UserRole)
                    print(f"🔍 معرف الجلسة من UserRole: {session_id} (نوع: {type(session_id)})")
                    self.current_session_id = session_id
                    print(f"✅ تم تحديد جلسة المعالجة: {self.current_session_id}")
                else:
                    print("⚠️ لم يتم العثور على عنصر في العمود الأول")
                    self.current_session_id = None
            else:
                print("⚠️ لا توجد صفوف محددة")
                self.current_session_id = None

        except Exception as e:
            print(f"خطأ في تحديد جلسة المعالجة: {e}")

    def load_treatment_plans_data(self):
        """تحميل بيانات خطط المعالجة في الجدول"""
        try:
            print("🔍 بدء تحميل بيانات خطط المعالجة...")

            # الحصول على المريض المختار
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()
                self.patient_id = current_patient_id # Update self.patient_id
                print(f"📋 معرف المريض المختار: {current_patient_id}")
            else:
                print("❌ لا يمكن الوصول إلى النافذة الرئيسية أو دالة get_current_patient_id")

            if not current_patient_id:
                print("⚠️ لا يوجد مريض مختار - مسح الجدول")
                self.clear_treatment_plans_table()
                return

            # جلب خطط المعالجة للمريض
            print(f"🔍 جلب خطط المعالجة للمريض {current_patient_id}...")
            plans = self.db_handler.get_treatment_plans_by_patient(current_patient_id)
            print(f"📊 تم العثور على {len(plans)} خطة معالجة")

            # تعيين عدد الصفوف
            self.treatment_plans_table.setRowCount(len(plans))

            # ملء البيانات
            for row, plan in enumerate(plans):
                # رقم السن
                tooth_item = QTableWidgetItem(str(plan.get('tooth_number', '')))
                tooth_item.setTextAlignment(Qt.AlignCenter)
                tooth_item.setData(Qt.UserRole, plan.get('id'))  # حفظ معرف الخطة
                self.treatment_plans_table.setItem(row, 0, tooth_item)

                # المعالجة
                treatment_item = QTableWidgetItem(str(plan.get('treatment_description', '')))
                self.treatment_plans_table.setItem(row, 1, treatment_item)

                # الكلفة
                cost_item = QTableWidgetItem(f"{plan.get('cost', 0):,}")
                cost_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_plans_table.setItem(row, 2, cost_item)

                # التاريخ
                date_str = plan.get('plan_date', '')
                if date_str:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = date_str
                else:
                    formatted_date = ''

                date_item = QTableWidgetItem(formatted_date)
                date_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_plans_table.setItem(row, 3, date_item)

                # الحالة
                status_item = QTableWidgetItem(str(plan.get('status', 'نشط')))
                status_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_plans_table.setItem(row, 4, status_item)

            print(f"✅ تم تحميل {len(plans)} خطة معالجة في الجدول بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات خطط المعالجة: {e}")
            self.clear_treatment_plans_table()

    def load_treatment_sessions_data(self):
        """تحميل بيانات جلسات المعالجة في الجدول"""
        try:
            print(f"🔍 بدء تحميل بيانات جلسات المعالجة للخطة: {self.current_plan_id}")

            if not self.current_plan_id:
                print("⚠️ لا توجد خطة محددة - مسح جدول الجلسات")
                self.clear_treatment_sessions_table()
                return

            # جلب جلسات المعالجة للخطة المحددة
            sessions = self.db_handler.get_treatment_sessions_by_plan(self.current_plan_id)
            print(f"📊 تم العثور على {len(sessions)} جلسة معالجة")

            # تعيين عدد الصفوف
            self.treatment_sessions_table.setRowCount(len(sessions))

            # ملء البيانات
            for row, session in enumerate(sessions):
                # التاريخ
                date_str = session.get('session_date', '')
                if date_str:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = date_str
                else:
                    formatted_date = ''

                date_item = QTableWidgetItem(formatted_date)
                date_item.setTextAlignment(Qt.AlignCenter)
                session_id = session.get('id')
                print(f"💾 حفظ معرف الجلسة في الصف {row}: {session_id} (نوع: {type(session_id)})")
                date_item.setData(Qt.UserRole, session_id)  # حفظ معرف الجلسة
                self.treatment_sessions_table.setItem(row, 0, date_item)

                # رقم السن
                tooth_item = QTableWidgetItem(str(session.get('tooth_number', '')))
                tooth_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_sessions_table.setItem(row, 1, tooth_item)

                # الإجراء
                procedure_item = QTableWidgetItem(str(session.get('procedure_description', '')))
                self.treatment_sessions_table.setItem(row, 2, procedure_item)

                # الكلفة (من خطة المعالجة)
                plan_cost = self.selected_plan_data.get('cost', 0) if self.selected_plan_data else 0
                cost_item = QTableWidgetItem(f"{plan_cost:,}")
                cost_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_sessions_table.setItem(row, 3, cost_item)

                # الدفعة
                payment = session.get('payment', 0)
                payment_item = QTableWidgetItem(f"{payment:,}")
                payment_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_sessions_table.setItem(row, 4, payment_item)

                # مجموع الدفعات (التراكمي)
                cumulative_payments = self.db_handler.get_cumulative_payments_up_to_session(
                    self.current_plan_id,
                    session.get('session_date'),
                    session.get('id')
                )
                cumulative_item = QTableWidgetItem(f"{cumulative_payments:,}")
                cumulative_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_sessions_table.setItem(row, 5, cumulative_item)

                # المتبقي (الكلفة - مجموع الدفعات التراكمي)
                remaining = plan_cost - cumulative_payments
                remaining_item = QTableWidgetItem(f"{remaining:,}")
                remaining_item.setTextAlignment(Qt.AlignCenter)
                self.treatment_sessions_table.setItem(row, 6, remaining_item)

            print(f"✅ تم تحميل {len(sessions)} جلسة معالجة في الجدول الرئيسي بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات جلسات المعالجة: {e}")
            self.clear_treatment_sessions_table()

    def clear_treatment_plans_table(self):
        """مسح جدول خطط المعالجة"""
        self.treatment_plans_table.setRowCount(0)
        self.current_plan_id = None
        self.selected_plan_data = None
        self.clear_treatment_sessions_table()

    def clear_treatment_sessions_table(self):
        """مسح جدول جلسات المعالجة"""
        self.treatment_sessions_table.setRowCount(0)
        self.current_session_id = None

    def refresh_data(self):
        """تحديث البيانات في الجداول"""
        self.load_treatment_plans_data()
        if self.current_plan_id:
            self.load_treatment_sessions_data()

    def refresh_for_patient(self):
        """تحديث البيانات عند تغيير المريض"""
        self.current_plan_id = None
        self.current_session_id = None
        self.selected_plan_data = None
        self.load_treatment_plans_data()

    def update_for_patient_change(self):
        """دالة عامة لتحديث التبويبة عند تغيير المريض - يمكن استدعاؤها من النافذة الرئيسية"""
        print("🔄 تم استدعاء update_for_patient_change في DentalTreatmentsTab")
        self.refresh_for_patient()

    # ===== دوال معالجة أزرار خطط المعالجة =====

    def add_treatment_plan(self):
        """إضافة خطة معالجة جديدة"""
        try:
            # التحقق من وجود مريض مختار
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

            if not current_patient_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض أولاً")
                return

            # فتح نافذة إضافة خطة معالجة
            dialog = TreatmentPlanDialog(
                db_handler=self.db_handler,
                patient_id=current_patient_id,
                parent=self
            )

            if dialog.exec_() == QDialog.Accepted:
                plan_data = dialog.get_plan_data()
                self.save_treatment_plan(plan_data)
                QMessageBox.information(self, "نجح", "تم إضافة خطة المعالجة بنجاح")

        except Exception as e:
            print(f"خطأ في إضافة خطة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة خطة المعالجة:\n{str(e)}")

    def edit_treatment_plan(self):
        """تعديل خطة معالجة محددة"""
        try:
            if not self.current_plan_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد خطة معالجة للتعديل")
                return

            # التحقق من وجود مريض مختار
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

            if not current_patient_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض أولاً")
                return

            # فتح نافذة تعديل خطة معالجة
            dialog = TreatmentPlanDialog(
                db_handler=self.db_handler,
                patient_id=current_patient_id,  # إضافة معرف المريض
                plan_id=self.current_plan_id,
                plan_data=self.selected_plan_data,
                parent=self
            )

            if dialog.exec_() == QDialog.Accepted:
                # تحديث الجداول
                self.refresh_data()
                QMessageBox.information(self, "نجح", "تم تعديل خطة المعالجة بنجاح")

        except Exception as e:
            print(f"خطأ في تعديل خطة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل خطة المعالجة:\n{str(e)}")

    def delete_treatment_plan(self):
        """حذف خطة معالجة محددة"""
        try:
            if not self.current_plan_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد خطة معالجة للحذف")
                return

            # رسالة تأكيد الحذف
            tooth_number = self.selected_plan_data.get('tooth_number', 'غير محدد') if self.selected_plan_data else 'غير محدد'
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف خطة المعالجة للسن {tooth_number}؟\n\n"
                f"سيتم حذف جميع جلسات المعالجة المرتبطة بهذه الخطة أيضاً.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف خطة المعالجة (سيتم حذف الجلسات تلقائياً بسبب CASCADE)
                success = self.db_handler.delete_treatment_plan(self.current_plan_id)

                if success:
                    # تحديث الجداول
                    self.refresh_data()
                    QMessageBox.information(self, "تم الحذف", "تم حذف خطة المعالجة وجلساتها بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف خطة المعالجة")

        except Exception as e:
            print(f"خطأ في حذف خطة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف خطة المعالجة:\n{str(e)}")

    # ===== دوال معالجة أزرار جلسات المعالجة =====

    def add_treatment_session(self):
        """إضافة جلسة معالجة جديدة"""
        try:
            if not self.current_plan_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد خطة معالجة أولاً لإضافة جلسة")
                return

            # الحصول على معرف المريض
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

            # فتح نافذة إضافة جلسة معالجة
            dialog = TreatmentSessionDialog(
                plan_id=self.current_plan_id,
                cost=self.selected_plan_data.get('cost', 0) if self.selected_plan_data else 0,
                patient_id=current_patient_id,
                parent=self
            )

            if dialog.exec_() == QDialog.Accepted:
                # تحديث جدول جلسات المعالجة
                self.load_treatment_sessions_data()
                QMessageBox.information(self, "نجح", "تم إضافة جلسة المعالجة بنجاح")

        except Exception as e:
            print(f"خطأ في إضافة جلسة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة جلسة المعالجة:\n{str(e)}")

    def edit_treatment_session(self):
        """تعديل جلسة معالجة محددة"""
        try:
            print(f"🔍 محاولة تعديل جلسة المعالجة - معرف الجلسة الحالي: {self.current_session_id}")

            if not self.current_session_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد جلسة معالجة للتعديل")
                return

            # جلب بيانات الجلسة
            print(f"🔍 جلب بيانات الجلسة من قاعدة البيانات للمعرف: {self.current_session_id}")
            session_data = self.db_handler.get_treatment_session(self.current_session_id)
            print(f"📊 بيانات الجلسة المسترجعة: {session_data}")

            if not session_data:
                QMessageBox.critical(self, "خطأ", f"لم يتم العثور على بيانات الجلسة للمعرف: {self.current_session_id}")
                return

            # الحصول على معرف المريض
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

            # فتح نافذة تعديل جلسة معالجة
            dialog = TreatmentSessionDialog(
                plan_id=self.current_plan_id,
                cost=self.selected_plan_data.get('cost', 0) if self.selected_plan_data else 0,
                patient_id=current_patient_id,
                session_data=session_data,
                parent=self
            )

            if dialog.exec_() == QDialog.Accepted:
                # تحديث جدول جلسات المعالجة
                self.load_treatment_sessions_data()
                QMessageBox.information(self, "نجح", "تم تعديل جلسة المعالجة بنجاح")

        except Exception as e:
            print(f"خطأ في تعديل جلسة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل جلسة المعالجة:\n{str(e)}")

    def delete_treatment_session(self):
        """حذف جلسة معالجة محددة"""
        try:
            if not self.current_session_id:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد جلسة معالجة للحذف")
                return

            # رسالة تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من حذف جلسة المعالجة المحددة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف جلسة المعالجة
                success = self.db_handler.delete_treatment_session(self.current_session_id)

                if success:
                    # تحديث جدول جلسات المعالجة
                    self.load_treatment_sessions_data()
                    QMessageBox.information(self, "تم الحذف", "تم حذف جلسة المعالجة بنجاح")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف جلسة المعالجة")

        except Exception as e:
            print(f"خطأ في حذف جلسة المعالجة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف جلسة المعالجة:\n{str(e)}")

    def create_main_control_buttons(self, parent_layout):
        """إنشاء أزرار التحكم الرئيسية والعرض"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # أزرار التحكم الأساسية
        # زر حفظ
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet(self.get_button_style("#28a745", "#218838"))
        save_btn.clicked.connect(self.save_treatment_plan)
        buttons_layout.addWidget(save_btn)

        # زر إلغاء - لون أحمر لاتباع معايير واجهة المستخدم
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        cancel_btn.clicked.connect(self.cancel_changes)
        buttons_layout.addWidget(cancel_btn)

        # زر حذف
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet(self.get_button_style("#dc3545", "#c82333"))
        delete_btn.clicked.connect(self.delete_treatment_plan)
        buttons_layout.addWidget(delete_btn)

        # زر إضافة خطة معالجة سنية
        add_plan_btn = QPushButton("إضافة خطة معالجة سنية")
        add_plan_btn.setStyleSheet(self.get_button_style("#ffc107", "#e0a800"))
        add_plan_btn.clicked.connect(self.add_new_treatment_plan)
        buttons_layout.addWidget(add_plan_btn)

        # زر إضافة جلسة معالجة سنية
        add_session_btn = QPushButton("إضافة جلسة معالجة سنية")
        add_session_btn.setStyleSheet(self.get_button_style("#007bff", "#0056b3"))
        add_session_btn.clicked.connect(self.add_treatment_session)
        buttons_layout.addWidget(add_session_btn)

        # فاصل بصري
        buttons_layout.addSpacing(20)

        # أزرار العرض (المدمجة من الحاوية اليسرى)
        # زر جدول المعالجات السنية
        view_plans_btn = QPushButton("جدول المعالجات السنية")
        view_plans_btn.setStyleSheet(self.get_button_style("#17a2b8", "#138496"))
        view_plans_btn.clicked.connect(self.view_treatment_plans)
        buttons_layout.addWidget(view_plans_btn)

        # زر جدول الجلسات السنية
        view_sessions_btn = QPushButton("جدول الجلسات السنية")
        view_sessions_btn.setStyleSheet(self.get_button_style("#6f42c1", "#5a32a3"))
        view_sessions_btn.clicked.connect(self.view_treatment_sessions)
        buttons_layout.addWidget(view_sessions_btn)

        # زر تعديل أسعار علاج الأسنان
        edit_prices_btn = QPushButton("تعديل أسعار علاج الأسنان")
        edit_prices_btn.setStyleSheet(self.get_button_style("#fd7e14", "#e8690b"))
        edit_prices_btn.clicked.connect(self.edit_default_prices)
        buttons_layout.addWidget(edit_prices_btn)

        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)

    def get_button_style(self, bg_color, hover_color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
        """



    def save_treatment_plan(self, plan_data):
        """حفظ خطة المعالجة"""
        # استخدام معرف المريض المحفوظ في الكلاس أولاً
        current_patient_id = self.patient_id

        # إذا لم يكن متوفراً، حاول الحصول عليه من النافذة الرئيسية
        if not current_patient_id:
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

        if not current_patient_id:
            QMessageBox.warning(self, "تحذير",
                              "يرجى اختيار مريض من تبويب المرضى أولاً.\n"
                              "سيظهر اسم المريض المختار في شريط التبويبات العلوي.")
            return

        # التحقق من صحة البيانات
        if not plan_data['tooth_number']:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد رقم السن")
            return

        if not plan_data['treatment'].strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال تفاصيل المعالجة")
            return

        if plan_data['cost'] <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلفة صحيحة")
            return

        try:
            # إضافة معرف المريض إلى بيانات الخطة
            plan_data['patient_id'] = current_patient_id

            # حفظ البيانات في قاعدة البيانات
            plan_id = self.db_handler.save_treatment_plan(plan_data)

            # حفظ معرف الخطة المحفوظة للاستخدام في الجلسات
            self.current_plan_id = plan_id

            # الحصول على اسم المريض للرسالة
            patient_name = ""
            if self.main_window and hasattr(self.main_window, 'get_current_patient_name'):
                patient_name = self.main_window.get_current_patient_name()

            success_message = f"تم حفظ خطة المعالجة بنجاح\n"
            if patient_name:
                success_message += f"المريض: {patient_name}\n"
            success_message += f"رقم السن: {plan_data['tooth_number']}\n"
            success_message += f"معرف الخطة: {plan_id}"

            QMessageBox.information(self, "نجح", success_message)

            # تحديث جدول خطط المعالجة في الواجهة
            self.refresh_treatment_plans_table()

            self.clear_form()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def cancel_changes(self):
        """إلغاء التغييرات"""
        reply = QMessageBox.question(
            self,
            "تأكيد الإلغاء",
            "هل أنت متأكد من إلغاء التغييرات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.clear_form()



    def add_new_treatment_plan(self):
        """إضافة خطة معالجة جديدة مع ترقيم تلقائي ذكي"""
        try:
            # مسح النموذج
            self.clear_form()

            # تعيين التاريخ الحالي
            self.treatment_plan.date_edit.setDate(QDate.currentDate())

            QMessageBox.information(self, "خطة جديدة", "تم إنشاء خطة معالجة جديدة")

        except Exception as e:
            print(f"خطأ في إضافة خطة معالجة جديدة: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء خطة جديدة: {str(e)}")











    def add_treatment_session(self):
        """إضافة جلسة معالجة سنية"""
        # التحقق من اختيار المريض من النافذة الرئيسية
        current_patient_id = None
        if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
            current_patient_id = self.main_window.get_current_patient_id()

        if not current_patient_id:
            QMessageBox.warning(self, "تحذير",
                              "يرجى اختيار مريض من تبويب المرضى أولاً.\n"
                              "سيظهر اسم المريض المختار في شريط التبويبات العلوي.")
            return

        # التحقق من وجود خطة معالجة محفوظة
        if not self.current_plan_id:
            # عرض خيارات للمستخدم
            reply = QMessageBox.question(
                self,
                "خطة المعالجة غير محفوظة",
                "يرجى حفظ خطة المعالجة أولاً أو اختيار خطة موجودة من جدول خطط المعالجة.\n\n"
                "ماذا تريد أن تفعل؟",
                QMessageBox.Save | QMessageBox.Open | QMessageBox.Cancel,
                QMessageBox.Save
            )

            if reply == QMessageBox.Save:
                # حفظ الخطة الحالية
                self.save_treatment_plan()
                # التحقق من نجاح الحفظ
                if not self.current_plan_id:
                    return  # فشل في الحفظ
            elif reply == QMessageBox.Open:
                # فتح جدول خطط المعالجة للاختيار
                self.view_treatment_plans()
                return
            else:
                return  # إلغاء العملية

        # الحصول على معرف المريض
        current_patient_id = self.patient_id
        if not current_patient_id and self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
            current_patient_id = self.main_window.get_current_patient_id()

        if not current_patient_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد مريض محدد لإضافة جلسة له.")
            return

        # الحصول على بيانات الخطة من قاعدة البيانات
        plan_data = self.db_handler.get_treatment_plan(self.current_plan_id)
        plan_cost = plan_data.get('cost', 0) if plan_data else 0

        # فتح نافذة جلسات المعالجة مع معرف الخطة
        dialog = TreatmentSessionDialog(
            plan_id=self.current_plan_id,
            cost=plan_cost,
            patient_id=current_patient_id,
            parent=self
        )

        if dialog.exec_() == QDialog.Accepted:
            # تحديث جدول جلسات المعالجة في الواجهة
            self.refresh_treatment_sessions_table()
            QMessageBox.information(self, "نجح", "تم حفظ جلسة المعالجة بنجاح")

    def view_treatment_plans(self):
        """عرض خطط المعالجة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("خطط المعالجة السنية")
        dialog.setModal(True)
        # تعديل الحجم ليطابق حجم واجهة تبويبة علاج الأسنان الرئيسية
        dialog.setGeometry(100, 100, 1400, 900)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة على الشاشة
        self.center_dialog_on_screen(dialog)

        layout = QVBoxLayout(dialog)

        # إضافة تعليمات للمستخدم
        instructions_label = QLabel("💡 انقر على أي صف لتحديد خطة المعالجة وتحميل بياناتها في النموذج الرئيسي")
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                color: #1976d2;
                padding: 10px;
                border-radius: 5px;
                font-size: 12px;
                border: 1px solid #bbdefb;
                margin-bottom: 10px;
            }
        """)
        instructions_label.setWordWrap(True)
        layout.addWidget(instructions_label)

        # إنشاء جدول خطط المعالجة (بدون رقم الخطة)
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels([
            "رقم السن", "المعالجة", "الكلفة", "التاريخ", "الحالة"
        ])

        # تنسيق الجدول مع تأثيرات التفاعل
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)  # تحديد الصف كاملاً
        table.setSelectionMode(QTableWidget.SingleSelection)  # تحديد صف واحد فقط
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #007bff;
                selection-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # جلب البيانات من قاعدة البيانات وعرضها
        current_patient_id = None
        if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
            current_patient_id = self.main_window.get_current_patient_id()

        if current_patient_id:
            self.load_treatment_plans_data_to_table(table, current_patient_id)
            # حفظ مرجع للجدول للتحديث اللاحق
            self.current_plans_table = table

            # ربط إشارة تحديد الصف بدالة تحميل البيانات
            table.itemSelectionChanged.connect(lambda: self.on_treatment_plan_selected_from_dialog(table, dialog))
        else:
            # إضافة صف يوضح أنه لا يوجد مريض مختار
            table.setRowCount(1)
            no_patient_item = QTableWidgetItem("لا يوجد مريض مختار")
            no_patient_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, 0, no_patient_item)
            table.setSpan(0, 0, 1, 5)  # دمج جميع الأعمدة

        layout.addWidget(table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec_()

    def on_treatment_plan_selected_from_dialog(self, table, dialog):
        """معالجة تحديد خطة معالجة من الجدول في النافذة المنبثقة"""
        try:
            # الحصول على الصف المحدد
            selected_items = table.selectedItems()
            if not selected_items:
                return

            # الحصول على رقم الصف المحدد
            selected_row = selected_items[0].row()

            # الحصول على معرف الخطة من البيانات المخفية في العمود الأول
            plan_id_item = table.item(selected_row, 0)
            if not plan_id_item:
                return

            plan_id = plan_id_item.data(Qt.UserRole)
            if not plan_id:
                print("لا يوجد معرف خطة في البيانات المخفية")
                return

            # جلب بيانات الخطة من قاعدة البيانات
            plan_data = self.db_handler.get_treatment_plan(plan_id)
            if not plan_data:
                QMessageBox.warning(dialog, "خطأ", "لا يمكن العثور على بيانات الخطة المحددة")
                return

            # تحميل البيانات في النموذج الرئيسي
            self.treatment_plan.load_plan_data(plan_data)

            # تعيين معرف الخطة الحالية
            self.current_plan_id = plan_id

            # عرض رسالة تأكيد
            tooth_number = plan_data.get('tooth_number', 'غير محدد')
            treatment_description = plan_data.get('treatment_description', 'غير محدد')

            QMessageBox.information(
                dialog,
                "تم تحديد الخطة",
                f"تم تحميل بيانات خطة المعالجة:\n\n"
                f"رقم السن: {tooth_number}\n"
                f"المعالجة: {treatment_description}\n"
                f"معرف الخطة: {plan_id}\n\n"
                f"يمكنك الآن تعديل البيانات في النموذج الرئيسي."
            )

            # إغلاق النافذة المنبثقة
            dialog.accept()

            print(f"تم تحديد وتحميل الخطة: {plan_id} - السن {tooth_number}")

        except Exception as e:
            print(f"خطأ في تحديد خطة المعالجة: {e}")
            QMessageBox.critical(dialog, "خطأ", f"حدث خطأ أثناء تحديد الخطة:\n{str(e)}")

    def center_dialog_on_screen(self, dialog):
        """توسيط النافذة المنبثقة على الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            dialog_size = dialog.geometry()
            x = (screen.width() - dialog_size.width()) // 2
            y = (screen.height() - dialog_size.height()) // 2
            dialog.move(x, y)
        except Exception as e:
            print(f"خطأ في توسيط النافذة المنبثقة: {e}")

    def load_treatment_plans_data_to_table(self, table, patient_id):
        """تحميل بيانات خطط المعالجة في الجدول المحدد"""
        try:
            # جلب خطط المعالجة للمريض
            plans = self.db_handler.get_treatment_plans_by_patient(patient_id)

            # تعيين عدد الصفوف
            table.setRowCount(len(plans))

            # ملء البيانات (بدون رقم الخطة)
            for row, plan in enumerate(plans):
                # رقم السن
                tooth_number_item = QTableWidgetItem(str(plan.get('tooth_number', '')))
                tooth_number_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 0, tooth_number_item)

                # المعالجة
                treatment_item = QTableWidgetItem(str(plan.get('treatment_description', '')))
                table.setItem(row, 1, treatment_item)

                # الكلفة
                cost_item = QTableWidgetItem(f"{plan.get('cost', 0):,}")
                cost_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 2, cost_item)

                # التاريخ
                date_str = plan.get('plan_date', '')
                if date_str:
                    try:
                        # تحويل التاريخ إلى تنسيق أفضل
                        from datetime import datetime
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = date_str
                else:
                    formatted_date = ''

                date_item = QTableWidgetItem(formatted_date)
                date_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 3, date_item)

                # الحالة
                status_item = QTableWidgetItem(str(plan.get('status', 'نشط')))
                status_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 4, status_item)

                # حفظ معرف الخطة في البيانات المخفية (في العمود الأول)
                tooth_number_item.setData(Qt.UserRole, plan.get('id'))

            # تحديث عرض الأعمدة
            table.resizeColumnsToContents()

        except Exception as e:
            print(f"خطأ في تحميل بيانات خطط المعالجة: {e}")
            # إضافة صف خطأ
            table.setRowCount(1)
            error_item = QTableWidgetItem(f"خطأ في تحميل البيانات: {str(e)}")
            error_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, 0, error_item)
            table.setSpan(0, 0, 1, 5)

    def view_treatment_sessions(self):
        """عرض جلسات المعالجة"""
        dialog = QDialog(self)
        dialog.setWindowTitle("جلسات المعالجة السنية")
        dialog.setModal(True)
        # تعديل الحجم ليطابق حجم واجهة تبويبة علاج الأسنان الرئيسية
        dialog.setGeometry(100, 100, 1400, 900)
        dialog.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة على الشاشة
        self.center_dialog_on_screen(dialog)

        layout = QVBoxLayout(dialog)

        # إنشاء جدول جلسات المعالجة (بدون رقم الخطة)
        table = QTableWidget()
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "التاريخ", "رقم السن", "الإجراء", "الكلفة", "الدفعة", "مجموع الدفعات", "المتبقي"
        ])

        # تنسيق الجدول
        table.horizontalHeader().setStretchLastSection(True)
        table.setAlternatingRowColors(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        # جلب البيانات من قاعدة البيانات وعرضها
        if self.current_plan_id:
            self.load_treatment_sessions_data_to_table(table, self.current_plan_id)
            # حفظ مرجع للجدول للتحديث اللاحق
            self.current_sessions_table = table
        else:
            # إضافة صف يوضح أنه لا توجد خطة محددة
            table.setRowCount(1)
            no_plan_item = QTableWidgetItem("لا توجد خطة معالجة محددة")
            no_plan_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, 0, no_plan_item)
            table.setSpan(0, 0, 1, 7)  # دمج جميع الأعمدة

        layout.addWidget(table)

        # زر إغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec_()

    def load_treatment_sessions_data_to_table(self, table, plan_id):
        """تحميل بيانات جلسات المعالجة في الجدول المحدد"""
        try:
            print(f"🔍 بدء تحميل بيانات جلسات المعالجة للجدول المنبثق - خطة: {plan_id}")

            # جلب جلسات المعالجة للخطة
            sessions = self.db_handler.get_treatment_sessions_by_plan(plan_id)
            print(f"📊 تم العثور على {len(sessions)} جلسة معالجة للجدول المنبثق")

            # تعيين عدد الصفوف
            table.setRowCount(len(sessions))

            # ملء البيانات (بدون رقم الخطة)
            for row, session in enumerate(sessions):
                # التاريخ
                date_str = session.get('session_date', '')
                if date_str:
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                        formatted_date = date_obj.strftime('%d/%m/%Y')
                    except:
                        formatted_date = date_str
                else:
                    formatted_date = ''

                date_item = QTableWidgetItem(formatted_date)
                date_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 0, date_item)

                # رقم السن
                tooth_number_item = QTableWidgetItem(str(session.get('tooth_number', '')))
                tooth_number_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 1, tooth_number_item)

                # الإجراء
                procedure_item = QTableWidgetItem(str(session.get('procedure_description', '')))
                table.setItem(row, 2, procedure_item)

                # الكلفة - جلسات المعالجة لا تحتوي على كلفة منفصلة، نستخدم الدفعة
                payment_amount = session.get('payment', 0)
                cost_item = QTableWidgetItem(f"{payment_amount:,}")
                cost_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 3, cost_item)

                # الدفعة
                payment_item = QTableWidgetItem(f"{payment_amount:,}")
                payment_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 4, payment_item)

                # مجموع الدفعات (التراكمي)
                cumulative_payments = self.db_handler.get_cumulative_payments_up_to_session(
                    plan_id,
                    session.get('session_date'),
                    session.get('id')
                )
                print(f"💰 مجموع الدفعات التراكمي للجلسة {session.get('id')}: {cumulative_payments}")
                cumulative_item = QTableWidgetItem(f"{cumulative_payments:,}")
                cumulative_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 5, cumulative_item)

                # المتبقي - للجلسات عادة يكون 0 لأن الدفعة تغطي الجلسة
                remaining_item = QTableWidgetItem("0")
                remaining_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 6, remaining_item)

            # تحديث عرض الأعمدة
            table.resizeColumnsToContents()
            print(f"✅ تم تحميل {len(sessions)} جلسة معالجة في الجدول المنبثق بنجاح")

        except Exception as e:
            print(f"خطأ في تحميل بيانات جلسات المعالجة: {e}")
            # إضافة صف خطأ
            table.setRowCount(1)
            error_item = QTableWidgetItem(f"خطأ في تحميل البيانات: {str(e)}")
            error_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(0, 0, error_item)
            table.setSpan(0, 0, 1, 7)

    def clear_form(self):
        """مسح النموذج"""
        self.treatment_plan.clear_form()
        self.treatment_options.clear_all_options()

        # إعادة تعيين معرف الخطة الحالية
        self.current_plan_id = None

        # إلغاء تحديد السن من المخطط
        if self.teeth_chart.selected_tooth:
            selected_btn = self.teeth_chart.tooth_buttons[self.teeth_chart.selected_tooth]
            selected_btn.set_selected(False)

    def refresh_treatment_plans_table(self):
        """تحديث جدول خطط المعالجة"""
        try:
            # الحصول على معرف المريض من النافذة الرئيسية
            current_patient_id = None
            if self.main_window and hasattr(self.main_window, 'get_current_patient_id'):
                current_patient_id = self.main_window.get_current_patient_id()

            if hasattr(self, 'db_handler') and current_patient_id:
                # تحديث جدول خطط المعالجة للمريض الحالي
                plans = self.db_handler.get_treatment_plans_by_patient(current_patient_id)
                print(f"تم تحديث جدول خطط المعالجة: {len(plans)} خطة للمريض {current_patient_id}")

                # إذا كان هناك جدول مفتوح، قم بتحديثه
                if hasattr(self, 'current_plans_table') and self.current_plans_table:
                    self.load_treatment_plans_data_to_table(self.current_plans_table, current_patient_id)
            else:
                print("لا يوجد مريض مختار لتحديث جدول خطط المعالجة")
        except Exception as e:
            print(f"خطأ في تحديث جدول خطط المعالجة: {e}")

    def refresh_treatment_sessions_table(self):
        """تحديث جدول جلسات المعالجة"""
        try:
            if hasattr(self, 'db_handler') and self.current_plan_id:
                # تحديث جدول جلسات المعالجة الرئيسي
                self.load_treatment_sessions_data()
                print(f"🔄 تم تحديث جدول جلسات المعالجة الرئيسي للخطة {self.current_plan_id}")

                # إذا كان هناك جدول منبثق مفتوح، قم بتحديثه أيضاً
                if hasattr(self, 'current_sessions_table') and self.current_sessions_table:
                    self.load_treatment_sessions_data_to_table(self.current_sessions_table, self.current_plan_id)
                    print(f"🔄 تم تحديث جدول جلسات المعالجة المنبثق للخطة {self.current_plan_id}")
            else:
                print("⚠️ لا توجد خطة محددة لتحديث جدول جلسات المعالجة")
        except Exception as e:
            print(f"❌ خطأ في تحديث جدول جلسات المعالجة: {e}")

    def set_current_plan_from_table(self, plan_id):
        """تعيين الخطة الحالية من جدول خطط المعالجة"""
        try:
            if hasattr(self, 'db_handler'):
                plan_data = self.db_handler.get_treatment_plan_by_id(plan_id)
                if plan_data:
                    self.current_plan_id = plan_id
                    # تحديث النموذج ببيانات الخطة المحددة
                    self.treatment_plan.load_plan_data(plan_data)
                    print(f"تم تحديد الخطة: {plan_id}")
        except Exception as e:
            print(f"خطأ في تحديد الخطة: {e}")

    def edit_default_prices(self):
        """فتح نافذة إدارة أسعار وأسماء المعالجة الشاملة"""
        dialog = ComprehensiveTreatmentPricingDialog(self)
        dialog.exec_()







class ComprehensiveTreatmentPricingDialog(QDialog):
    """نافذة إدارة أسعار وأسماء المعالجة الشاملة - نسخة جديدة مبسطة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_tab = parent
        self.prices_manager = PricesManager()
        self.treatment_name_fields = {}  # حقول أسماء المعالجات
        self.price_spinboxes = {}  # حقول الأسعار
        self.current_prices = {}

        # إنشاء الواجهة أولاً
        print("🔧 إنشاء واجهة النافذة...")
        self.init_ui()
        # ثم تحميل البيانات وتعبئة الحقول
        print("📊 تحميل البيانات...")
        self.load_current_data()
        print("✏️ تعبئة الحقول...")
        self.populate_fields()
        print(f"✅ تم إنشاء النافذة بنجاح - حقول الأسماء: {len(self.treatment_name_fields)}, حقول الأسعار: {len(self.price_spinboxes)}")

    def init_ui(self):
        """تهيئة واجهة النافذة"""
        self.setWindowTitle("تعديل أسعار وأسماء علاج الأسنان")
        self.setModal(True)
        self.setMinimumSize(1600, 900)  # زيادة الارتفاع لإظهار الأزرار
        self.resize(1600, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # توسيط النافذة
        self.center_on_screen()

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # عنوان النافذة
        title_label = QLabel("تعديل أسعار وأسماء علاج الأسنان")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # إنشاء ويدجت المحتوى
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # إنشاء حاوية المجموعات الثمانية
        self.create_eight_groups_container(content_layout)

        # إضافة المحتوى إلى منطقة التمرير
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area, 1)  # نسبة تمدد 1 (لا تأخذ كل المساحة)

        # إضافة مساحة فارغة صغيرة قبل الأزرار
        main_layout.addSpacing(20)

        # أزرار التحكم (خارج منطقة التمرير)
        self.create_control_buttons(main_layout)

        # التأكد من أن الأزرار ظاهرة
        main_layout.setStretch(0, 1)  # منطقة التمرير تأخذ المساحة المتاحة
        main_layout.setStretch(2, 0)  # الأزرار لا تتمدد

    def create_eight_groups_container(self, main_layout):
        """إنشاء حاوية المجموعات الثمانية مطابقة للأصل"""
        print("🏗️ إنشاء حاوية المجموعات الثمانية...")

        # إنشاء مجموعة رئيسية
        main_group = QGroupBox("تعديل خيارات المعالجة والأسعار")
        main_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #f8f9ff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 8px 2px 8px;
                color: #28a745;
                background-color: #f8f9ff;
            }
        """)

        # تخطيط أفقي للمجموعات الثمانية
        horizontal_layout = QHBoxLayout(main_group)
        horizontal_layout.setSpacing(8)
        horizontal_layout.setContentsMargins(10, 25, 10, 15)

        # إنشاء المجموعات الثمانية
        print("📋 إنشاء مجموعة اللبية...")
        endodontic_group = self.create_treatment_group("لبية (Endodontic)", [
            "Vital", "Necrotic", "إعادة معالجة", "متكلسة",
            "C shape", "ذروة مفتوحة", "أداة مكسورة", "منحنية بشدة"
        ], "endodontic", is_price_group=False)

        endodontic_prices_group = self.create_treatment_group("أسعار اللبية", [
            ("Vital", 120000), ("Necrotic", 150000), ("إعادة معالجة", 200000), ("متكلسة", 180000),
            ("C shape", 250000), ("ذروة مفتوحة", 160000), ("أداة مكسورة", 300000), ("منحنية بشدة", 150000)
        ], "endodontic", is_price_group=True)

        restorative_group = self.create_treatment_group("ترميمية (Restorative)", [
            "كومبوزت", "أملغم", "GIC", "وتد فايبر",
            "قلب معدني", "Onlay", "Inlay", "Rebond"
        ], "restorative", is_price_group=False)

        restorative_prices_group = self.create_treatment_group("أسعار الترميمية", [
            ("كومبوزت", 75000), ("أملغم", 50000), ("GIC", 40000), ("وتد فايبر", 80000),
            ("قلب معدني", 60000), ("Onlay", 200000), ("Inlay", 180000), ("Rebond", 50000)
        ], "restorative", is_price_group=True)

        crowns_group = self.create_treatment_group("تيجان (Crowns)", [
            "خزف معدن", "زيركون 4D", "زيركون مغطى إيماكس", "زيركون مغطى خزف",
            "زيركون cutback", "ستانلس", "إيماكس", "زيركون Full Anatomy"
        ], "crowns", is_price_group=False)

        crowns_prices_group = self.create_treatment_group("أسعار التيجان", [
            ("خزف معدن", 150000), ("زيركون 4D", 200000), ("زيركون مغطى إيماكس", 250000), ("زيركون مغطى خزف", 220000),
            ("زيركون cutback", 230000), ("ستانلس", 80000), ("إيماكس", 180000), ("زيركون Full Anatomy", 400000)
        ], "crowns", is_price_group=True)

        surgery_group = self.create_treatment_group("جراحة (Surgery)", [
            "قلع بسيط", "قلع جراحي", "منحصرة", "منطمرة",
            "تطويل تاج", "قطع ذروة", "تضحيك", "بتر جذر"
        ], "surgery", is_price_group=False)

        surgery_prices_group = self.create_treatment_group("أسعار الجراحة", [
            ("قلع بسيط", 30000), ("قلع جراحي", 75000), ("منحصرة", 100000), ("منطمرة", 120000),
            ("تطويل تاج", 80000), ("قطع ذروة", 90000), ("تضحيك", 150000), ("بتر جذر", 100000)
        ], "surgery", is_price_group=True)

        # ترتيب المجموعات أفقياً
        horizontal_layout.addWidget(endodontic_group)
        horizontal_layout.addWidget(endodontic_prices_group)
        horizontal_layout.addWidget(restorative_group)
        horizontal_layout.addWidget(restorative_prices_group)
        horizontal_layout.addWidget(crowns_group)
        horizontal_layout.addWidget(crowns_prices_group)
        horizontal_layout.addWidget(surgery_group)
        horizontal_layout.addWidget(surgery_prices_group)

        # تعيين نسب التمدد المتساوية
        for i in range(8):
            horizontal_layout.setStretch(i, 1)

        main_layout.addWidget(main_group)

    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        try:
            from PyQt5.QtWidgets import QDesktopWidget
            screen = QDesktopWidget().screenGeometry()
            dialog_width = 1600
            dialog_height = 800

            x = (screen.width() - dialog_width) // 2
            y = (screen.height() - dialog_height) // 2

            self.setGeometry(x, y, dialog_width, dialog_height)

        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")
            self.setGeometry(100, 100, 1600, 800)

    def create_treatment_group(self, title, options, category, is_price_group=False):
        """إنشاء مجموعة معالجة أو أسعار"""
        print(f"🔨 إنشاء مجموعة: {title} - عدد الخيارات: {len(options)} - أسعار: {is_price_group}")
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                border: 1px solid #ced4da;
                border-radius: 5px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 2px 5px 2px 5px;
                color: #495057;
            }
        """)

        group.setMinimumWidth(180)
        group.setMinimumHeight(400)
        group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        group.setAlignment(Qt.AlignCenter)

        main_layout = QVBoxLayout(group)
        main_layout.setContentsMargins(8, 15, 8, 8)
        main_layout.setAlignment(Qt.AlignTop)

        # حاوية للحقول
        fields_widget = QWidget()
        fields_layout = QVBoxLayout(fields_widget)
        fields_layout.setSpacing(3)
        fields_layout.setContentsMargins(0, 0, 0, 0)

        for option in options:
            if is_price_group:
                # مجموعة أسعار - إنشاء حقل سعر
                name, price = option
                price_field = QSpinBox()
                price_field.setMinimum(0)
                price_field.setMaximum(999999999)
                price_field.setValue(price)
                price_field.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
                price_field.setLayoutDirection(Qt.RightToLeft)
                price_field.setStyleSheet("""
                    QSpinBox {
                        font-size: 11px;
                        font-weight: bold;
                        margin: 1px;
                        padding: 3px;
                        border: 1px solid #ced4da;
                        border-radius: 3px;
                        background-color: #ffffff;
                        min-width: 100px;
                        min-height: 18px;
                    }
                    QSpinBox:focus {
                        border-color: #007bff;
                        background-color: #f8f9ff;
                    }
                    QSpinBox:hover {
                        border-color: #007bff;
                    }
                """)

                field_key = f"{category}_{name}"
                self.price_spinboxes[field_key] = price_field
                fields_layout.addWidget(price_field)
            else:
                # مجموعة أسماء - إنشاء حقل نص
                name_field = QLineEdit(option)
                name_field.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
                name_field.setLayoutDirection(Qt.RightToLeft)
                name_field.setStyleSheet("""
                    QLineEdit {
                        font-size: 11px;
                        margin: 1px;
                        padding: 3px;
                        border: 1px solid #ced4da;
                        border-radius: 3px;
                        background-color: #ffffff;
                        min-width: 100px;
                        min-height: 18px;
                    }
                    QLineEdit:focus {
                        border-color: #007bff;
                        background-color: #f8f9ff;
                    }
                    QLineEdit:hover {
                        border-color: #007bff;
                    }
                """)

                field_key = f"{category}_{option}"
                self.treatment_name_fields[field_key] = name_field
                fields_layout.addWidget(name_field)

        main_layout.addWidget(fields_widget)
        main_layout.addStretch()
        return group

    def create_control_buttons(self, main_layout):
        """إنشاء أزرار التحكم"""
        print("🔘 بدء إنشاء أزرار التحكم...")

        # إنشاء حاوية للأزرار مع خلفية مميزة
        buttons_container = QWidget()
        print("✅ تم إنشاء حاوية الأزرار")
        buttons_container.setStyleSheet("""
            QWidget {
                background-color: #e9ecef;
                border-top: 2px solid #6c757d;
                border-radius: 0px 0px 8px 8px;
                padding: 10px;
                min-height: 80px;
                max-height: 80px;
            }
        """)
        buttons_container.setFixedHeight(80)  # ارتفاع ثابت للأزرار

        buttons_layout = QHBoxLayout(buttons_container)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(20, 15, 20, 15)

        # زر الحفظ
        save_btn = QPushButton("حفظ جميع التغييرات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: 2px solid #1e7e34;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 150px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #218838;
                border: 2px solid #155724;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
                border: 2px solid #155724;
            }
        """)
        save_btn.clicked.connect(self.save_all_changes)
        buttons_layout.addWidget(save_btn)
        print("✅ تم إنشاء زر الحفظ")

        # زر الاستعادة
        restore_btn = QPushButton("استعادة افتراضي")
        restore_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: 2px solid #dc5200;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 150px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #e8590c;
                border: 2px solid #c44500;
            }
            QPushButton:pressed {
                background-color: #dc5200;
                border: 2px solid #c44500;
            }
        """)
        restore_btn.clicked.connect(self.restore_defaults)
        buttons_layout.addWidget(restore_btn)
        print("✅ تم إنشاء زر الاستعادة")

        # زر الإلغاء
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: 2px solid #bd2130;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-width: 150px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #c82333;
                border: 2px solid #a71e2a;
            }
            QPushButton:pressed {
                background-color: #bd2130;
                border: 2px solid #a71e2a;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_btn)
        print("✅ تم إنشاء زر الإلغاء")

        buttons_layout.addStretch()

        # إضافة حاوية الأزرار إلى التخطيط الرئيسي
        main_layout.addWidget(buttons_container)
        print("✅ تم إضافة حاوية الأزرار إلى التخطيط الرئيسي")
        print("🎯 انتهاء إنشاء أزرار التحكم بنجاح")
    def load_current_data(self):
        """تحميل البيانات الحالية"""
        try:
            self.current_prices = self.prices_manager.load_prices()
            print(f"تم تحميل {len(self.current_prices)} سعر")
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.current_prices = {}

    def populate_fields(self):
        """تعبئة الحقول بالبيانات المحملة"""
        # الحصول على الأسعار الافتراضية كنسخة احتياطية
        default_prices = self.prices_manager.get_default_prices()

        # تعبئة حقول الأسعار
        for field_key, price_field in self.price_spinboxes.items():
            if field_key in self.current_prices:
                price_field.setValue(self.current_prices[field_key])
            elif field_key in default_prices:
                price_field.setValue(default_prices[field_key])
            else:
                # قيمة افتراضية عامة
                price_field.setValue(50000)

    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        try:
            # جمع البيانات المحدثة
            updated_prices = {}
            updated_names = {}

            # جمع الأسعار المحدثة
            for field_key, price_field in self.price_spinboxes.items():
                updated_prices[field_key] = price_field.value()

            # جمع أسماء المعالجات المحدثة
            for field_key, name_field in self.treatment_name_fields.items():
                updated_names[field_key] = name_field.text().strip()

            # التحقق من صحة البيانات
            if not self.validate_data(updated_names, updated_prices):
                return

            # حفظ الأسعار والأسماء
            prices_success = self.prices_manager.save_prices(updated_prices)
            names_success = self.save_treatment_names(updated_names)

            if prices_success and names_success:
                # تحديث البيانات في الواجهة الرئيسية
                self.update_main_interface(updated_prices, updated_names)

                # رسالة نجاح
                QMessageBox.information(self, "نجح الحفظ",
                                      "تم حفظ جميع التغييرات بنجاح!\n"
                                      "ستبقى التغييرات محفوظة عند إعادة تشغيل التطبيق.")

                # إغلاق النافذة
                self.accept()

            else:
                error_msg = "فشل في حفظ "
                if not prices_success:
                    error_msg += "الأسعار "
                if not names_success:
                    error_msg += "أسماء المعالجات "
                QMessageBox.critical(self, "خطأ", error_msg)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء الحفظ: {str(e)}")

    def validate_data(self, names, prices):
        """التحقق من صحة البيانات"""
        # التحقق من عدم وجود أسماء فارغة
        for field_key, name in names.items():
            if not name.strip():
                QMessageBox.warning(self, "تحذير", f"اسم المعالجة لا يمكن أن يكون فارغاً: {field_key}")
                return False

        # التحقق من الأسعار
        for field_key, price in prices.items():
            if price < 0:
                QMessageBox.warning(self, "تحذير", f"السعر لا يمكن أن يكون سالباً: {field_key}")
                return False

        return True

    def save_treatment_names(self, treatment_names):
        """حفظ أسماء المعالجات في ملف التكوين"""
        try:
            config_file = "dental_prices_config.json"

            # تحميل البيانات الموجودة أو إنشاء ملف جديد
            existing_data = {}
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)

            # تحديث أسماء المعالجات
            existing_data['treatment_names'] = treatment_names

            # حفظ البيانات المحدثة
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=4)

            print(f"تم حفظ أسماء المعالجات في {config_file}")
            return True
        except Exception as e:
            print(f"خطأ في حفظ أسماء المعالجات: {e}")
            return False

    def update_main_interface(self, updated_prices, updated_names=None):
        """تحديث البيانات في الواجهة الرئيسية"""
        try:
            if self.parent_tab and hasattr(self.parent_tab, 'treatment_options'):
                # تحديث الأسعار في واجهة خيارات المعالجة
                self.parent_tab.treatment_options.update_prices(updated_prices)

                # تحديث خيارات المعالجة (الأسماء والأسعار)
                self.parent_tab.treatment_options.refresh_options()

                print("تم تحديث الأسعار والأسماء في الواجهة الرئيسية")
        except Exception as e:
            print(f"خطأ في تحديث الواجهة الرئيسية: {e}")

    def restore_defaults(self):
        """استعادة القيم الافتراضية"""
        try:
            reply = QMessageBox.question(self, "تأكيد الاستعادة",
                                       "هل أنت متأكد من استعادة جميع الأسعار والأسماء إلى القيم الافتراضية؟\n"
                                       "سيتم فقدان جميع التعديلات الحالية!",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)

            if reply == QMessageBox.Yes:
                # الحصول على القيم الافتراضية
                default_prices = self.prices_manager.get_default_prices()

                # تطبيق القيم الافتراضية على الحقول
                self.apply_defaults_to_fields(default_prices)

                QMessageBox.information(self, "تمت الاستعادة",
                                      "تم استعادة جميع القيم الافتراضية بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استعادة القيم الافتراضية: {str(e)}")

    def apply_defaults_to_fields(self, default_prices):
        """تطبيق القيم الافتراضية على الحقول"""
        # تطبيق أسماء المعالجات الافتراضية
        for field_key, name_field in self.treatment_name_fields.items():
            if '_' in field_key:
                category, treatment_name = field_key.split('_', 1)
                name_field.setText(treatment_name)

        # تطبيق الأسعار الافتراضية
        for field_key, price_field in self.price_spinboxes.items():
            if field_key in default_prices:
                price_field.setValue(default_prices[field_key])





























