#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حذف خيار "منحنية بشدة" من مجموعة اللبية
Test removing "منحنية بشدة" option from Endodontic group
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class RemoveSeverelyCurvedOptionTestWindow(QMainWindow):
    """نافذة اختبار حذف خيار منحنية بشدة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار حذف خيار منحنية بشدة من مجموعة اللبية")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار حذف خيار منحنية بشدة من مجموعة اللبية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحديث
        info_label = QLabel("""
        🎯 حذف خيار "منحنية بشدة" من مجموعة اللبية:
        
        ❌ إزالة الخيار من القائمة:
        • تم حذف "منحنية بشدة" من قائمة options في دالة create_endodontic_group()
        • القائمة الجديدة تحتوي على 7 خيارات بدلاً من 8
        • القائمة الجديدة: ["Vital", "Necrotic", "إعادة معالجة", "متكلسة", "C shape", "ذروة مفتوحة", "أداة مكسورة"]
        
        ✅ الحفاظ على التنسيق:
        • الاحتفاظ بجميع التحسينات الحالية للمربعات:
          - حجم الخط: font-size: 14px
          - العرض الأدنى: min-width: 160px
          - الارتفاع الأدنى: min-height: 24px
          - الحشو: padding: 4px
        • الاحتفاظ بالمحاذاة اليسرى (text-align: left) والتخطيط العمودي
        • جميع الخيارات المتبقية تظهر بنفس التنسيق الموحد
        
        🔧 الاعتبارات التقنية المحققة:
        • تحديث مفاتيح القاموس في self.checkboxes بشكل صحيح
        • الحفاظ على جميع الوظائف والتفاعل الحالي:
          - stateChanged.connect
          - clear_all_options
          - get_selected_options
        • عدم التأثير على المجموعات الأخرى (الترميمية، التيجان، الجراحة)
        • الحفاظ على التخطيط الشبكي 2x2 للمجموعات الأربعة
        
        📊 مقارنة قبل وبعد الحذف:
        • قبل الحذف: 8 خيارات في مجموعة اللبية
        • بعد الحذف: 7 خيارات في مجموعة اللبية
        • الخيار المحذوف: "منحنية بشدة"
        • الخيارات المتبقية: جميعها تعمل بنفس الكفاءة والتنسيق
        
        ✅ النتائج المحققة:
        • إزالة خيار "منحنية بشدة" بالكامل من مجموعة اللبية
        • الحفاظ على جميع التحسينات والوظائف الحالية
        • مجموعة اللبية تحتوي الآن على 7 خيارات فقط
        • جميع الخيارات المتبقية تعمل بشكل صحيح
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة خيارات المعالجة مع الحذف
        options_title = QLabel("⚙️ خيارات المعالجة (بدون منحنية بشدة)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setAlignment(Qt.AlignCenter)
        self.selected_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                padding: 12px;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد جميع خيارات اللبية
        endodontic_btn = QPushButton("تحديد جميع خيارات اللبية (7 خيارات)")
        endodontic_btn.clicked.connect(self.select_all_endodontic)
        endodontic_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        buttons_layout.addWidget(endodontic_btn)
        
        # زر عد خيارات اللبية
        count_btn = QPushButton("عد خيارات اللبية")
        count_btn.clicked.connect(self.count_endodontic_options)
        count_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(count_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار حذف خيار منحنية بشدة:
        
        🔍 ما يجب ملاحظته:
        • عدم وجود خيار "منحنية بشدة" في مجموعة اللبية
        • مجموعة اللبية تحتوي على 7 خيارات فقط:
          1. Vital
          2. Necrotic
          3. إعادة معالجة
          4. متكلسة
          5. C shape
          6. ذروة مفتوحة
          7. أداة مكسورة
        • جميع الخيارات المتبقية تظهر بالتنسيق المحسن
        • المجموعات الأخرى لم تتأثر بالحذف
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على زر "تحديد جميع خيارات اللبية" للتأكد من وجود 7 خيارات فقط
        • انقر على زر "عد خيارات اللبية" لعرض العدد الدقيق
        • تحقق بصرياً من عدم وجود خيار "منحنية بشدة"
        • اختبر التفاعل مع جميع الخيارات المتبقية
        • تحقق من أن المجموعات الأخرى لم تتأثر
        
        ✅ النتائج المتوقعة:
        • عدم وجود خيار "منحنية بشدة" في مجموعة اللبية
        • وجود 7 خيارات فقط في مجموعة اللبية
        • عمل جميع الخيارات المتبقية بشكل صحيح
        • الحفاظ على التنسيق والتحسينات الحالية
        • عدم تأثر المجموعات الأخرى
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم حذف خيار منحنية بشدة من مجموعة اللبية بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_all_endodontic(self):
        """تحديد جميع خيارات مجموعة اللبية"""
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد جميع خيارات اللبية
        checkboxes = self.treatment_options.checkboxes
        endodontic_options = [key for key in checkboxes.keys() if key.startswith("endodontic_")]
        
        for option_key in endodontic_options:
            checkboxes[option_key].setChecked(True)
        
    def count_endodontic_options(self):
        """عد خيارات مجموعة اللبية"""
        checkboxes = self.treatment_options.checkboxes
        endodontic_options = [key for key in checkboxes.keys() if key.startswith("endodontic_")]
        
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(
            self, 
            "عدد خيارات اللبية", 
            f"عدد خيارات مجموعة اللبية: {len(endodontic_options)} خيارات\n\n"
            f"الخيارات هي:\n" + 
            "\n".join([f"{i+1}. {key.replace('endodontic_', '')}" for i, key in enumerate(endodontic_options)])
        )
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = RemoveSeverelyCurvedOptionTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
