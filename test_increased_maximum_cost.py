#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تعديل الحد الأقصى لحقول الكلفة إلى 999,999,999
Test increasing maximum cost fields to 999,999,999
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, TreatmentSessionDialog

class IncreasedMaximumCostTestWindow(QMainWindow):
    """نافذة اختبار الحد الأقصى المحدث لحقول الكلفة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تعديل الحد الأقصى لحقول الكلفة إلى 999,999,999")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تعديل الحد الأقصى لحقول الكلفة إلى 999,999,999")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 تعديل الحد الأقصى لجميع حقول الكلفة إلى 999,999,999:
        
        ✅ تعديل حقل الكلفة في خطة المعالجة:
        • تغيير الحد الأقصى من setMaximum(999999) إلى setMaximum(999999999)
        • زيادة الحد الأقصى من 999,999 إلى 999,999,999 ليرة سورية
        • الاحتفاظ بجميع الخصائص الأخرى:
          - نوع الحقل: QSpinBox (أرقام صحيحة)
          - لاحقة العملة: "ل.س"
          - إزالة أسهم التحكم: NoButtons
          - العرض الثابت: 120px
          - المحاذاة المركزية: AlignCenter
        
        ✅ تعديل حقل الدفعة في نافذة جلسات المعالجة:
        • تغيير الحد الأقصى من setMaximum(999999) إلى setMaximum(999999999)
        • زيادة الحد الأقصى من 999,999 إلى 999,999,999 ليرة سورية
        • الاحتفاظ بجميع الخصائص الأخرى:
          - نوع الحقل: QSpinBox (أرقام صحيحة)
          - لاحقة العملة: "ليرة سورية"
          - الاتصال بدالة الحساب: valueChanged.connect(calculate_remaining)
        
        🔧 التحسينات التقنية المطبقة:
        • الحد الأقصى الجديد: 999,999,999 (تسعة أرقام)
        • الاحتفاظ بنوع البيانات int للأرقام الصحيحة
        • عرض الحقول كافي لإظهار الأرقام الطويلة
        • الحسابات تعمل بشكل صحيح مع القيم الأكبر
        • عدم التأثير على أي خصائص أخرى للحقول
        
        📊 المقارنة قبل وبعد التعديل:
        • الحد الأقصى السابق: 999,999 ليرة سورية (ستة أرقام)
        • الحد الأقصى الجديد: 999,999,999 ليرة سورية (تسعة أرقام)
        • الزيادة: 1000 ضعف القيمة السابقة
        • التطبيق: جميع حقول الكلفة في البرنامج
        
        ✅ النتائج المحققة:
        • إمكانية إدخال قيم مالية أكبر بكثير
        • مرونة أكبر في التعامل مع الكلف العالية
        • الحفاظ على جميع الوظائف الحالية
        • تناسق في جميع حقول الكلفة
        • دعم أفضل للعملات ذات القيم العالية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع الحد الأقصى المحدث
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع حد أقصى 999,999,999)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار قيمة متوسطة
        test_medium_btn = QPushButton("اختبار كلفة 5,000,000 ل.س")
        test_medium_btn.clicked.connect(self.test_medium_cost)
        test_medium_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_medium_btn)
        
        # زر اختبار قيمة عالية
        test_high_btn = QPushButton("اختبار كلفة 50,000,000 ل.س")
        test_high_btn.clicked.connect(self.test_high_cost)
        test_high_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_high_btn)
        
        # زر اختبار الحد الأقصى
        test_max_btn = QPushButton("اختبار الحد الأقصى 999,999,999 ل.س")
        test_max_btn.clicked.connect(self.test_maximum_cost)
        test_max_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(test_max_btn)
        
        # زر اختبار نافذة الجلسات
        test_session_btn = QPushButton("اختبار نافذة الجلسات")
        test_session_btn.clicked.connect(self.test_session_dialog)
        test_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_session_btn)
        
        # زر مسح القيم
        clear_btn = QPushButton("مسح القيم")
        clear_btn.clicked.connect(self.clear_values)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار الحد الأقصى المحدث:
        
        🔍 ما يجب ملاحظته:
        • حقل الكلفة في خطة المعالجة يقبل قيم حتى 999,999,999 ليرة سورية
        • حقل الدفعة في نافذة الجلسات يقبل قيم حتى 999,999,999 ليرة سورية
        • عرض القيم الطويلة بشكل صحيح في الحقول
        • الحسابات تعمل بشكل صحيح مع القيم الكبيرة
        • جميع الخصائص الأخرى محفوظة (لاحقة العملة، المحاذاة، إلخ)
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على "اختبار كلفة 5,000,000 ل.س" لتجربة قيمة متوسطة
        • انقر على "اختبار كلفة 50,000,000 ل.س" لتجربة قيمة عالية
        • انقر على "اختبار الحد الأقصى 999,999,999 ل.س" لتجربة أعلى قيمة
        • انقر على "اختبار نافذة الجلسات" لفتح نافذة الجلسات واختبار حقل الدفعة
        • جرب إدخال قيم مختلفة يدوياً في حقول الكلفة
        • تحقق من أن الحقول لا تقبل قيم أكبر من الحد الأقصى الجديد
        
        ✅ النتائج المتوقعة:
        • قبول قيم مالية كبيرة حتى 999,999,999 ليرة سورية
        • عرض القيم الطويلة بشكل صحيح ومقروء
        • عمل الحسابات بشكل صحيح مع القيم الكبيرة
        • الحفاظ على جميع الوظائف والخصائص الأخرى
        • تناسق في جميع حقول الكلفة في البرنامج
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تعديل الحد الأقصى لجميع حقول الكلفة إلى 999,999,999 بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_medium_cost(self):
        """اختبار قيمة كلفة متوسطة"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(5000000)
        
    def test_high_cost(self):
        """اختبار قيمة كلفة عالية"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(50000000)
        
    def test_maximum_cost(self):
        """اختبار الحد الأقصى للكلفة"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(999999999)
        
    def test_session_dialog(self):
        """اختبار نافذة جلسة المعالجة مع كلفة عالية"""
        # إنشاء نافذة جلسة معالجة مع كلفة عالية
        dialog = TreatmentSessionDialog(
            plan_number="001",
            cost=100000000,  # كلفة عالية للاختبار
            parent=self
        )
        dialog.exec_()
        
    def clear_values(self):
        """مسح جميع القيم"""
        self.dental_tab.treatment_plan.cost_spinbox.setValue(0)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = IncreasedMaximumCostTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
