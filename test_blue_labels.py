#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تغيير لون التسميات إلى الأزرق في حاوية خطة المعالجة السنية
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الويدجت
from ui.tabs.dental_treatments_tab import TreatmentPlanWidget

class TestBlueLabelsWindow(QMainWindow):
    """نافذة اختبار التسميات الزرقاء"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار التسميات الزرقاء في حاوية خطة المعالجة السنية")
        self.setGeometry(200, 200, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار التسميات الزرقاء في حاوية خطة المعالجة السنية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف التغيير
        description = QLabel("""
🎨 التغيير المطبق:

✅ تم تغيير لون النص للتسميات الخمس من الرمادي إلى الأزرق:

1️⃣ تسمية "رقم السن" - اللون: #007bff (أزرق)
2️⃣ تسمية "المعالجة السنية" - اللون: #007bff (أزرق)  
3️⃣ تسمية "الكلفة" - اللون: #007bff (أزرق)
4️⃣ تسمية "التاريخ" - اللون: #007bff (أزرق)
5️⃣ تسمية "الحالة" - اللون: #007bff (أزرق)

🔧 التفاصيل التقنية:
• تم تحديث labels_straight_border_style
• تم تحديث تنسيق تسمية "الحالة" منفصلاً
• تم الحفاظ على باقي خصائص التنسيق (الحدود، الخلفية، الخط)
• النص الأزرق واضح على الخلفية الرمادية الفاتحة (#f8f9fa)

🎯 النتيجة المتوقعة:
جميع التسميات الخمس يجب أن تظهر بنص أزرق واضح ومقروء
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #007bff;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # إضافة ويدجت خطة المعالجة للاختبار
        test_widget_label = QLabel("🧪 ويدجت خطة المعالجة السنية مع التسميات الزرقاء:")
        test_widget_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding: 10px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(test_widget_label)
        
        # إضافة الويدجت
        self.treatment_plan_widget = TreatmentPlanWidget()
        layout.addWidget(self.treatment_plan_widget)
        
        # زر لطباعة البيانات
        test_btn = QPushButton("📊 طباعة بيانات الخطة (اختبار الوظائف)")
        test_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
                margin-top: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
        """)
        test_btn.clicked.connect(self.print_plan_data)
        layout.addWidget(test_btn)
        
        # منطقة الحالة
        self.status_label = QLabel("✅ تم تطبيق التسميات الزرقاء - تحقق من الألوان أعلاه")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #28a745;
                padding: 10px;
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                border-radius: 5px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.status_label)
        
    def print_plan_data(self):
        """طباعة بيانات الخطة لاختبار الوظائف"""
        try:
            data = self.treatment_plan_widget.get_plan_data()
            print("=" * 50)
            print("📊 بيانات خطة المعالجة:")
            for key, value in data.items():
                print(f"   {key}: {value}")
            print("=" * 50)
            
            status = data.get('status', 'غير محدد')
            self.status_label.setText(f"✅ تم طباعة البيانات - الحالة: {status}")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #007bff;
                    padding: 10px;
                    background-color: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 5px;
                    margin-top: 20px;
                }
            """)
            
        except Exception as e:
            print(f"❌ خطأ في طباعة البيانات: {e}")
            self.status_label.setText(f"❌ خطأ في طباعة البيانات: {e}")
            self.status_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #dc3545;
                    padding: 10px;
                    background-color: #f8d7da;
                    border: 1px solid #f5c6cb;
                    border-radius: 5px;
                    margin-top: 20px;
                }
            """)

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestBlueLabelsWindow()
    window.show()
    
    print("تم تشغيل اختبار التسميات الزرقاء")
    print("تحقق من أن جميع التسميات الخمس تظهر بلون أزرق (#007bff)")
    print("التسميات: رقم السن، المعالجة السنية، الكلفة، التاريخ، الحالة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
