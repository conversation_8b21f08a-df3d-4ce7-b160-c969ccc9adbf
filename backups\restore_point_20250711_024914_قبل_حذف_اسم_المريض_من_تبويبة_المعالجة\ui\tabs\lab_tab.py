import sys
from datetime import datetime
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea,
                             QGridLayout, QDoubleSpinBox, QFileDialog)
from PyQt5.QtPrintSupport import QPrintDialog, QPrinter, QPrintPreviewDialog
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate, QDateTime, QBuffer, QIODevice
from PyQt5.QtGui import QIcon, QFont, QPainter, QPen, QColor, QPixmap, QTextDocument

class LabOrderForm(QWidget):
    """نموذج طلب مخبري"""
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.patient_id = None
        self.patients = []
        self.labs = []
        self.work_types = []
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # حقول الإدخال
        self.patient_combo = QComboBox()
        
        self.lab_combo = QComboBox()
        self.lab_combo.currentIndexChanged.connect(self.on_lab_changed)
        
        self.work_type_combo = QComboBox()
        self.work_type_combo.currentIndexChanged.connect(self.on_work_type_changed)
        
        self.pieces_count_input = QSpinBox()
        self.pieces_count_input.setRange(1, 100)
        self.pieces_count_input.setValue(1)
        self.pieces_count_input.valueChanged.connect(self.calculate_total_price)
        
        self.price_input = QDoubleSpinBox()
        self.price_input.setRange(0, 1000000)
        self.price_input.setSingleStep(100)
        self.price_input.setSuffix(" ل.س")
        self.price_input.valueChanged.connect(self.calculate_total_price)
        
        self.total_price_input = QDoubleSpinBox()
        self.total_price_input.setRange(0, 1000000)
        self.total_price_input.setSingleStep(100)
        self.total_price_input.setSuffix(" ل.س")
        self.total_price_input.setReadOnly(True)
        
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات")
        self.notes_input.setMaximumHeight(80)
        
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("المريض:", self.patient_combo)
        form_layout.addRow("المخبر:", self.lab_combo)
        form_layout.addRow("نوع العمل:", self.work_type_combo)
        form_layout.addRow("عدد القطع:", self.pieces_count_input)
        form_layout.addRow("السعر (للقطعة):", self.price_input)
        form_layout.addRow("السعر الإجمالي:", self.total_price_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        form_layout.addRow("التاريخ:", self.date_input)
        
        # إضافة النموذج إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        
        # أزرار الحفظ والإلغاء والطباعة
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 10, 0, 0)
        
        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setProperty("class", "btn-success")
        self.save_button.setMinimumWidth(100)

        self.print_button = QPushButton("🖨️ طباعة")
        self.print_button.setProperty("class", "btn-primary")
        self.print_button.setMinimumWidth(100)
        self.print_button.setEnabled(False)  # تعطيل زر الطباعة حتى يتم حفظ الطلب

        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setProperty("class", "btn-secondary")
        self.cancel_button.setMinimumWidth(100)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.print_button)
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        # تحميل المرضى
        self.patients = self.db_handler.get_all_patients()
        self.patient_combo.clear()
        
        for patient in self.patients:
            self.patient_combo.addItem(patient['name'], patient['id'])
        
        # تحميل المخابر
        self.labs = self.db_handler.get_labs()
        self.lab_combo.clear()
        
        for lab in self.labs:
            self.lab_combo.addItem(lab['name'], lab['id'])
        
        # تحميل أنواع العمل
        self.load_work_types()
    
    def load_work_types(self):
        """تحميل أنواع العمل بناءً على المخبر المحدد"""
        lab_id = self.lab_combo.currentData()
        
        if lab_id:
            self.work_types = self.db_handler.get_lab_work_types()
            self.work_type_combo.clear()
            
            for work_type in self.work_types:
                self.work_type_combo.addItem(work_type['name'], work_type['id'])
    
    def on_lab_changed(self):
        """معالجة تغيير المخبر"""
        self.load_work_types()
    
    def on_work_type_changed(self):
        """معالجة تغيير نوع العمل"""
        work_type_id = self.work_type_combo.currentData()
        
        if work_type_id:
            # البحث عن نوع العمل في القائمة
            for work_type in self.work_types:
                if work_type['id'] == work_type_id:
                    # تعيين السعر الافتراضي
                    self.price_input.setValue(work_type.get('default_cost', 0))
                    break
        
        self.calculate_total_price()
    
    def calculate_total_price(self):
        """حساب السعر الإجمالي"""
        pieces_count = self.pieces_count_input.value()
        price = self.price_input.value()
        total_price = pieces_count * price
        
        self.total_price_input.setValue(total_price)
    
    def set_patient(self, patient_id):
        """تعيين المريض"""
        if patient_id:
            # البحث عن المريض في القائمة
            for i in range(self.patient_combo.count()):
                if self.patient_combo.itemData(i) == patient_id:
                    self.patient_combo.setCurrentIndex(i)
                    break
    
    def clear_form(self):
        """مسح النموذج"""
        self.patient_combo.setCurrentIndex(0) if self.patient_combo.count() > 0 else None
        self.lab_combo.setCurrentIndex(0) if self.lab_combo.count() > 0 else None
        self.work_type_combo.setCurrentIndex(0) if self.work_type_combo.count() > 0 else None
        self.pieces_count_input.setValue(1)
        self.price_input.setValue(0)
        self.total_price_input.setValue(0)
        self.notes_input.clear()
        self.date_input.setDate(QDate.currentDate())
        self.print_button.setEnabled(False)
    
    def get_lab_order_data(self):
        """الحصول على بيانات الطلب المخبري"""
        return {
            'patient_id': self.patient_combo.currentData(),
            'patient_name': self.patient_combo.currentText(),
            'lab_id': self.lab_combo.currentData(),
            'lab_name': self.lab_combo.currentText(),
            'work_type_id': self.work_type_combo.currentData(),
            'work_type_name': self.work_type_combo.currentText(),
            'pieces_count': self.pieces_count_input.value(),
            'price': self.price_input.value(),
            'total_price': self.total_price_input.value(),
            'notes': self.notes_input.toPlainText().strip(),
            'date': self.date_input.date().toString("yyyy-MM-dd")
        }

class LabOrderDialog(QDialog):
    """نافذة حوار إضافة/تعديل طلب مخبري"""
    def __init__(self, db_handler, parent=None, order_data=None, patient_id=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.order_data = order_data
        self.patient_id = patient_id
        self.order_id = None
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # عنوان النافذة
        self.setWindowTitle("إضافة طلب مخبري جديد" if not self.order_data else "تعديل الطلب المخبري")
        self.setMinimumWidth(500)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج الطلب المخبري
        self.order_form = LabOrderForm(self.db_handler)
        
        if self.patient_id:
            self.order_form.set_patient(self.patient_id)
        
        main_layout.addWidget(self.order_form)
        
        # تعيين بيانات الطلب إذا كانت متوفرة
        if self.order_data:
            self.set_order_data()
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # ربط أزرار النموذج
        self.order_form.save_button.clicked.connect(self.accept)
        self.order_form.cancel_button.clicked.connect(self.reject)
        self.order_form.print_button.clicked.connect(self.print_order)
    
    def set_order_data(self):
        """تعيين بيانات الطلب في النموذج"""
        # تعيين المريض
        patient_id = self.order_data.get('patient_id')
        self.order_form.set_patient(patient_id)
        
        # تعيين المخبر
        lab_id = self.order_data.get('lab_id')
        for i in range(self.order_form.lab_combo.count()):
            if self.order_form.lab_combo.itemData(i) == lab_id:
                self.order_form.lab_combo.setCurrentIndex(i)
                break
        
        # تعيين نوع العمل
        work_type_id = self.order_data.get('work_type_id')
        for i in range(self.order_form.work_type_combo.count()):
            if self.order_form.work_type_combo.itemData(i) == work_type_id:
                self.order_form.work_type_combo.setCurrentIndex(i)
                break
        
        # تعيين باقي الحقول
        self.order_form.pieces_count_input.setValue(self.order_data.get('pieces_count', 1))
        self.order_form.price_input.setValue(self.order_data.get('price', 0))
        self.order_form.notes_input.setText(self.order_data.get('notes', ''))
        
        # تعيين التاريخ
        order_date = self.order_data.get('date', '')
        if order_date:
            self.order_form.date_input.setDate(QDate.fromString(order_date, "yyyy-MM-dd"))
        
        # تمكين زر الطباعة
        self.order_form.print_button.setEnabled(True)
        self.order_id = self.order_data.get('id')
    
    def get_order_data(self):
        """الحصول على بيانات الطلب من النموذج"""
        return self.order_form.get_lab_order_data()
    
    def print_order(self):
        """طباعة الطلب المخبري"""
        if not self.order_id:
            QMessageBox.warning(self, "تنبيه", "يرجى حفظ الطلب أولاً قبل الطباعة")
            return
        
        # إنشاء مستند HTML للطباعة
        order_data = self.get_order_data()
        html = self.generate_order_html(order_data)
        
        # إنشاء مستند نصي
        document = QTextDocument()
        document.setHtml(html)
        
        # إعداد الطابعة
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A5)
        printer.setOrientation(QPrinter.Portrait)
        
        # عرض نافذة معاينة الطباعة
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(lambda p: document.print_(p))
        preview.exec_()
    
    def generate_order_html(self, order_data):
        """إنشاء مستند HTML للطلب المخبري"""
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طلب مخبري</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 10px; }}
                .info {{ margin-bottom: 20px; }}
                .info-row {{ display: flex; margin-bottom: 5px; }}
                .info-label {{ font-weight: bold; width: 120px; }}
                .info-value {{ flex: 1; }}
                .table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .table th {{ background-color: #f2f2f2; }}
                .footer {{ margin-top: 30px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">طلب مخبري</div>
                <div>العيادة السنية</div>
            </div>
            
            <div class="info">
                <div class="info-row">
                    <div class="info-label">رقم الطلب:</div>
                    <div class="info-value">{self.order_id}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">التاريخ:</div>
                    <div class="info-value">{order_data['date']}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">المريض:</div>
                    <div class="info-value">{order_data['patient_name']}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">المخبر:</div>
                    <div class="info-value">{order_data['lab_name']}</div>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>نوع العمل</th>
                        <th>عدد القطع</th>
                        <th>السعر (للقطعة)</th>
                        <th>السعر الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{order_data['work_type_name']}</td>
                        <td>{order_data['pieces_count']}</td>
                        <td>{order_data['price']} ل.س</td>
                        <td>{order_data['total_price']} ل.س</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="info">
                <div class="info-row">
                    <div class="info-label">ملاحظات:</div>
                    <div class="info-value">{order_data['notes']}</div>
                </div>
            </div>
            
            <div class="footer">
                <p>توقيع الطبيب: ________________</p>
            </div>
        </body>
        </html>
        """
        
        return html

class LabTab(QWidget):
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.current_patient_id = None
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط البحث
        search_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث عن طلب مخبري (اسم المريض، المخبر، نوع العمل)")
        self.search_input.textChanged.connect(self.search_orders)
        
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_orders)
        
        search_layout.addWidget(self.search_input)
        search_layout.addWidget(self.search_button)
        
        main_layout.addLayout(search_layout)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        self.add_order_button = QPushButton("إضافة طلب مخبري جديد")
        self.add_order_button.clicked.connect(self.add_lab_order)
        
        self.edit_order_button = QPushButton("تعديل الطلب المخبري")
        self.edit_order_button.clicked.connect(self.edit_lab_order)
        self.edit_order_button.setEnabled(False)
        
        self.delete_order_button = QPushButton("حذف الطلب المخبري")
        self.delete_order_button.clicked.connect(self.delete_lab_order)
        self.delete_order_button.setEnabled(False)
        
        self.print_order_button = QPushButton("طباعة الطلب المخبري")
        self.print_order_button.clicked.connect(self.print_lab_order)
        self.print_order_button.setEnabled(False)
        
        buttons_layout.addWidget(self.add_order_button)
        buttons_layout.addWidget(self.edit_order_button)
        buttons_layout.addWidget(self.delete_order_button)
        buttons_layout.addWidget(self.print_order_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # جدول الطلبات المخبرية
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels(["المريض", "المخبر", "نوع العمل", "عدد القطع", "السعر الإجمالي", "التاريخ", "ملاحظات"])
        self.orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.orders_table.verticalHeader().setVisible(False)
        self.orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.orders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.orders_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.orders_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        main_layout.addWidget(self.orders_table)
        
        # تحميل الطلبات المخبرية
        self.load_lab_orders()
    
    def load_lab_orders(self):
        """تحميل الطلبات المخبرية"""
        orders = self.db_handler.get_lab_orders()
        self.update_orders_table(orders)
    
    def update_orders_table(self, orders):
        """تحديث جدول الطلبات المخبرية"""
        self.orders_table.setRowCount(0)
        
        for row, order in enumerate(orders):
            self.orders_table.insertRow(row)
            
            # المريض
            patient_item = QTableWidgetItem(order.get('patient_name', ''))
            patient_item.setData(Qt.UserRole, order['id'])  # تخزين معرف الطلب
            self.orders_table.setItem(row, 0, patient_item)
            
            # المخبر
            lab_item = QTableWidgetItem(order.get('lab_name', ''))
            self.orders_table.setItem(row, 1, lab_item)
            
            # نوع العمل
            work_type_item = QTableWidgetItem(order.get('work_type_name', ''))
            self.orders_table.setItem(row, 2, work_type_item)
            
            # عدد القطع
            pieces_count_item = QTableWidgetItem(str(order.get('pieces_count', 0)))
            self.orders_table.setItem(row, 3, pieces_count_item)
            
            # السعر الإجمالي
            total_price = order.get('total_price', 0)
            total_price_item = QTableWidgetItem(f"{total_price} ل.س")
            self.orders_table.setItem(row, 4, total_price_item)
            
            # التاريخ
            date_item = QTableWidgetItem(order.get('date', ''))
            self.orders_table.setItem(row, 5, date_item)
            
            # ملاحظات
            notes_item = QTableWidgetItem(order.get('notes', ''))
            self.orders_table.setItem(row, 6, notes_item)
    
    def on_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""
        selected_items = self.orders_table.selectedItems()
        
        if selected_items:
            self.edit_order_button.setEnabled(True)
            self.delete_order_button.setEnabled(True)
            self.print_order_button.setEnabled(True)
        else:
            self.edit_order_button.setEnabled(False)
            self.delete_order_button.setEnabled(False)
            self.print_order_button.setEnabled(False)
    
    def search_orders(self):
        """البحث عن الطلبات المخبرية"""
        search_text = self.search_input.text().strip()
        
        # البحث في جميع الطلبات وتصفيتها محلياً
        orders = self.db_handler.get_lab_orders()

        if search_text:
            # تصفية النتائج محلياً بناءً على اسم المريض أو المخبر
            filtered_orders = []
            for order in orders:
                if (search_text.lower() in order.get('patient_name', '').lower() or
                    search_text.lower() in order.get('lab_name', '').lower() or
                    search_text.lower() in order.get('work_type', '').lower()):
                    filtered_orders.append(order)
            orders = filtered_orders
        
        self.update_orders_table(orders)
    
    def add_lab_order(self):
        """إضافة طلب مخبري جديد"""
        dialog = LabOrderDialog(self.db_handler, self, patient_id=self.current_patient_id)
        
        if dialog.exec_() == QDialog.Accepted:
            order_data = dialog.get_order_data()
            
            # التحقق من صحة البيانات
            if not order_data['patient_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المريض")
                return
            
            if not order_data['lab_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المخبر")
                return
            
            if not order_data['work_type_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار نوع العمل")
                return
            
            # إضافة الطلب المخبري إلى قاعدة البيانات
            order_id = self.db_handler.add_lab_order(
                order_data['patient_id'],
                order_data['lab_id'],
                order_data['work_type_id'],
                order_data['pieces_count'],
                order_data['price'],
                order_data['total_price'],
                order_data['notes'],
                order_data['date']
            )
            
            if order_id:
                QMessageBox.information(self, "نجاح", "تمت إضافة الطلب المخبري بنجاح")
                self.load_lab_orders()
                
                # تمكين زر الطباعة وطباعة الطلب
                dialog.order_id = order_id
                dialog.order_form.print_button.setEnabled(True)
                
                reply = QMessageBox.question(
                    self,
                    "طباعة الطلب",
                    "هل ترغب في طباعة الطلب المخبري الآن؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    dialog.print_order()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة الطلب المخبري")
    
    def edit_lab_order(self):
        """تعديل طلب مخبري"""
        selected_items = self.orders_table.selectedItems()
        
        if not selected_items:
            return
        
        order_id = selected_items[0].data(Qt.UserRole)
        order_data = self.db_handler.get_lab_order(order_id)
        
        if not order_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب المخبري")
            return
        
        dialog = LabOrderDialog(self.db_handler, self, order_data)
        
        if dialog.exec_() == QDialog.Accepted:
            updated_data = dialog.get_order_data()
            
            # التحقق من صحة البيانات
            if not updated_data['patient_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المريض")
                return
            
            if not updated_data['lab_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المخبر")
                return
            
            if not updated_data['work_type_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار نوع العمل")
                return
            
            # تحديث الطلب المخبري في قاعدة البيانات
            success = self.db_handler.update_lab_order(
                order_id,
                updated_data['patient_id'],
                updated_data['lab_id'],
                updated_data['work_type_id'],
                updated_data['pieces_count'],
                updated_data['price'],
                updated_data['total_price'],
                updated_data['notes'],
                updated_data['date']
            )
            
            if success:
                QMessageBox.information(self, "نجاح", "تم تحديث الطلب المخبري بنجاح")
                self.load_lab_orders()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحديث الطلب المخبري")
    
    def delete_lab_order(self):
        """حذف طلب مخبري"""
        selected_items = self.orders_table.selectedItems()
        
        if not selected_items:
            return
        
        order_id = selected_items[0].data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من رغبتك في حذف هذا الطلب المخبري؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.db_handler.delete_lab_order(order_id)
            
            if success:
                QMessageBox.information(self, "نجاح", "تم حذف الطلب المخبري بنجاح")
                self.load_lab_orders()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حذف الطلب المخبري")
    
    def print_lab_order(self):
        """طباعة طلب مخبري"""
        selected_items = self.orders_table.selectedItems()
        
        if not selected_items:
            return
        
        order_id = selected_items[0].data(Qt.UserRole)
        order_data = self.db_handler.get_lab_order(order_id)
        
        if not order_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الطلب المخبري")
            return
        
        # إنشاء نافذة حوار الطلب المخبري لاستخدام وظيفة الطباعة
        dialog = LabOrderDialog(self.db_handler, self, order_data)
        dialog.order_id = order_id
        dialog.print_order()
    
    def set_patient(self, patient_id):
        """تعيين المريض الحالي"""
        self.current_patient_id = patient_id
        
        if patient_id:
            # تحميل الطلبات المخبرية للمريض
            orders = self.db_handler.get_lab_orders(patient_id=patient_id)
            self.update_orders_table(orders)
        else:
            # تحميل جميع الطلبات المخبرية
            self.load_lab_orders()