#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحديث خيارات المعالجة
Test updated treatment options
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class UpdatedTreatmentOptionsTestWindow(QMainWindow):
    """نافذة اختبار تحديث خيارات المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تحديث خيارات المعالجة")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تحديث خيارات المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحديثات
        info_label = QLabel("""
        🎯 التحديثات المطبقة على خيارات المعالجة:
        
        🔧 تعديل مجموعة الجراحة (Surgery):
        • استبدال خيار "تجميل" بخيار "تضحيك"
        • الاحتفاظ بباقي الخيارات الستة الأخرى:
          - قلع بسيط
          - قلع جراحي
          - منحصرة
          - منطمرة
          - تطويل تاج
          - قطع ذروة
        • الخيار الجديد "تضحيك" يحتفظ بنفس التنسيق والخصائص
        
        ➕ إضافة خيار جديد لمجموعة التيجان (Crowns):
        • إضافة خيار "إيماكس" إلى قائمة خيارات التيجان
        • القائمة الجديدة تصبح (7 خيارات):
          - خزف معدن
          - زيركون 4D
          - زيركون مغطى إيماكس
          - زيركون مغطى خزف
          - زيركون cutback
          - ستانلس
          - إيماكس (جديد)
        
        ✅ الاحتفاظ بجميع التحسينات الحالية:
        • حجم الخط الكبير (font-size: 14px)
        • العرض المحسن (min-width: 140px)
        • الارتفاع المحسن (min-height: 22px)
        • الحشو المحسن (padding: 3px)
        • محاذاة النص اليسرى (text-align: left)
        • إزالة الهوامش (margin: 0px)
        
        🔧 الاعتبارات التقنية:
        • تحديث مفاتيح القاموس في self.checkboxes بشكل صحيح
        • الحفاظ على جميع الوظائف والتفاعل (stateChanged.connect, clear_all_options, get_selected_options)
        • عدم التأثير على التخطيط الشبكي 2x2 للمجموعات
        • عدم التأثير على أي عناصر أخرى في التبويبة
        
        ✅ النتائج المحققة:
        • تحديث خيارات المعالجة لتعكس المصطلحات الطبية الصحيحة
        • إضافة خيار تاج إيماكس المهم
        • الحفاظ على جميع التحسينات والوظائف الحالية
        • إجمالي الخيارات الآن: 27 خيار (بدلاً من 26)
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة خيارات المعالجة المحدثة
        options_title = QLabel("⚙️ خيارات المعالجة المحدثة")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        self.treatment_options.options_changed.connect(self.on_options_changed)
        layout.addWidget(self.treatment_options)
        
        # معلومات الخيارات المحددة
        self.selected_info = QLabel("لم يتم تحديد أي خيارات")
        self.selected_info.setAlignment(Qt.AlignCenter)
        self.selected_info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #007bff;
                padding: 12px;
                background-color: #e7f3ff;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(self.selected_info)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر تحديد الخيارات الجديدة
        new_options_btn = QPushButton("تحديد الخيارات الجديدة")
        new_options_btn.clicked.connect(self.select_new_options)
        new_options_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        buttons_layout.addWidget(new_options_btn)
        
        # زر تحديد عشوائي
        random_btn = QPushButton("تحديد خيارات عشوائية")
        random_btn.clicked.connect(self.select_random_options)
        random_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(random_btn)
        
        # زر مسح الكل
        clear_btn = QPushButton("مسح جميع الخيارات")
        clear_btn.clicked.connect(self.treatment_options.clear_all_options)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار الخيارات المحدثة:
        
        🔍 ما يجب ملاحظته:
        • في مجموعة الجراحة: البحث عن خيار "تضحيك" بدلاً من "تجميل"
        • في مجموعة التيجان: البحث عن خيار "إيماكس" الجديد (الخيار السابع)
        • جميع الخيارات الأخرى تبقى كما هي
        • التنسيق والخصائص موحدة لجميع الخيارات
        • إجمالي الخيارات الآن 27 خيار (بدلاً من 26)
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على زر "تحديد الخيارات الجديدة" لتحديد "تضحيك" و "إيماكس"
        • انقر على مربعات اختيار مختلفة لاختبار الوظائف
        • استخدم زر "تحديد خيارات عشوائية" لرؤية التحديد العشوائي
        • استخدم زر "مسح جميع الخيارات" لإلغاء التحديد
        • تحقق من أن جميع الوظائف تعمل مع الخيارات الجديدة
        
        ✅ النتائج المتوقعة:
        • ظهور خيار "تضحيك" في مجموعة الجراحة
        • ظهور خيار "إيماكس" في مجموعة التيجان
        • عمل جميع الوظائف مع الخيارات الجديدة
        • الحفاظ على جميع التحسينات والتنسيق
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تحديث خيارات المعالجة بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def select_new_options(self):
        """تحديد الخيارات الجديدة للاختبار"""
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات الجديدة
        checkboxes = self.treatment_options.checkboxes
        
        # تحديد "تضحيك" من مجموعة الجراحة
        if "surgery_تضحيك" in checkboxes:
            checkboxes["surgery_تضحيك"].setChecked(True)
        
        # تحديد "إيماكس" من مجموعة التيجان
        if "crowns_إيماكس" in checkboxes:
            checkboxes["crowns_إيماكس"].setChecked(True)
        
    def select_random_options(self):
        """تحديد خيارات عشوائية للاختبار"""
        import random
        checkboxes = list(self.treatment_options.checkboxes.values())
        # تحديد 8-15 خيارات عشوائياً
        num_to_select = random.randint(8, 15)
        selected_checkboxes = random.sample(checkboxes, min(num_to_select, len(checkboxes)))
        
        # مسح جميع الخيارات أولاً
        self.treatment_options.clear_all_options()
        
        # تحديد الخيارات العشوائية
        for checkbox in selected_checkboxes:
            checkbox.setChecked(True)
        
    def on_options_changed(self):
        """عند تغيير الخيارات المحددة"""
        selected = self.treatment_options.get_selected_options()
        if selected:
            text = f"الخيارات المحددة ({len(selected)}): " + ", ".join(selected)
        else:
            text = "لم يتم تحديد أي خيارات"
        self.selected_info.setText(text)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UpdatedTreatmentOptionsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
