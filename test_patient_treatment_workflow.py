#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار آلية ربط المرضى بخطط المعالجة وجلسات العلاج
Test patient-treatment workflow integration
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

# محاكي قاعدة البيانات للاختبار
class MockDBHandler:
    """محاكي قاعدة البيانات للاختبار"""
    
    def __init__(self):
        self.treatment_plans = []
        self.treatment_sessions = []
        self.next_plan_id = 1
        self.next_session_id = 1
    
    def save_treatment_plan(self, plan_data):
        """حفظ خطة معالجة"""
        plan_id = self.next_plan_id
        plan_data['id'] = plan_id
        self.treatment_plans.append(plan_data)
        self.next_plan_id += 1
        print(f"✅ تم حفظ خطة المعالجة: {plan_id}")
        return plan_id
    
    def save_treatment_session(self, session_data):
        """حفظ جلسة معالجة"""
        session_id = self.next_session_id
        session_data['id'] = session_id
        self.treatment_sessions.append(session_data)
        self.next_session_id += 1
        print(f"✅ تم حفظ جلسة المعالجة: {session_id}")
        return session_id
    
    def get_treatment_plans_by_patient(self, patient_id):
        """الحصول على خطط المعالجة للمريض"""
        plans = [p for p in self.treatment_plans if p.get('patient_id') == patient_id]
        print(f"📋 خطط المعالجة للمريض {patient_id}: {len(plans)} خطة")
        return plans
    
    def get_treatment_sessions_by_plan(self, plan_id):
        """الحصول على جلسات المعالجة للخطة"""
        sessions = [s for s in self.treatment_sessions if s.get('plan_id') == plan_id]
        print(f"📅 جلسات المعالجة للخطة {plan_id}: {len(sessions)} جلسة")
        return sessions
    
    def get_treatment_plan_by_id(self, plan_id):
        """الحصول على خطة معالجة بالمعرف"""
        for plan in self.treatment_plans:
            if plan.get('id') == plan_id:
                return plan
        return None

class PatientTreatmentWorkflowTestWindow(QMainWindow):
    """نافذة اختبار آلية ربط المرضى بخطط المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار آلية ربط المرضى بخطط المعالجة وجلسات العلاج")
        self.setGeometry(50, 50, 1800, 1000)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار آلية ربط المرضى بخطط المعالجة وجلسات العلاج")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات آلية العمل
        info_label = QLabel("""
        🎯 آلية العمل المطبقة لربط المرضى بخطط المعالجة وجلسات العلاج:
        
        ✅ 1. آلية اختيار المريض:
        • متغير current_patient_id لتخزين معرف المريض المحدد
        • جميع عمليات الإدخال والحفظ ترتبط بالمريض المحدد
        • حقل عرض المريض في أعلى تبويبة المعالجة يعمل بشكل صحيح
        
        ✅ 2. التحقق من اختيار المريض عند الحفظ:
        • عند النقر على "حفظ خطة المعالجة"، التحقق من وجود مريض محدد
        • رسالة خطأ: "يرجى اختيار مريض من قائمة المرضى أولاً"
        • منع عملية الحفظ حتى يتم اختيار مريض
        
        ✅ 3. حفظ خطة المعالجة:
        • ربط الخطة بمعرف المريض المحدد (patient_id)
        • حفظ في جدول "خطط المعالجة السنية"
        • عرض رسالة تأكيد مع معرف الخطة
        • تحديث جدول خطط المعالجة في الواجهة
        • حفظ معرف الخطة (current_plan_id) للاستخدام في الجلسات
        
        ✅ 4. التحقق قبل إضافة جلسة معالجة:
        • التحقق من وجود مريض محدد
        • التحقق من وجود خطة معالجة محفوظة للمريض الحالي
        • رسالة: "يرجى حفظ خطة المعالجة أولاً أو اختيار خطة موجودة"
        • خيارات: "حفظ الخطة الحالية" أو "اختيار خطة موجودة"
        
        ✅ 5. ربط الجلسات بخطط المعالجة:
        • كل جلسة ترتبط بخطة معالجة محددة (plan_id)
        • عرض معرف خطة المعالجة في نافذة إضافة الجلسة
        • حفظ الجلسة مع ربطها بالخطة والمريض
        • تحديث جدول جلسات المعالجة فوراً
        
        ✅ 6. واجهة المستخدم المحسنة:
        • رسائل تحذيرية واضحة عند عدم استيفاء الشروط
        • تحديث فوري لجداول البيانات بعد الحفظ
        • الحفاظ على جميع التحسينات المطبقة مسبقاً
        
        📊 تدفق العمل المنطقي:
        
        1️⃣ اختيار المريض من تبويبة المرضى
        2️⃣ إدخال بيانات خطة المعالجة
        3️⃣ حفظ خطة المعالجة (مع ربطها بالمريض)
        4️⃣ إضافة جلسات المعالجة (مع ربطها بالخطة والمريض)
        5️⃣ تحديث الجداول والواجهة فوراً
        
        🔧 التحسينات التقنية:
        • إضافة current_plan_id لتتبع الخطة الحالية
        • تحديث TreatmentSessionDialog لدعم plan_id و patient_id
        • إضافة دوال refresh_treatment_plans_table() و refresh_treatment_sessions_table()
        • تحسين دالة get_session_data() لتشمل معرفات الربط
        • تحديث دالة clear_form() لإعادة تعيين معرف الخطة
        
        🎨 الفوائد المحققة:
        • تدفق عمل منطقي ومترابط
        • منع الأخطاء في إدخال البيانات
        • ربط صحيح بين المرضى وخطط المعالجة والجلسات
        • واجهة مستخدم واضحة مع رسائل توجيهية
        • تحديث فوري للبيانات والجداول
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 10px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع محاكي قاعدة البيانات
        treatment_title = QLabel("⚙️ تبويبة علاج الأسنان (مع آلية ربط المرضى)")
        treatment_title.setAlignment(Qt.AlignCenter)
        treatment_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(treatment_title)
        
        # إنشاء تبويبة علاج الأسنان مع محاكي قاعدة البيانات
        mock_db = MockDBHandler()
        self.dental_tab = DentalTreatmentsTab(mock_db)
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر محاكاة اختيار مريض
        select_patient_btn = QPushButton("محاكاة اختيار مريض (ID: 123)")
        select_patient_btn.clicked.connect(self.simulate_patient_selection)
        select_patient_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(select_patient_btn)
        
        # زر اختبار حفظ بدون مريض
        test_no_patient_btn = QPushButton("اختبار حفظ بدون مريض")
        test_no_patient_btn.clicked.connect(self.test_save_without_patient)
        test_no_patient_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(test_no_patient_btn)
        
        # زر اختبار إضافة جلسة بدون خطة
        test_no_plan_btn = QPushButton("اختبار جلسة بدون خطة")
        test_no_plan_btn.clicked.connect(self.test_session_without_plan)
        test_no_plan_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_no_plan_btn)
        
        # زر إعادة تعيين
        reset_btn = QPushButton("إعادة تعيين")
        reset_btn.clicked.connect(self.reset_test)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(reset_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار آلية ربط المرضى:
        
        🔍 سيناريوهات الاختبار:
        
        1️⃣ اختبار حفظ بدون مريض:
        • انقر على "اختبار حفظ بدون مريض"
        • حاول حفظ خطة معالجة
        • يجب أن تظهر رسالة: "يرجى اختيار مريض من قائمة المرضى أولاً"
        
        2️⃣ اختبار الحفظ الصحيح:
        • انقر على "محاكاة اختيار مريض"
        • أدخل بيانات خطة المعالجة
        • احفظ الخطة - يجب أن تنجح العملية
        
        3️⃣ اختبار جلسة بدون خطة:
        • انقر على "اختبار جلسة بدون خطة"
        • حاول إضافة جلسة معالجة
        • يجب أن تظهر خيارات: حفظ الخطة أو اختيار خطة موجودة
        
        4️⃣ اختبار الجلسة الصحيحة:
        • بعد حفظ خطة معالجة
        • انقر على "إضافة جلسة معالجة"
        • يجب أن تفتح النافذة مع معرف الخطة
        
        ✅ النتائج المتوقعة:
        • منع حفظ خطة بدون مريض محدد
        • ربط صحيح بين المرضى والخطط والجلسات
        • رسائل واضحة للمستخدم
        • تحديث فوري للجداول
        • تدفق عمل منطقي ومترابط
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تطبيق آلية ربط المرضى بخطط المعالجة وجلسات العلاج بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def simulate_patient_selection(self):
        """محاكاة اختيار مريض"""
        self.dental_tab.current_patient_id = 123
        self.dental_tab.patient_name_label.setText("أحمد محمد علي")
        print("✅ تم محاكاة اختيار المريض: أحمد محمد علي (ID: 123)")
        
    def test_save_without_patient(self):
        """اختبار حفظ بدون مريض"""
        self.dental_tab.current_patient_id = None
        self.dental_tab.patient_name_label.setText("لم يتم اختيار مريض")
        print("❌ تم إلغاء اختيار المريض - اختبر الآن حفظ خطة المعالجة")
        
    def test_session_without_plan(self):
        """اختبار إضافة جلسة بدون خطة"""
        self.dental_tab.current_patient_id = 123
        self.dental_tab.current_plan_id = None
        self.dental_tab.patient_name_label.setText("أحمد محمد علي")
        print("⚠️ مريض محدد ولكن لا توجد خطة محفوظة - اختبر الآن إضافة جلسة")
        
    def reset_test(self):
        """إعادة تعيين الاختبار"""
        self.dental_tab.current_patient_id = None
        self.dental_tab.current_plan_id = None
        self.dental_tab.patient_name_label.setText("لم يتم اختيار مريض")
        self.dental_tab.clear_form()
        print("🔄 تم إعادة تعيين الاختبار")

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = PatientTreatmentWorkflowTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
