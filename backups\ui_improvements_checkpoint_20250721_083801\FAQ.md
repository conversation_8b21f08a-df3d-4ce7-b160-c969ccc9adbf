# الأسئلة الشائعة

## عام

### ما هو نظام إدارة العيادة السنية؟
نظام إدارة العيادة السنية هو تطبيق سطح مكتب مصمم خصيصًا لمساعدة أطباء الأسنان في إدارة عياداتهم بكفاءة. يوفر النظام واجهة مستخدم عربية بالكامل مع دعم الكتابة من اليمين إلى اليسار (RTL).

### ما هي المتطلبات التقنية لتشغيل النظام؟
يتطلب النظام Python 3.7 أو أحدث، بالإضافة إلى المكتبات المذكورة في ملف requirements.txt. يمكن تشغيل النظام على أنظمة Windows و macOS و Linux.

## التثبيت والإعداد

### كيف يمكنني تثبيت النظام؟
يمكنك تثبيت النظام باتباع الخطوات التالية:
1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المستودع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة: `pip install -r requirements.txt`
5. قم بتشغيل التطبيق: `python main.py` أو انقر مرتين على ملف `run.bat`

### كيف يمكنني إنشاء حساب مستخدم جديد؟
يتم إنشاء حساب مستخدم افتراضي (admin/admin123) عند تشغيل النظام لأول مرة. يمكن للمستخدم المسؤول إنشاء حسابات جديدة من خلال تبويبة الإعدادات > إدارة المستخدمين.

## استخدام النظام

### كيف يمكنني إضافة مريض جديد؟
يمكنك إضافة مريض جديد من خلال تبويبة المرضى، ثم النقر على زر "إضافة مريض جديد" وملء النموذج بالمعلومات المطلوبة.

### كيف يمكنني إضافة خطة علاج لمريض؟
1. انتقل إلى تبويبة المرضى واختر المريض المطلوب
2. انتقل إلى تبويبة المعالجة
3. استخدم مخطط الأسنان لاختيار السن المطلوب
4. أدخل تفاصيل خطة العلاج واضغط على "حفظ"

### كيف يمكنني جدولة موعد؟
1. انتقل إلى تبويبة المواعيد
2. اختر التاريخ والوقت المناسب
3. انقر على الفترة الزمنية المطلوبة
4. أدخل تفاصيل الموعد واختر المريض
5. اضغط على "حفظ"

### كيف يمكنني إنشاء تقرير؟
1. انتقل إلى تبويبة التقارير
2. اختر نوع التقرير (مريض، مخبر، عيادة)
3. حدد المعايير المطلوبة (نطاق التاريخ، المريض، إلخ)
4. اضغط على "إنشاء التقرير"
5. يمكنك طباعة التقرير أو حفظه كملف PDF

## النسخ الاحتياطي واستعادة البيانات

### كيف يمكنني عمل نسخة احتياطية من البيانات؟
1. انتقل إلى تبويبة الإعدادات > النسخ الاحتياطي
2. اضغط على "إنشاء نسخة احتياطية"
3. اختر موقع حفظ النسخة الاحتياطية

### كيف يمكنني استعادة البيانات من نسخة احتياطية؟
1. انتقل إلى تبويبة الإعدادات > النسخ الاحتياطي
2. اضغط على "استعادة من نسخة احتياطية"
3. اختر ملف النسخة الاحتياطية
4. اتبع التعليمات لاستعادة البيانات

## استكشاف الأخطاء وإصلاحها

### النظام لا يعمل بعد التثبيت
تأكد من تثبيت جميع المكتبات المطلوبة باستخدام الأمر: `pip install -r requirements.txt`

### لا يمكنني تسجيل الدخول
تأكد من استخدام بيانات تسجيل الدخول الصحيحة. إذا نسيت كلمة المرور، يمكنك استخدام حساب المسؤول الافتراضي (admin/admin123) لإعادة تعيين كلمة المرور.

### كيف يمكنني تغيير بين الوضع الفاتح والغامق؟
يمكنك تغيير الوضع من خلال تبويبة الإعدادات > المظهر، ثم اختيار الوضع المطلوب.

### أين يتم تخزين البيانات؟
يتم تخزين البيانات في قاعدة بيانات SQLite في مجلد التطبيق. يمكنك عمل نسخة احتياطية منها بانتظام لتجنب فقدان البيانات.