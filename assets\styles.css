/* أنماط عامة للتطبيق */

/* متغيرات الألوان للوضع الفاتح */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --background-color: #f5f5f5;
    --card-background: #ffffff;
    --text-color: #333333;
    --border-color: #dddddd;
    --hover-color: #ecf0f1;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
}

/* متغيرات الألوان للوضع الغامق */
[data-theme="dark"] {
    --primary-color: #1a2530;
    --secondary-color: #2980b9;
    --accent-color: #c0392b;
    --background-color: #2c3e50;
    --card-background: #34495e;
    --text-color: #ecf0f1;
    --border-color: #7f8c8d;
    --hover-color: #2c3e50;
    --success-color: #27ae60;
    --warning-color: #d35400;
    --danger-color: #c0392b;
    --info-color: #2980b9;
}

/* أنماط عامة */
QWidget {
    font-family: 'Arial', 'Tahoma', sans-serif;
    font-size: 10pt;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* شريط العنوان */
#titleBar {
    background-color: var(--primary-color);
    color: white;
    border-bottom: 1px solid var(--border-color);
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid var(--border-color);
    background: var(--card-background);
    border-radius: 4px;
}

QTabBar::tab {
    padding: 10px 15px;
    margin: 2px;
    border-radius: 4px;
    background-color: var(--card-background);
    color: var(--text-color);
}

QTabBar::tab:selected {
    background-color: var(--secondary-color);
    color: white;
    font-weight: bold;
}

QTabBar::tab:hover {
    background-color: var(--hover-color);
}

/* الأزرار */
QPushButton {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1f6dad;
}

QPushButton:disabled {
    background-color: #95a5a6;
    color: #ecf0f1;
}

/* أزرار الإجراءات */
QPushButton.success {
    background-color: var(--success-color);
}

QPushButton.success:hover {
    background-color: #27ae60;
}

QPushButton.warning {
    background-color: var(--warning-color);
}

QPushButton.warning:hover {
    background-color: #d35400;
}

QPushButton.danger {
    background-color: var(--danger-color);
}

QPushButton.danger:hover {
    background-color: #c0392b;
}

/* حقول الإدخال */
QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit {
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--card-background);
    color: var(--text-color);
}

QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
    border: 1px solid var(--secondary-color);
}

/* القوائم المنسدلة */
QComboBox {
    padding-right: 20px;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid var(--border-color);
}

QComboBox QAbstractItemView {
    background-color: var(--card-background);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    selection-background-color: var(--secondary-color);
    selection-color: white;
}

/* الجداول */
QTableView, QTreeView, QListView {
    background-color: var(--card-background);
    alternate-background-color: var(--hover-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    gridline-color: var(--border-color);
}

QTableView::item:selected, QTreeView::item:selected, QListView::item:selected {
    background-color: var(--secondary-color);
    color: white;
}

QHeaderView::section {
    background-color: var(--primary-color);
    color: white;
    padding: 5px;
    border: 1px solid var(--border-color);
}

/* مربعات الحوار */
QDialog {
    background-color: var(--background-color);
}

/* البطاقات والإطارات */
QFrame {
    border-radius: 4px;
}

QGroupBox {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-top: 20px;
    font-weight: bold;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 5px;
    color: var(--text-color);
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background: var(--background-color);
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: var(--secondary-color);
    min-height: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: var(--background-color);
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: var(--secondary-color);
    min-width: 20px;
    border-radius: 5px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* شريط الحالة */
QStatusBar {
    background-color: var(--primary-color);
    color: white;
}

/* مخطط الأسنان */
#teethChart {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.tooth {
    fill: white;
    stroke: #333;
    stroke-width: 1;
}

.tooth:hover {
    fill: var(--hover-color);
    cursor: pointer;
}

.tooth.selected {
    fill: var(--secondary-color);
}

.tooth.treated {
    fill: var(--success-color);
}

.tooth.pending {
    fill: var(--warning-color);
}

/* تقويم المواعيد */
#calendarWidget {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.calendar-day {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
}

.calendar-day.today {
    background-color: var(--info-color);
    color: white;
}

.calendar-day.has-appointments {
    font-weight: bold;
}

.time-slot {
    border: 1px solid var(--border-color);
    background-color: var(--card-background);
}

.time-slot.occupied {
    background-color: var(--accent-color);
    color: white;
}

.time-slot:hover {
    background-color: var(--hover-color);
}

/* تخصيص RTL */
/* ملاحظة: خاصية direction غير متوفرة في Qt CSS، يتم التحكم في الاتجاه من خلال الكود */

/* تخصيص الرسوم البيانية */
.chart-widget {
    background-color: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}