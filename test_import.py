#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لاستيراد واجهة خيارات المعالجة
"""

try:
    from PyQt5.QtWidgets import QApplication
    import sys

    # إنشاء QApplication
    app = QApplication(sys.argv)

    from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget, DentalTreatmentsTab
    print("✅ تم استيراد TreatmentOptionsWidget و DentalTreatmentsTab بنجاح")

    # اختبار إنشاء كائن
    widget = TreatmentOptionsWidget()
    print("✅ تم إنشاء كائن TreatmentOptionsWidget بنجاح")

    # اختبار إنشاء الواجهة الرئيسية
    main_tab = DentalTreatmentsTab(None)  # None بدلاً من db_handler للاختبار
    print("✅ تم إنشاء كائن DentalTreatmentsTab بنجاح")

    # اختبار الطرق الأساسية
    widget.clear_all_options()
    print("✅ تم تشغيل clear_all_options() بنجاح")

    selected = widget.get_selected_options()
    print(f"✅ تم تشغيل get_selected_options() بنجاح: {len(selected)} خيارات محددة")

    print("\n🎉 جميع الاختبارات نجحت! المشكلة تم حلها.")
    print("✅ يمكن الآن تشغيل التطبيق الرئيسي بدون مشاكل.")

except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
