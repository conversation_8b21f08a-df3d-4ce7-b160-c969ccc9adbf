#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار توحيد الحد الأدنى لارتفاع العناصر في حاوية معلومات المريض
Test unifying minimum height of elements in patient information container
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.patients_tab import PatientsTab

class UnifiedElementHeightsTestWindow(QMainWindow):
    """نافذة اختبار توحيد ارتفاع العناصر"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار توحيد الحد الأدنى لارتفاع العناصر في حاوية معلومات المريض")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار توحيد الحد الأدنى لارتفاع العناصر في حاوية معلومات المريض")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 توحيد الحد الأدنى لارتفاع العناصر في حاوية معلومات المريض:
        
        ✅ توحيد ارتفاع حقول البيانات الأساسية مع عناوينها:
        • توحيد الحد الأدنى لارتفاع حقول التعبئة (الاسم، سنة الولادة، العمر، رقم الموبايل، رقم الواتس آب)
        • جعل ارتفاع العناوين مطابق لارتفاع الحقول العادية (35px)
        • ضمان أن جميع العناصر في نفس الصف لها نفس الارتفاع
        • استخدام min-height و max-height لضمان التطابق
        
        ✅ توحيد ارتفاع حقول النصوص الطويلة مع عناوينها:
        • توحيد الحد الأدنى لارتفاع العناوين (الأمراض العامة، الأدوية، الملاحظات)
        • جعل ارتفاع العناوين مطابق لارتفاع حقول النصوص المتعددة الأسطر (80px)
        • ضمان التناسق البصري بين العناوين وحقول QTextEdit
        • استخدام معامل is_text_field في دالة create_label()
        
        🔧 التحسينات التقنية المطبقة:
        • دالة create_label() محدثة مع معامل is_text_field
        • تحديد الارتفاع بناءً على نوع الحقل (عادي أم نص طويل)
        • تنسيق CSS موحد لجميع أنواع الحقول
        • إزالة setMaximumHeight() المتكررة واستخدام CSS بدلاً منها
        • الاحتفاظ بجميع الوظائف الحالية للحقول
        
        📊 الارتفاعات الموحدة:
        • الحقول العادية (QLineEdit, QSpinBox): 35px
        • عناوين الحقول العادية: 35px (مطابقة)
        • حقول النصوص الطويلة (QTextEdit): 80px
        • عناوين حقول النصوص الطويلة: 80px (مطابقة)
        • العرض الثابت للعناوين: 120px (موحد)
        
        ✅ النتائج المحققة:
        • تناسق بصري كامل في حاوية معلومات المريض
        • جميع العناصر تبدو متوازنة ومنظمة
        • توحيد الارتفاعات بين العناوين وحقول التعبئة المقترنة
        • تحسين المظهر العام دون إهدار المساحة
        • الحفاظ على قابلية القراءة والاستخدام
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة المرضى مع الارتفاعات الموحدة
        options_title = QLabel("⚙️ تبويبة المرضى (مع ارتفاعات موحدة للعناصر)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة المرضى
        self.patients_tab = PatientsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.patients_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار بيانات تجريبية
        test_data_btn = QPushButton("تعبئة بيانات تجريبية")
        test_data_btn.clicked.connect(self.fill_test_data)
        test_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_data_btn)
        
        # زر مسح البيانات
        clear_data_btn = QPushButton("مسح البيانات")
        clear_data_btn.clicked.connect(self.clear_test_data)
        clear_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_data_btn)
        
        # زر اختبار التركيز
        test_focus_btn = QPushButton("اختبار التركيز على الحقول")
        test_focus_btn.clicked.connect(self.test_field_focus)
        test_focus_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_focus_btn)
        
        # زر عرض معلومات الارتفاعات
        show_heights_btn = QPushButton("عرض معلومات الارتفاعات")
        show_heights_btn.clicked.connect(self.show_height_info)
        show_heights_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        buttons_layout.addWidget(show_heights_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار توحيد الارتفاعات:
        
        🔍 ما يجب ملاحظته:
        • عناوين الحقول العادية (الاسم، سنة الولادة، رقم الموبايل، رقم الواتس آب) لها نفس ارتفاع الحقول (35px)
        • عناوين حقول النصوص الطويلة (الأمراض العامة، الأدوية، الملاحظات) لها نفس ارتفاع حقول النصوص (80px)
        • جميع العناصر في نفس الصف متطابقة في الارتفاع
        • التناسق البصري الكامل في حاوية معلومات المريض
        • المحاذاة المتوازنة بين العناوين والحقول
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على "تعبئة بيانات تجريبية" لملء الحقول ومشاهدة التناسق
        • انقر على "مسح البيانات" لإفراغ الحقول والعودة للحالة الأصلية
        • انقر على "اختبار التركيز على الحقول" لرؤية تأثير التركيز على الحقول
        • انقر على "عرض معلومات الارتفاعات" لرؤية تفاصيل الارتفاعات
        • لاحظ التطابق في الارتفاع بين كل عنوان وحقله المقترن
        • تحقق من التناسق البصري العام للنموذج
        
        ✅ النتائج المتوقعة:
        • تطابق كامل في الارتفاع بين العناوين والحقول المقترنة بها
        • مظهر متوازن ومنظم لحاوية معلومات المريض
        • تناسق بصري كامل في جميع عناصر النموذج
        • تحسين في المظهر العام دون التأثير على الوظائف
        • سهولة القراءة والاستخدام مع التصميم المحسن
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم توحيد الحد الأدنى لارتفاع العناصر في حاوية معلومات المريض بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def fill_test_data(self):
        """تعبئة بيانات تجريبية"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            form.name_input.setText("أحمد محمد علي")
            form.birth_year_input.setValue(1985)
            form.mobile_input.setText("**********")
            form.whatsapp_input.setText("**********")
            form.general_diseases_input.setPlainText("ضغط الدم، السكري")
            form.medications_input.setPlainText("أسبرين، ميتفورمين")
            form.notes_input.setPlainText("مريض منتظم في المواعيد، يحتاج متابعة دورية")
            
    def clear_test_data(self):
        """مسح البيانات التجريبية"""
        if hasattr(self.patients_tab, 'patient_form'):
            self.patients_tab.patient_form.clear_form()
            
    def test_field_focus(self):
        """اختبار التركيز على الحقول"""
        if hasattr(self.patients_tab, 'patient_form'):
            form = self.patients_tab.patient_form
            form.name_input.setFocus()
            
    def show_height_info(self):
        """عرض معلومات الارتفاعات"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(
            self,
            "معلومات الارتفاعات الموحدة",
            "الارتفاعات الموحدة في حاوية معلومات المريض:\n\n"
            "📏 الحقول العادية:\n"
            "• حقول الإدخال (QLineEdit, QSpinBox): 35px\n"
            "• عناوين الحقول العادية: 35px\n\n"
            "📏 حقول النصوص الطويلة:\n"
            "• حقول النصوص (QTextEdit): 80px\n"
            "• عناوين حقول النصوص: 80px\n\n"
            "📐 العرض الموحد:\n"
            "• عرض جميع العناوين: 120px\n\n"
            "✅ النتيجة:\n"
            "تناسق بصري كامل بين العناوين والحقول المقترنة بها!"
        )

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = UnifiedElementHeightsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
