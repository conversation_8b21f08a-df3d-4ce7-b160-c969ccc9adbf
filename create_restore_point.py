#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إنشاء نقاط الاستعادة للمشروع
تقوم بإنشاء نسخة احتياطية كاملة من المشروع مع طابع زمني
"""

import os
import shutil
import datetime
from pathlib import Path

def create_restore_point(description=""):
    """
    إنشاء نقطة استعادة جديدة
    
    Args:
        description (str): وصف التغييرات التي ستتم
    """
    
    # الحصول على الوقت الحالي
    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d_%H%M%S")
    
    # إنشاء اسم المجلد
    backup_name = f"restore_point_{timestamp}"
    if description:
        backup_name += f"_{description}"
    
    # مسارات المجلدات
    project_root = Path("c:/Users/<USER>/OneDrive/Desktop/trae")
    backups_dir = project_root / "backups"
    backup_dir = backups_dir / backup_name
    
    # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
    backups_dir.mkdir(exist_ok=True)
    backup_dir.mkdir(exist_ok=True)
    
    print(f"🔄 إنشاء نقطة استعادة: {backup_name}")
    
    # الملفات والمجلدات المهمة للنسخ الاحتياطي
    important_items = [
        "main.py",
        "ui",
        "database", 
        "assets",
        "reports",
        "requirements.txt",
        "setup.py",
        "start.bat",
        "dental_prices_config.json"
    ]
    
    # نسخ الملفات والمجلدات المهمة
    for item in important_items:
        source = project_root / item
        if source.exists():
            if source.is_file():
                shutil.copy2(source, backup_dir / item)
                print(f"  ✅ تم نسخ الملف: {item}")
            elif source.is_dir():
                shutil.copytree(source, backup_dir / item)
                print(f"  ✅ تم نسخ المجلد: {item}")
    
    # إنشاء ملف معلومات النسخة الاحتياطية
    info_file = backup_dir / "restore_info.txt"
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write(f"تاريخ إنشاء النسخة الاحتياطية: {now.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"الوصف: {description}\n")
        f.write(f"اسم النسخة: {backup_name}\n")
        f.write("\n--- معلومات النسخة الاحتياطية ---\n")
        f.write("هذه النسخة الاحتياطية تحتوي على:\n")
        for item in important_items:
            if (project_root / item).exists():
                f.write(f"- {item}\n")
    
    print(f"✅ تم إنشاء نقطة الاستعادة بنجاح في: {backup_dir}")
    print(f"📝 لاستعادة هذه النقطة، استخدم: restore_from_backup('{backup_name}')")
    
    return backup_dir

def restore_from_backup(backup_name):
    """
    استعادة من نقطة استعادة محددة
    
    Args:
        backup_name (str): اسم النسخة الاحتياطية المراد استعادتها
    """
    
    project_root = Path("c:/Users/<USER>/OneDrive/Desktop/trae")
    backups_dir = project_root / "backups"
    backup_dir = backups_dir / backup_name
    
    if not backup_dir.exists():
        print(f"❌ النسخة الاحتياطية غير موجودة: {backup_name}")
        return False
    
    print(f"🔄 استعادة من النسخة الاحتياطية: {backup_name}")
    
    # قراءة معلومات النسخة الاحتياطية
    info_file = backup_dir / "restore_info.txt"
    if info_file.exists():
        print("📋 معلومات النسخة الاحتياطية:")
        with open(info_file, 'r', encoding='utf-8') as f:
            print(f.read())
    
    # التأكد من الاستعادة
    response = input("هل تريد المتابعة مع الاستعادة؟ (y/n): ")
    if response.lower() != 'y':
        print("❌ تم إلغاء الاستعادة")
        return False
    
    # استعادة الملفات
    for item in backup_dir.iterdir():
        if item.name == "restore_info.txt":
            continue
            
        target = project_root / item.name
        
        if target.exists():
            if target.is_file():
                target.unlink()
            elif target.is_dir():
                shutil.rmtree(target)
        
        if item.is_file():
            shutil.copy2(item, target)
            print(f"  ✅ تم استعادة الملف: {item.name}")
        elif item.is_dir():
            shutil.copytree(item, target)
            print(f"  ✅ تم استعادة المجلد: {item.name}")
    
    print("✅ تم استعادة النسخة الاحتياطية بنجاح!")
    return True

def list_restore_points():
    """
    عرض قائمة بجميع نقاط الاستعادة المتاحة
    """
    
    project_root = Path("c:/Users/<USER>/OneDrive/Desktop/trae")
    backups_dir = project_root / "backups"
    
    if not backups_dir.exists():
        print("📂 لا توجد نقاط استعادة متاحة")
        return
    
    restore_points = [d for d in backups_dir.iterdir() if d.is_dir()]
    
    if not restore_points:
        print("📂 لا توجد نقاط استعادة متاحة")
        return
    
    print("📋 نقاط الاستعادة المتاحة:")
    print("-" * 50)
    
    for point in sorted(restore_points, key=lambda x: x.name):
        info_file = point / "restore_info.txt"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                date_line = next((l for l in lines if l.startswith("تاريخ إنشاء")), "")
                desc_line = next((l for l in lines if l.startswith("الوصف")), "")
                
                print(f"📁 {point.name}")
                print(f"   {date_line.strip()}")
                print(f"   {desc_line.strip()}")
                print()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "list":
            list_restore_points()
        elif sys.argv[1] == "restore" and len(sys.argv) > 2:
            restore_from_backup(sys.argv[2])
        elif sys.argv[1] == "create":
            description = " ".join(sys.argv[2:]) if len(sys.argv) > 2 else ""
            create_restore_point(description)
        else:
            print("الاستخدام:")
            print("  python create_restore_point.py create [وصف]")
            print("  python create_restore_point.py list")
            print("  python create_restore_point.py restore [اسم_النسخة]")
    else:
        # إنشاء نقطة استعادة افتراضية
        create_restore_point("نسخة_احتياطية_افتراضية")