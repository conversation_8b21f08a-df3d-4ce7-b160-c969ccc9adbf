#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الترقيم التلقائي الذكي لأرقام خطط المعالجة
Test intelligent auto-numbering system for treatment plan numbers
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton, QTextEdit
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class AutoNumberingSystemTestWindow(QMainWindow):
    """نافذة اختبار نظام الترقيم التلقائي الذكي"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار نظام الترقيم التلقائي الذكي لأرقام خطط المعالجة")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار نظام الترقيم التلقائي الذكي لأرقام خطط المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات النظام
        info_label = QLabel("""
        🎯 نظام الترقيم التلقائي الذكي لأرقام خطط المعالجة:
        
        ✅ الترقيم التلقائي عند فتح الواجهة:
        • عند فتح تبويبة علاج الأسنان، يتم ملء حقل رقم خطة المعالجة تلقائياً
        • استخدام دالة get_next_plan_number() للحصول على الرقم التالي من قاعدة البيانات
        • الرقم يظهر فور تحميل الواجهة دون تدخل المستخدم
        
        ✅ نظام الترقيم التصاعدي الذكي:
        • البحث في قاعدة البيانات عن أكبر رقم خطة معالجة موجود حالياً
        • إضافة 1 إلى أكبر رقم موجود للحصول على الرقم التالي
        • في حالة عدم وجود خطط سابقة، البدء من الرقم 1
        
        ✅ التعامل مع الخطط المحذوفة:
        • الاحتفاظ بالترتيب التصاعدي (مثال: إذا كانت الأرقام 1,2,4 فالرقم التالي سيكون 5)
        • عدم إعادة استخدام الأرقام المحذوفة للحفاظ على التسلسل
        
        ✅ التحديث التلقائي:
        • عند حفظ خطة معالجة جديدة بنجاح، تحديث حقل رقم الخطة تلقائياً للرقم التالي
        • عند النقر على زر "إضافة خطة معالجة سنية", تحديث الرقم للخطة الجديدة
        • تزامن الترقيم مع قاعدة البيانات في جميع الأوقات
        
        🔧 الاعتبارات التقنية المطبقة:
        • دالة get_next_plan_number() تتعامل مع قاعدة البيانات بشكل صحيح
        • التعامل مع الحالات الاستثنائية (قاعدة بيانات فارغة، أخطاء الاتصال)
        • الاحتفاظ بجميع الوظائف الحالية لحقل رقم الخطة
        • نظام ترقيم محلي احتياطي في حالة عدم توفر قاعدة البيانات
        
        📊 قاعدة البيانات الوهمية للاختبار:
        • الخطة رقم 1: تنظيف - السن 11 - 50,000 ل.س
        • الخطة رقم 2: حشوة - السن 12 - 75,000 ل.س
        • الخطة رقم 4: تاج - السن 13 - 200,000 ل.س
        • (لاحظ أن الرقم 3 محذوف لاختبار التعامل مع الخطط المحذوفة)
        • الرقم التالي المتوقع: 5
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع الترقيم التلقائي
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع نظام ترقيم تلقائي ذكي)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار الرقم الحالي
        test_current_btn = QPushButton("عرض الرقم الحالي")
        test_current_btn.clicked.connect(self.show_current_number)
        test_current_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_current_btn)
        
        # زر اختبار الرقم التالي
        test_next_btn = QPushButton("اختبار الرقم التالي")
        test_next_btn.clicked.connect(self.test_next_number)
        test_next_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_next_btn)
        
        # زر اختبار إضافة خطة جديدة
        test_add_plan_btn = QPushButton("اختبار إضافة خطة جديدة")
        test_add_plan_btn.clicked.connect(self.test_add_new_plan)
        test_add_plan_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        buttons_layout.addWidget(test_add_plan_btn)
        
        # زر اختبار قاعدة البيانات الوهمية
        test_mock_db_btn = QPushButton("عرض قاعدة البيانات الوهمية")
        test_mock_db_btn.clicked.connect(self.show_mock_database)
        test_mock_db_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        buttons_layout.addWidget(test_mock_db_btn)
        
        # زر إعادة تطبيق الترقيم
        reapply_numbering_btn = QPushButton("إعادة تطبيق الترقيم")
        reapply_numbering_btn.clicked.connect(self.reapply_auto_numbering)
        reapply_numbering_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(reapply_numbering_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # منطقة عرض النتائج
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(150)
        self.results_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        layout.addWidget(self.results_text)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار نظام الترقيم التلقائي:
        
        🔍 ما يجب ملاحظته:
        • حقل رقم خطة المعالجة يحتوي على رقم تلقائي عند فتح البرنامج
        • الرقم المعروض هو الرقم التالي المتاح (5 في هذا المثال)
        • عند النقر على "إضافة خطة معالجة سنية"، يتم تحديث الرقم للخطة التالية
        • النظام يتعامل مع الخطط المحذوفة بذكاء (لا يعيد استخدام الأرقام المحذوفة)
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على "عرض الرقم الحالي" لرؤية الرقم المعين حالياً
        • انقر على "اختبار الرقم التالي" لرؤية الرقم التالي المحسوب
        • انقر على "اختبار إضافة خطة جديدة" لمحاكاة إضافة خطة جديدة
        • انقر على "عرض قاعدة البيانات الوهمية" لرؤية البيانات المستخدمة في الحساب
        • انقر على "إعادة تطبيق الترقيم" لإعادة تطبيق النظام
        • لاحظ كيف يتم تحديث الرقم تلقائياً بعد كل عملية
        
        ✅ النتائج المتوقعة:
        • رقم خطة تلقائي عند فتح البرنامج (5 في هذا المثال)
        • تحديث تلقائي للرقم بعد كل عملية إضافة
        • عدم إعادة استخدام الأرقام المحذوفة
        • ترقيم تصاعدي ذكي ومتسق
        • عمل النظام حتى بدون قاعدة بيانات حقيقية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تطبيق نظام الترقيم التلقائي الذكي بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
        # عرض الرقم الحالي عند البداية
        self.show_current_number()
        
    def show_current_number(self):
        """عرض الرقم الحالي"""
        current_number = self.dental_tab.treatment_plan.plan_number_edit.text()
        self.results_text.append(f"🔢 الرقم الحالي في حقل خطة المعالجة: {current_number}")
        
    def test_next_number(self):
        """اختبار الرقم التالي"""
        next_number = self.dental_tab.get_next_plan_number()
        self.results_text.append(f"➡️ الرقم التالي المحسوب: {next_number}")
        
    def test_add_new_plan(self):
        """اختبار إضافة خطة جديدة"""
        old_number = self.dental_tab.treatment_plan.plan_number_edit.text()
        self.dental_tab.add_new_treatment_plan()
        new_number = self.dental_tab.treatment_plan.plan_number_edit.text()
        self.results_text.append(f"📋 إضافة خطة جديدة: {old_number} → {new_number}")
        
    def show_mock_database(self):
        """عرض قاعدة البيانات الوهمية"""
        mock_db = self.dental_tab.simulate_database_operations()
        self.results_text.append("🗄️ قاعدة البيانات الوهمية:")
        for plan in mock_db['treatment_plans']:
            self.results_text.append(f"   خطة {plan['plan_number']}: {plan['treatment']} - السن {plan['tooth_number']} - {plan['cost']} ل.س")
        max_number = self.dental_tab.get_max_plan_number_from_mock_db()
        self.results_text.append(f"   أكبر رقم: {max_number}, الرقم التالي: {max_number + 1 if max_number else 1}")
        
    def reapply_auto_numbering(self):
        """إعادة تطبيق الترقيم التلقائي"""
        old_number = self.dental_tab.treatment_plan.plan_number_edit.text()
        self.dental_tab.apply_auto_numbering()
        new_number = self.dental_tab.treatment_plan.plan_number_edit.text()
        self.results_text.append(f"🔄 إعادة تطبيق الترقيم: {old_number} → {new_number}")

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = AutoNumberingSystemTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
