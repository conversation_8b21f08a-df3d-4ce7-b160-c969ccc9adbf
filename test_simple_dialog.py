#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار النافذة المبسطة لإدارة الأسعار
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة المبسطة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog

class TestSimpleWindow(QMainWindow):
    """نافذة اختبار النافذة المبسطة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار النافذة المبسطة لإدارة الأسعار")
        self.setGeometry(200, 200, 600, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار النافذة المبسطة لإدارة أسعار وأسماء المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف النافذة المبسطة
        description = QLabel("""
النافذة المبسطة الجديدة:
✅ جدول واضح وبسيط للمعالجات والأسعار
✅ 32 معالجة مقسمة حسب الفئات
✅ إمكانية تعديل الأسماء والأسعار مباشرة
✅ واجهة عربية RTL كاملة
✅ حفظ دائم للتغييرات
✅ تصميم مبسط وواضح
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #007bff;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # زر فتح النافذة المبسطة
        open_btn = QPushButton("📋 فتح النافذة المبسطة (جدول)")
        open_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #004085, #002752);
            }
        """)
        open_btn.clicked.connect(self.open_simple_dialog)
        layout.addWidget(open_btn)
        
        # معلومات إضافية
        info = QLabel("النافذة الآن تستخدم جدول بسيط وواضح بدلاً من التخطيط المعقد")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 6px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(info)
        
    def open_simple_dialog(self):
        """فتح النافذة المبسطة"""
        try:
            print("📋 فتح النافذة المبسطة...")
            dialog = ComprehensiveTreatmentPricingDialog(self)
            
            print(f"📊 عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
            print(f"📊 عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
            
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                print("✅ تم حفظ التغييرات")
            else:
                print("❌ تم إلغاء العملية")
                
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestSimpleWindow()
    window.show()
    
    print("تم تشغيل نافذة اختبار النافذة المبسطة")
    print("اضغط على الزر لاختبار النافذة المبسطة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
