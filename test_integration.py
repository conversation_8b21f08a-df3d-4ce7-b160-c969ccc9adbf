#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التكامل بين نافذة إدارة الأسعار ونافذة خطة المعالجة
"""

import sys
import os
import json
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النوافذ
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog, DentalTreatmentsTab

class IntegrationTestWindow(QMainWindow):
    """نافذة اختبار التكامل"""
    
    def __init__(self):
        super().__init__()
        self.dental_tab = None
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار التكامل - إدارة الأسعار وخطة المعالجة")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار التكامل بين إدارة الأسعار وخطة المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف الاختبار
        description = QLabel("""
خطوات الاختبار:
1. اضغط "إنشاء تبويبة المعالجة" لإنشاء واجهة خطة المعالجة
2. اضغط "فتح إدارة الأسعار" لتعديل أسماء المعالجات والأسعار
3. قم بتعديل بعض الأسماء والأسعار واحفظ التغييرات
4. تحقق من أن التغييرات ظهرت في واجهة خطة المعالجة
5. اضغط "عرض ملف التكوين" لرؤية البيانات المحفوظة
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # أزرار الاختبار
        buttons_layout = QHBoxLayout()
        
        # زر إنشاء تبويبة المعالجة
        create_tab_btn = QPushButton("🏥 إنشاء تبويبة المعالجة")
        create_tab_btn.setStyleSheet(self.get_button_style("#007bff"))
        create_tab_btn.clicked.connect(self.create_dental_tab)
        buttons_layout.addWidget(create_tab_btn)
        
        # زر فتح إدارة الأسعار
        open_pricing_btn = QPushButton("💰 فتح إدارة الأسعار")
        open_pricing_btn.setStyleSheet(self.get_button_style("#28a745"))
        open_pricing_btn.clicked.connect(self.open_pricing_dialog)
        buttons_layout.addWidget(open_pricing_btn)
        
        # زر عرض ملف التكوين
        show_config_btn = QPushButton("📄 عرض ملف التكوين")
        show_config_btn.setStyleSheet(self.get_button_style("#ffc107"))
        show_config_btn.clicked.connect(self.show_config_file)
        buttons_layout.addWidget(show_config_btn)
        
        layout.addLayout(buttons_layout)
        
        # منطقة الحالة
        self.status_label = QLabel("جاهز للاختبار...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 5px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.status_label)
        
    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
            QPushButton:pressed {{
                opacity: 0.6;
            }}
        """
        
    def create_dental_tab(self):
        """إنشاء تبويبة المعالجة"""
        try:
            # إنشاء تبويبة المعالجة (بدون قاعدة بيانات للاختبار)
            self.dental_tab = DentalTreatmentsTab(db_handler=None, parent=self)
            self.dental_tab.show()
            self.status_label.setText("✅ تم إنشاء تبويبة المعالجة بنجاح")
            print("تم إنشاء تبويبة المعالجة")
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في إنشاء تبويبة المعالجة: {e}")
            print(f"خطأ: {e}")
            
    def open_pricing_dialog(self):
        """فتح نافذة إدارة الأسعار"""
        try:
            if not self.dental_tab:
                self.status_label.setText("⚠️ يرجى إنشاء تبويبة المعالجة أولاً")
                return
                
            dialog = ComprehensiveTreatmentPricingDialog(self.dental_tab)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                self.status_label.setText("✅ تم حفظ التغييرات في إدارة الأسعار")
                print("تم حفظ التغييرات")
            else:
                self.status_label.setText("ℹ️ تم إلغاء تعديل الأسعار")
                print("تم إلغاء التعديل")
                
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في فتح إدارة الأسعار: {e}")
            print(f"خطأ: {e}")
            
    def show_config_file(self):
        """عرض محتوى ملف التكوين"""
        try:
            config_file = "dental_prices_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print("=" * 50)
                print("محتوى ملف التكوين:")
                print("=" * 50)
                
                if 'treatment_names' in data:
                    print("أسماء المعالجات:")
                    for key, name in data['treatment_names'].items():
                        print(f"  {key}: {name}")
                    print()
                
                # عرض بعض الأسعار كمثال
                prices_count = len([k for k in data.keys() if k != 'treatment_names'])
                print(f"عدد الأسعار المحفوظة: {prices_count}")
                
                self.status_label.setText(f"✅ تم عرض ملف التكوين - {prices_count} سعر محفوظ")
            else:
                self.status_label.setText("⚠️ ملف التكوين غير موجود")
                print("ملف التكوين غير موجود")
                
        except Exception as e:
            self.status_label.setText(f"❌ خطأ في قراءة ملف التكوين: {e}")
            print(f"خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = IntegrationTestWindow()
    window.show()
    
    print("تم تشغيل اختبار التكامل")
    print("اتبع الخطوات في الواجهة لاختبار التكامل")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
