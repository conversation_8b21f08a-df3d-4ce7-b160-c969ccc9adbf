#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التراجع عن تعديل حقل رقم السن
Test reverting tooth number field modifications
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class ToothNumberRevertTestWindow(QMainWindow):
    """نافذة اختبار التراجع عن تعديل حقل رقم السن"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار التراجع عن تعديل حقل رقم السن")
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار التراجع عن تعديل حقل رقم السن")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التراجع
        info_label = QLabel("""
        🔄 التراجع المطبق على حقل رقم السن:
        
        ❌ إزالة التنسيق المطابق للعنوان:
        • تم التراجع عن جعل تنسيق حقل التعبئة مطابقاً لتنسيق العنوان
        • تم إزالة التنسيق المخصص من عنوان "رقم السن"
        • تم إزالة التنسيق المخصص من حقل إدخال رقم السن
        
        ✅ العودة للتنسيق الأصلي:
        • عنوان "رقم السن": تنسيق افتراضي بسيط
        • حقل إدخال رقم السن: تنسيق افتراضي بسيط
        • الاحتفاظ بالخصائص الوظيفية:
          - القراءة فقط (ReadOnly)
          - النص التوضيحي "السن"
          - العرض الثابت 60px
          - المحاذاة المركزية للمحتوى
        
        🎯 التحسينات المحفوظة:
        • توحيد حجم الخط للعناوين (14px) - محفوظ
        • إزالة إطار عناوين المجموعات - محفوظ
        • إزالة أسهم التحكم من الكلفة والتاريخ - محفوظ
        • التاريخ التلقائي - محفوظ
        • الترقيم التلقائي - محفوظ
        • زر "إضافة خطة معالجة سنية" - محفوظ
        • ترتيب الأزرار المحسن - محفوظ
        
        ✅ النتيجة النهائية:
        • حقل رقم السن بالتنسيق الأصلي البسيط
        • جميع التحسينات الأخرى محفوظة
        • الحفاظ على جميع الوظائف والتفاعل
        • مظهر متوازن ومتناسق
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع التراجع
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع التراجع عن حقل رقم السن)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار التراجع:
        
        🔍 ما يجب ملاحظته:
        • حقل رقم السن بالتنسيق الأصلي البسيط (بدون تنسيق مخصص)
        • عنوان "رقم السن" بالتنسيق الافتراضي
        • جميع التحسينات الأخرى محفوظة:
          - عناوين الحاويات والمجموعات بحجم 14px
          - عدم وجود حدود حول عناوين المجموعات
          - عدم وجود أسهم تحكم في حقول الكلفة والتاريخ
          - التاريخ الحالي معبأ تلقائياً
          - وجود زر "إضافة خطة معالجة سنية"
          - أسماء الأزرار المحدثة
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على أسنان مختلفة في المخطط التفاعلي
        • لاحظ ظهور رقم السن في الحقل بالتنسيق الأصلي
        • اختر خيارات معالجة مختلفة
        • جرب زر "إضافة خطة معالجة سنية"
        • تحقق من حقول خطة المعالجة الأخرى
        • اختبر جميع الأزرار والوظائف
        
        ✅ النتائج المتوقعة:
        • حقل رقم السن بمظهر بسيط وطبيعي
        • جميع التحسينات الأخرى تعمل بشكل صحيح
        • مظهر متوازن ومتناسق للواجهة
        • الحفاظ على جميع الوظائف الأصلية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم التراجع عن تعديل حقل رقم السن بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = ToothNumberRevertTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
