#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار واجهة إدارة خطط وجلسات المعالجة السنية
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

from ui.tabs.dental_treatments_tab import DentalTreatmentsTab
from database.db_handler import DatabaseHandler

class DentalTreatmentTestWindow(QMainWindow):
    """نافذة اختبار واجهة المعالجة السنية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        
        # إعداد النافذة الرئيسية
        self.setWindowTitle("اختبار واجهة إدارة المعالجة السنية")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # إنشاء معالج قاعدة البيانات
        db_handler = DatabaseHandler()
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(db_handler)
        main_layout.addWidget(self.dental_tab)
        
        # تطبيق التنسيق
        self.apply_styles()
    
    def apply_styles(self):
        """تطبيق التنسيقات العامة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QLabel {
                font-family: 'Arial', 'Tahoma', sans-serif;
            }
        """)

def test_dental_treatment_interface():
    """اختبار واجهة المعالجة السنية"""
    
    print("🦷 بدء اختبار واجهة إدارة المعالجة السنية...")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تعيين خصائص التطبيق
    app.setApplicationName("واجهة إدارة المعالجة السنية")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("عيادة الأسنان")
    
    # إنشاء النافذة الرئيسية
    test_window = DentalTreatmentTestWindow()
    test_window.show()
    
    print("✅ تم إنشاء واجهة المعالجة السنية بنجاح")
    print("\n📋 الميزات المتاحة للاختبار:")
    print("   • مخطط الأسنان التفاعلي (32 سن)")
    print("   • خيارات المعالجة الأربعة:")
    print("     - لبية (Endodontic)")
    print("     - ترميمية (Restorative)")
    print("     - تيجان (Crowns)")
    print("     - جراحة (Surgery)")
    print("   • خطة المعالجة السنية")
    print("   • جلسات المعالجة السنية")
    print("   • عرض البيانات والتقارير")
    
    print("\n🎯 تعليمات الاختبار:")
    print("   1. انقر على أي سن في المخطط")
    print("   2. اختر خيارات المعالجة المناسبة")
    print("   3. املأ بيانات خطة المعالجة")
    print("   4. جرب حفظ الخطة")
    print("   5. أضف جلسة معالجة سنية")
    print("   6. استخدم أزرار العرض في الجانب الأيسر")
    
    print("\n👁️ النافذة مفتوحة للاختبار البصري...")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_dental_treatment_interface()
