import sys
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea,
                             QGridLayout, QDoubleSpinBox)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate, QDateTime
from PyQt5.QtGui import QIcon, QFont

class ExpenseForm(QWidget):
    """نموذج إضافة/تعديل مصروف"""
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # حقول الإدخال
        self.expense_type_combo = QComboBox()
        self.expense_type_combo.addItems(["فواتير المواد", "أجور", "إيجار العيادة", "مصاريف أخرى"])
        
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("وصف المصروف")
        
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 1000000)
        self.amount_input.setSingleStep(100)
        self.amount_input.setSuffix(" ل.س")
        
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية")
        self.notes_input.setMaximumHeight(80)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("نوع المصروف:", self.expense_type_combo)
        form_layout.addRow("الوصف:", self.description_input)
        form_layout.addRow("المبلغ:", self.amount_input)
        form_layout.addRow("التاريخ:", self.date_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        # إضافة النموذج إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
    
    def clear_form(self):
        """مسح النموذج"""
        self.expense_type_combo.setCurrentIndex(0)
        self.description_input.clear()
        self.amount_input.setValue(0)
        self.date_input.setDate(QDate.currentDate())
        self.notes_input.clear()
    
    def get_expense_data(self):
        """الحصول على بيانات المصروف"""
        return {
            'expense_type': self.expense_type_combo.currentText(),
            'description': self.description_input.text().strip(),
            'amount': self.amount_input.value(),
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText().strip()
        }
    
    def set_expense_data(self, expense_data):
        """تعيين بيانات المصروف في النموذج"""
        # تعيين نوع المصروف
        expense_type = expense_data.get('expense_type', '')
        index = self.expense_type_combo.findText(expense_type)
        if index >= 0:
            self.expense_type_combo.setCurrentIndex(index)
        
        # تعيين باقي الحقول
        self.description_input.setText(expense_data.get('description', ''))
        self.amount_input.setValue(expense_data.get('amount', 0))
        self.notes_input.setText(expense_data.get('notes', ''))
        
        # تعيين التاريخ
        expense_date = expense_data.get('date', '')
        if expense_date:
            self.date_input.setDate(QDate.fromString(expense_date, "yyyy-MM-dd"))

class ExpenseDialog(QDialog):
    """نافذة حوار إضافة/تعديل مصروف"""
    def __init__(self, db_handler, parent=None, expense_data=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.expense_data = expense_data
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # عنوان النافذة
        self.setWindowTitle("إضافة مصروف جديد" if not self.expense_data else "تعديل المصروف")
        self.setMinimumWidth(400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج المصروف
        self.expense_form = ExpenseForm(self.db_handler)
        
        main_layout.addWidget(self.expense_form)
        
        # تعيين بيانات المصروف إذا كانت متوفرة
        if self.expense_data:
            self.expense_form.set_expense_data(self.expense_data)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
    
    def get_expense_data(self):
        """الحصول على بيانات المصروف من النموذج"""
        return self.expense_form.get_expense_data()

class ExpensesTab(QWidget):
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط البحث والتصفية
        filter_layout = QHBoxLayout()
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث عن مصروف (الوصف، النوع)")
        self.search_input.textChanged.connect(self.search_expenses)
        
        self.expense_type_filter = QComboBox()
        self.expense_type_filter.addItem("جميع الأنواع")
        self.expense_type_filter.addItems(["فواتير المواد", "أجور", "إيجار العيادة", "مصاريف أخرى"])
        self.expense_type_filter.currentIndexChanged.connect(self.filter_expenses)
        
        self.from_date_filter = QDateEdit()
        self.from_date_filter.setCalendarPopup(True)
        self.from_date_filter.setDate(QDate.currentDate().addMonths(-1))
        self.from_date_filter.dateChanged.connect(self.filter_expenses)
        
        self.to_date_filter = QDateEdit()
        self.to_date_filter.setCalendarPopup(True)
        self.to_date_filter.setDate(QDate.currentDate())
        self.to_date_filter.dateChanged.connect(self.filter_expenses)
        
        filter_layout.addWidget(QLabel("من:"))
        filter_layout.addWidget(self.from_date_filter)
        filter_layout.addWidget(QLabel("إلى:"))
        filter_layout.addWidget(self.to_date_filter)
        filter_layout.addWidget(QLabel("النوع:"))
        filter_layout.addWidget(self.expense_type_filter)
        filter_layout.addWidget(self.search_input)
        
        main_layout.addLayout(filter_layout)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        self.add_expense_button = QPushButton("إضافة مصروف جديد")
        self.add_expense_button.clicked.connect(self.add_expense)
        
        self.edit_expense_button = QPushButton("تعديل المصروف")
        self.edit_expense_button.clicked.connect(self.edit_expense)
        self.edit_expense_button.setEnabled(False)
        
        self.delete_expense_button = QPushButton("حذف المصروف")
        self.delete_expense_button.clicked.connect(self.delete_expense)
        self.delete_expense_button.setEnabled(False)
        
        buttons_layout.addWidget(self.add_expense_button)
        buttons_layout.addWidget(self.edit_expense_button)
        buttons_layout.addWidget(self.delete_expense_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(6)
        self.expenses_table.setHorizontalHeaderLabels(["النوع", "الوصف", "المبلغ", "التاريخ", "ملاحظات", "تاريخ الإضافة"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.verticalHeader().setVisible(False)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setSelectionMode(QTableWidget.SingleSelection)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        main_layout.addWidget(self.expenses_table)
        
        # ملخص المصروفات
        summary_layout = QHBoxLayout()
        
        self.total_label = QLabel("إجمالي المصروفات: 0 ل.س")
        self.total_label.setAlignment(Qt.AlignRight)
        self.total_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        summary_layout.addStretch()
        summary_layout.addWidget(self.total_label)
        
        main_layout.addLayout(summary_layout)
        
        # تحميل المصروفات
        self.load_expenses()
    
    def load_expenses(self):
        """تحميل المصروفات"""
        expenses = self.db_handler.get_expenses()
        self.update_expenses_table(expenses)
    
    def update_expenses_table(self, expenses):
        """تحديث جدول المصروفات"""
        self.expenses_table.setRowCount(0)
        
        total_amount = 0
        
        for row, expense in enumerate(expenses):
            self.expenses_table.insertRow(row)
            
            # نوع المصروف
            expense_type_item = QTableWidgetItem(expense.get('expense_type', ''))
            expense_type_item.setData(Qt.UserRole, expense['id'])  # تخزين معرف المصروف
            self.expenses_table.setItem(row, 0, expense_type_item)
            
            # الوصف
            description_item = QTableWidgetItem(expense.get('description', ''))
            self.expenses_table.setItem(row, 1, description_item)
            
            # المبلغ
            amount = expense.get('amount', 0)
            amount_item = QTableWidgetItem(f"{amount} ل.س")
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.expenses_table.setItem(row, 2, amount_item)
            
            total_amount += amount
            
            # التاريخ
            date_item = QTableWidgetItem(expense.get('date', ''))
            self.expenses_table.setItem(row, 3, date_item)
            
            # ملاحظات
            notes_item = QTableWidgetItem(expense.get('notes', ''))
            self.expenses_table.setItem(row, 4, notes_item)
            
            # تاريخ الإضافة
            created_at_item = QTableWidgetItem(expense.get('created_at', ''))
            self.expenses_table.setItem(row, 5, created_at_item)
        
        # تحديث إجمالي المصروفات
        self.total_label.setText(f"إجمالي المصروفات: {total_amount} ل.س")
    
    def on_selection_changed(self):
        """معالجة تغيير التحديد في الجدول"""
        selected_items = self.expenses_table.selectedItems()
        
        if selected_items:
            self.edit_expense_button.setEnabled(True)
            self.delete_expense_button.setEnabled(True)
        else:
            self.edit_expense_button.setEnabled(False)
            self.delete_expense_button.setEnabled(False)
    
    def search_expenses(self):
        """البحث عن المصروفات"""
        self.filter_expenses()
    
    def filter_expenses(self):
        """تصفية المصروفات"""
        search_text = self.search_input.text().strip()
        expense_type = self.expense_type_filter.currentText()
        from_date = self.from_date_filter.date().toString("yyyy-MM-dd")
        to_date = self.to_date_filter.date().toString("yyyy-MM-dd")
        
        # إذا كان نوع المصروف هو "جميع الأنواع"، نجعله فارغًا للبحث عن جميع الأنواع
        if expense_type == "جميع الأنواع":
            expense_type = ""
        
        expenses = self.db_handler.filter_expenses(search_text, expense_type, from_date, to_date)
        self.update_expenses_table(expenses)
    
    def add_expense(self):
        """إضافة مصروف جديد"""
        dialog = ExpenseDialog(self.db_handler, self)
        
        if dialog.exec_() == QDialog.Accepted:
            expense_data = dialog.get_expense_data()
            
            # التحقق من صحة البيانات
            if not expense_data['description']:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال وصف المصروف")
                return
            
            if expense_data['amount'] <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح للمصروف")
                return
            
            # إضافة المصروف إلى قاعدة البيانات
            success = self.db_handler.add_expense(
                expense_data['expense_type'],
                expense_data['description'],
                expense_data['amount'],
                expense_data['date'],
                expense_data['notes']
            )
            
            if success:
                QMessageBox.information(self, "نجاح", "تمت إضافة المصروف بنجاح")
                self.load_expenses()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة المصروف")
    
    def edit_expense(self):
        """تعديل مصروف"""
        selected_items = self.expenses_table.selectedItems()
        
        if not selected_items:
            return
        
        expense_id = selected_items[0].data(Qt.UserRole)
        expense_data = self.db_handler.get_expense(expense_id)
        
        if not expense_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على المصروف")
            return
        
        dialog = ExpenseDialog(self.db_handler, self, expense_data)
        
        if dialog.exec_() == QDialog.Accepted:
            updated_data = dialog.get_expense_data()
            
            # التحقق من صحة البيانات
            if not updated_data['description']:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال وصف المصروف")
                return
            
            if updated_data['amount'] <= 0:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح للمصروف")
                return
            
            # تحديث المصروف في قاعدة البيانات
            success = self.db_handler.update_expense(
                expense_id,
                updated_data['expense_type'],
                updated_data['description'],
                updated_data['amount'],
                updated_data['date'],
                updated_data['notes']
            )
            
            if success:
                QMessageBox.information(self, "نجاح", "تم تحديث المصروف بنجاح")
                self.load_expenses()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحديث المصروف")
    
    def delete_expense(self):
        """حذف مصروف"""
        selected_items = self.expenses_table.selectedItems()
        
        if not selected_items:
            return
        
        expense_id = selected_items[0].data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من رغبتك في حذف هذا المصروف؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.db_handler.delete_expense(expense_id)
            
            if success:
                QMessageBox.information(self, "نجاح", "تم حذف المصروف بنجاح")
                self.load_expenses()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حذف المصروف")