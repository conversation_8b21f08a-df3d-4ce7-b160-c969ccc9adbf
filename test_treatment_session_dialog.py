#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نافذة جلسة المعالجة السنية
Test script for TreatmentSessionDialog improvements
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                             QVBoxLayout, QWidget, QLabel, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة المحسنة
from ui.tabs.dental_treatments_tab import TreatmentSessionDialog

class TestMainWindow(QMainWindow):
    """نافذة اختبار لنافذة جلسة المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار نافذة جلسة المعالجة السنية المحسنة")
        self.setGeometry(100, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان الاختبار
        title = QLabel("اختبار التحسينات على نافذة جلسة المعالجة السنية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف التحسينات
        description = QLabel("""
التحسينات المطبقة:
• تحسين محاذاة النص العربي (RTL) في جميع الحقول والتسميات
• تقليل ارتفاع حقل تفاصيل الإجراء إلى 80px (من 100px)
• تقليل ارتفاع الحقول والتسميات مع الحفاظ على الوضوح
• ترتيب الأزرار من اليمين إلى اليسار (حفظ، إلغاء، حذف)
• تحسين حجم النافذة إلى 1000×640 (من 1000×660)
• محاذاة جميع التسميات إلى اليمين بشكل مثالي
• ضمان العرض الصحيح من البداية دون تدخل المستخدم
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #3498db;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # أزرار الاختبار
        self.create_test_buttons(layout)
        
    def create_test_buttons(self, layout):
        """إنشاء أزرار الاختبار"""
        
        # زر اختبار نافذة جديدة
        new_session_btn = QPushButton("🆕 اختبار نافذة جلسة جديدة")
        new_session_btn.setStyleSheet(self.get_button_style("#28a745"))
        new_session_btn.clicked.connect(self.test_new_session_dialog)
        layout.addWidget(new_session_btn)
        
        # زر اختبار نافذة تعديل
        edit_session_btn = QPushButton("✏️ اختبار نافذة تعديل جلسة")
        edit_session_btn.setStyleSheet(self.get_button_style("#007bff"))
        edit_session_btn.clicked.connect(self.test_edit_session_dialog)
        layout.addWidget(edit_session_btn)
        
        # زر اختبار التحسينات
        optimization_test_btn = QPushButton("⚡ اختبار التحسينات والضغط")
        optimization_test_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        optimization_test_btn.clicked.connect(self.test_optimization)
        layout.addWidget(optimization_test_btn)
        
        layout.addStretch()
        
    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
        """
        
    def test_new_session_dialog(self):
        """اختبار نافذة جلسة جديدة"""
        try:
            dialog = TreatmentSessionDialog(
                plan_id=1,
                cost=50000,
                patient_id=1,
                parent=self
            )
            
            result = dialog.exec_()
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح", "تم حفظ الجلسة بنجاح!")
            else:
                QMessageBox.information(self, "ملغي", "تم إلغاء العملية")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح النافذة: {str(e)}")
            
    def test_edit_session_dialog(self):
        """اختبار نافذة تعديل جلسة"""
        try:
            # بيانات جلسة وهمية للاختبار
            session_data = {
                'id': 1,
                'session_date': '2025-07-21',
                'procedure': 'تنظيف الأسنان وإزالة الجير',
                'payment': 25000
            }
            
            dialog = TreatmentSessionDialog(
                plan_id=1,
                cost=50000,
                patient_id=1,
                session_data=session_data,
                parent=self
            )
            
            result = dialog.exec_()
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح", "تم تحديث الجلسة بنجاح!")
            else:
                QMessageBox.information(self, "ملغي", "تم إلغاء العملية")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح النافذة: {str(e)}")
            
    def test_optimization(self):
        """اختبار التحسينات والضغط"""
        QMessageBox.information(
            self,
            "اختبار التحسينات",
            "افتح نافذة الجلسة وتحقق من:\n"
            "• الحجم الجديد المضغوط أكثر (1000x640 بدلاً من 1000x660)\n"
            "• ارتفاع حقل تفاصيل الإجراء المحسن (80px بدلاً من 100px)\n"
            "• ارتفاع الحقول المحسن (40px بدلاً من 48px)\n"
            "• ارتفاع التسميات المحسن (22px بدلاً من 28px)\n"
            "• محاذاة جميع التسميات إلى اليمين بشكل مثالي\n"
            "• ترتيب الأزرار من اليمين: حفظ، إلغاء، حذف\n"
            "• محاذاة النص العربي في جميع الحقول\n"
            "• وضوح جميع العناصر رغم التحسين"
        )

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
