import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea,
                             QGridLayout, QDoubleSpinBox, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate, QDateTime
from PyQt5.QtGui import QIcon, QFont, QPainter, QPixmap
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog
from PyQt5.QtChart import QChart, QChartView, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis, QPieSeries

class PatientReportWidget(QWidget):
    """واجهة تقارير المريض"""
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # نموذج البحث
        search_group = QGroupBox("بحث عن مريض")
        search_layout = QVBoxLayout(search_group)
        
        # حقل البحث
        search_input_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("اسم المريض أو رقم الهاتف")
        self.search_button = QPushButton("بحث")
        self.search_button.clicked.connect(self.search_patients)
        
        search_input_layout.addWidget(self.search_input)
        search_input_layout.addWidget(self.search_button)
        
        search_layout.addLayout(search_input_layout)
        
        # جدول المرضى
        self.patients_table = QTableWidget()
        self.patients_table.setColumnCount(3)
        self.patients_table.setHorizontalHeaderLabels(["اسم المريض", "رقم الهاتف", "العمر"])
        self.patients_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.patients_table.verticalHeader().setVisible(False)
        self.patients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.patients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.patients_table.itemSelectionChanged.connect(self.on_patient_selected)
        
        search_layout.addWidget(self.patients_table)
        
        main_layout.addWidget(search_group)
        
        # نموذج التقرير
        report_group = QGroupBox("تقرير المريض")
        report_layout = QVBoxLayout(report_group)
        
        # فترة التقرير
        date_layout = QHBoxLayout()
        
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        
        self.generate_report_button = QPushButton("إنشاء التقرير")
        self.generate_report_button.clicked.connect(self.generate_patient_report)
        self.generate_report_button.setEnabled(False)
        
        self.print_report_button = QPushButton("طباعة التقرير")
        self.print_report_button.clicked.connect(self.print_patient_report)
        self.print_report_button.setEnabled(False)
        
        date_layout.addWidget(QLabel("من:"))
        date_layout.addWidget(self.from_date)
        date_layout.addWidget(QLabel("إلى:"))
        date_layout.addWidget(self.to_date)
        date_layout.addWidget(self.generate_report_button)
        date_layout.addWidget(self.print_report_button)
        
        report_layout.addLayout(date_layout)
        
        # جدول التقرير
        self.report_table = QTableWidget()
        self.report_table.setColumnCount(5)
        self.report_table.setHorizontalHeaderLabels(["التاريخ", "نوع المعالجة", "تفاصيل الجلسة", "المبلغ المدفوع", "الرصيد المتبقي"])
        self.report_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.report_table.verticalHeader().setVisible(False)
        self.report_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        report_layout.addWidget(self.report_table)
        
        # ملخص التقرير
        summary_layout = QHBoxLayout()
        
        self.total_cost_label = QLabel("إجمالي التكلفة: 0 ل.س")
        self.total_paid_label = QLabel("إجمالي المدفوع: 0 ل.س")
        self.total_remaining_label = QLabel("إجمالي المتبقي: 0 ل.س")
        
        for label in [self.total_cost_label, self.total_paid_label, self.total_remaining_label]:
            label.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        summary_layout.addWidget(self.total_cost_label)
        summary_layout.addWidget(self.total_paid_label)
        summary_layout.addWidget(self.total_remaining_label)
        
        report_layout.addLayout(summary_layout)
        
        main_layout.addWidget(report_group)
        
        # تعيين نسب التخطيط
        main_layout.setStretch(0, 1)  # نموذج البحث
        main_layout.setStretch(1, 2)  # نموذج التقرير
    
    def search_patients(self):
        """البحث عن المرضى"""
        search_text = self.search_input.text().strip()
        
        if not search_text:
            QMessageBox.warning(self, "تنبيه", "يرجى إدخال اسم المريض أو رقم الهاتف للبحث")
            return
        
        patients = self.db_handler.search_patients(search_text)
        
        self.patients_table.setRowCount(0)
        
        for row, patient in enumerate(patients):
            self.patients_table.insertRow(row)
            
            # اسم المريض
            name_item = QTableWidgetItem(patient.get('name', ''))
            name_item.setData(Qt.UserRole, patient['id'])  # تخزين معرف المريض
            self.patients_table.setItem(row, 0, name_item)
            
            # رقم الهاتف
            phone_item = QTableWidgetItem(patient.get('phone', ''))
            self.patients_table.setItem(row, 1, phone_item)
            
            # العمر
            age = patient.get('age', 0)
            age_item = QTableWidgetItem(str(age))
            self.patients_table.setItem(row, 2, age_item)
    
    def on_patient_selected(self):
        """معالجة تحديد مريض"""
        selected_items = self.patients_table.selectedItems()
        
        if selected_items:
            self.generate_report_button.setEnabled(True)
        else:
            self.generate_report_button.setEnabled(False)
            self.print_report_button.setEnabled(False)
    
    def generate_patient_report(self):
        """إنشاء تقرير المريض"""
        selected_items = self.patients_table.selectedItems()
        
        if not selected_items:
            return
        
        patient_id = selected_items[0].data(Qt.UserRole)
        patient_name = selected_items[0].text()
        
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # الحصول على بيانات التقرير
        report_data = self.db_handler.get_patient_report(patient_id, from_date, to_date)
        
        # تحديث جدول التقرير
        self.report_table.setRowCount(0)
        
        total_cost = 0
        total_paid = 0
        total_remaining = 0
        
        for row, session in enumerate(report_data):
            self.report_table.insertRow(row)
            
            # التاريخ
            date_item = QTableWidgetItem(session.get('date', ''))
            self.report_table.setItem(row, 0, date_item)
            
            # نوع المعالجة
            treatment_type_item = QTableWidgetItem(session.get('treatment_type', ''))
            self.report_table.setItem(row, 1, treatment_type_item)
            
            # تفاصيل الجلسة
            details_item = QTableWidgetItem(session.get('details', ''))
            self.report_table.setItem(row, 2, details_item)
            
            # المبلغ المدفوع
            payment = session.get('payment', 0)
            payment_item = QTableWidgetItem(f"{payment} ل.س")
            self.report_table.setItem(row, 3, payment_item)
            
            # الرصيد المتبقي
            remaining = session.get('remaining', 0)
            remaining_item = QTableWidgetItem(f"{remaining} ل.س")
            self.report_table.setItem(row, 4, remaining_item)
            
            # تحديث الإجماليات
            total_cost += session.get('total_cost', 0)
            total_paid += payment
            total_remaining = total_cost - total_paid
        
        # تحديث ملخص التقرير
        self.total_cost_label.setText(f"إجمالي التكلفة: {total_cost} ل.س")
        self.total_paid_label.setText(f"إجمالي المدفوع: {total_paid} ل.س")
        self.total_remaining_label.setText(f"إجمالي المتبقي: {total_remaining} ل.س")
        
        # تمكين زر الطباعة
        self.print_report_button.setEnabled(True)
    
    def print_patient_report(self):
        """طباعة تقرير المريض"""
        if self.report_table.rowCount() == 0:
            QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للطباعة")
            return
        
        selected_items = self.patients_table.selectedItems()
        patient_name = selected_items[0].text()
        
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # إنشاء مستند HTML للطباعة
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المريض</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 10px; }}
                .info {{ margin-bottom: 20px; }}
                .info-row {{ display: flex; margin-bottom: 5px; }}
                .info-label {{ font-weight: bold; width: 120px; }}
                .info-value {{ flex: 1; }}
                .table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .table th {{ background-color: #f2f2f2; }}
                .summary {{ margin-top: 20px; }}
                .summary-item {{ font-weight: bold; margin-bottom: 5px; }}
                .footer {{ margin-top: 30px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">تقرير المريض</div>
                <div>العيادة السنية</div>
            </div>
            
            <div class="info">
                <div class="info-row">
                    <div class="info-label">اسم المريض:</div>
                    <div class="info-value">{patient_name}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">الفترة:</div>
                    <div class="info-value">من {from_date} إلى {to_date}</div>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>نوع المعالجة</th>
                        <th>تفاصيل الجلسة</th>
                        <th>المبلغ المدفوع</th>
                        <th>الرصيد المتبقي</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # إضافة صفوف الجدول
        for row in range(self.report_table.rowCount()):
            date = self.report_table.item(row, 0).text()
            treatment_type = self.report_table.item(row, 1).text()
            details = self.report_table.item(row, 2).text()
            payment = self.report_table.item(row, 3).text()
            remaining = self.report_table.item(row, 4).text()
            
            html += f"""
                    <tr>
                        <td>{date}</td>
                        <td>{treatment_type}</td>
                        <td>{details}</td>
                        <td>{payment}</td>
                        <td>{remaining}</td>
                    </tr>
            """
        
        # إضافة ملخص التقرير
        total_cost = self.total_cost_label.text().split(":")[1].strip()
        total_paid = self.total_paid_label.text().split(":")[1].strip()
        total_remaining = self.total_remaining_label.text().split(":")[1].strip()
        
        html += f"""
                </tbody>
            </table>
            
            <div class="summary">
                <div class="summary-item">{self.total_cost_label.text()}</div>
                <div class="summary-item">{self.total_paid_label.text()}</div>
                <div class="summary-item">{self.total_remaining_label.text()}</div>
            </div>
            
            <div class="footer">
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </body>
        </html>
        """
        
        # طباعة المستند
        self.print_html_document(html, "تقرير المريض")
    
    def print_html_document(self, html, title):
        """طباعة مستند HTML"""
        from PyQt5.QtWebEngineWidgets import QWebEngineView
        
        # إنشاء عرض الويب
        web_view = QWebEngineView()
        web_view.setHtml(html)
        
        # إعداد الطابعة
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        printer.setOutputFormat(QPrinter.NativeFormat)
        
        # عرض نافذة معاينة الطباعة
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(web_view.print_)
        preview.exec_()

class LabReportWidget(QWidget):
    """واجهة تقارير المخبر"""
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # نموذج التقرير
        filter_group = QGroupBox("تصفية التقرير")
        filter_layout = QHBoxLayout(filter_group)
        
        # اختيار المخبر
        self.lab_combo = QComboBox()
        self.lab_combo.addItem("جميع المخابر", None)
        
        # تحميل المخابر
        labs = self.db_handler.get_labs()
        for lab in labs:
            self.lab_combo.addItem(lab['name'], lab['id'])
        
        # فترة التقرير
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        
        self.generate_report_button = QPushButton("إنشاء التقرير")
        self.generate_report_button.clicked.connect(self.generate_lab_report)
        
        self.print_report_button = QPushButton("طباعة التقرير")
        self.print_report_button.clicked.connect(self.print_lab_report)
        self.print_report_button.setEnabled(False)
        
        filter_layout.addWidget(QLabel("المخبر:"))
        filter_layout.addWidget(self.lab_combo)
        filter_layout.addWidget(QLabel("من:"))
        filter_layout.addWidget(self.from_date)
        filter_layout.addWidget(QLabel("إلى:"))
        filter_layout.addWidget(self.to_date)
        filter_layout.addWidget(self.generate_report_button)
        filter_layout.addWidget(self.print_report_button)
        
        main_layout.addWidget(filter_group)
        
        # جدول التقرير
        self.report_table = QTableWidget()
        self.report_table.setColumnCount(6)
        self.report_table.setHorizontalHeaderLabels(["المخبر", "نوع العمل", "عدد الطلبات", "عدد القطع", "إجمالي المبلغ", "حالة الدفع"])
        self.report_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.report_table.verticalHeader().setVisible(False)
        self.report_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        main_layout.addWidget(self.report_table)
        
        # ملخص التقرير
        summary_group = QGroupBox("ملخص التقرير")
        summary_layout = QHBoxLayout(summary_group)
        
        # مخطط بياني للمخابر
        self.chart_view = QChartView()
        self.chart_view.setRenderHint(QPainter.Antialiasing)
        
        # ملخص المبالغ
        amounts_layout = QVBoxLayout()
        
        self.total_amount_label = QLabel("إجمالي المبالغ: 0 ل.س")
        self.paid_amount_label = QLabel("المبالغ المدفوعة: 0 ل.س")
        self.remaining_amount_label = QLabel("المبالغ المتبقية: 0 ل.س")
        
        for label in [self.total_amount_label, self.paid_amount_label, self.remaining_amount_label]:
            label.setStyleSheet("font-weight: bold; font-size: 14px;")
            amounts_layout.addWidget(label)
        
        amounts_layout.addStretch()
        
        summary_layout.addWidget(self.chart_view, 2)
        summary_layout.addLayout(amounts_layout, 1)
        
        main_layout.addWidget(summary_group)
        
        # تعيين نسب التخطيط
        main_layout.setStretch(0, 0)  # نموذج التصفية
        main_layout.setStretch(1, 1)  # جدول التقرير
        main_layout.setStretch(2, 1)  # ملخص التقرير
    
    def generate_lab_report(self):
        """إنشاء تقرير المخبر"""
        lab_id = self.lab_combo.currentData()
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # الحصول على بيانات التقرير
        report_data = self.db_handler.get_lab_report(lab_id, from_date, to_date)
        
        # تحديث جدول التقرير
        self.report_table.setRowCount(0)
        
        total_amount = 0
        paid_amount = 0
        
        # بيانات المخطط البياني
        lab_data = {}
        
        for row, item in enumerate(report_data):
            self.report_table.insertRow(row)
            
            # المخبر
            lab_name = item.get('lab_name', '')
            lab_item = QTableWidgetItem(lab_name)
            self.report_table.setItem(row, 0, lab_item)
            
            # نوع العمل
            work_type_item = QTableWidgetItem(item.get('work_type_name', ''))
            self.report_table.setItem(row, 1, work_type_item)
            
            # عدد الطلبات
            orders_count_item = QTableWidgetItem(str(item.get('orders_count', 0)))
            self.report_table.setItem(row, 2, orders_count_item)
            
            # عدد القطع
            pieces_count_item = QTableWidgetItem(str(item.get('pieces_count', 0)))
            self.report_table.setItem(row, 3, pieces_count_item)
            
            # إجمالي المبلغ
            amount = item.get('total_amount', 0)
            amount_item = QTableWidgetItem(f"{amount} ل.س")
            self.report_table.setItem(row, 4, amount_item)
            
            # حالة الدفع
            paid = item.get('paid_amount', 0)
            remaining = amount - paid
            payment_status = "مدفوع بالكامل" if remaining <= 0 else f"متبقي {remaining} ل.س"
            payment_status_item = QTableWidgetItem(payment_status)
            self.report_table.setItem(row, 5, payment_status_item)
            
            # تحديث الإجماليات
            total_amount += amount
            paid_amount += paid
            
            # تحديث بيانات المخطط البياني
            if lab_name not in lab_data:
                lab_data[lab_name] = 0
            lab_data[lab_name] += amount
        
        # تحديث ملخص التقرير
        remaining_amount = total_amount - paid_amount
        
        self.total_amount_label.setText(f"إجمالي المبالغ: {total_amount} ل.س")
        self.paid_amount_label.setText(f"المبالغ المدفوعة: {paid_amount} ل.س")
        self.remaining_amount_label.setText(f"المبالغ المتبقية: {remaining_amount} ل.س")
        
        # إنشاء المخطط البياني
        self.create_lab_chart(lab_data)
        
        # تمكين زر الطباعة
        self.print_report_button.setEnabled(True)
    
    def create_lab_chart(self, lab_data):
        """إنشاء مخطط بياني للمخابر"""
        # إنشاء مخطط دائري
        pie_series = QPieSeries()
        
        for lab_name, amount in lab_data.items():
            slice = pie_series.append(f"{lab_name} ({amount} ل.س)", amount)
            slice.setLabelVisible(True)
        
        chart = QChart()
        chart.addSeries(pie_series)
        chart.setTitle("توزيع المبالغ حسب المخبر")
        chart.setAnimationOptions(QChart.SeriesAnimations)
        chart.legend().setAlignment(Qt.AlignRight)
        
        self.chart_view.setChart(chart)
    
    def print_lab_report(self):
        """طباعة تقرير المخبر"""
        if self.report_table.rowCount() == 0:
            QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للطباعة")
            return
        
        lab_name = self.lab_combo.currentText()
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # إنشاء مستند HTML للطباعة
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخبر</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 10px; }}
                .info {{ margin-bottom: 20px; }}
                .info-row {{ display: flex; margin-bottom: 5px; }}
                .info-label {{ font-weight: bold; width: 120px; }}
                .info-value {{ flex: 1; }}
                .table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .table th {{ background-color: #f2f2f2; }}
                .summary {{ margin-top: 20px; }}
                .summary-item {{ font-weight: bold; margin-bottom: 5px; }}
                .footer {{ margin-top: 30px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">تقرير المخبر</div>
                <div>العيادة السنية</div>
            </div>
            
            <div class="info">
                <div class="info-row">
                    <div class="info-label">المخبر:</div>
                    <div class="info-value">{lab_name}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">الفترة:</div>
                    <div class="info-value">من {from_date} إلى {to_date}</div>
                </div>
            </div>
            
            <table class="table">
                <thead>
                    <tr>
                        <th>المخبر</th>
                        <th>نوع العمل</th>
                        <th>عدد الطلبات</th>
                        <th>عدد القطع</th>
                        <th>إجمالي المبلغ</th>
                        <th>حالة الدفع</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        # إضافة صفوف الجدول
        for row in range(self.report_table.rowCount()):
            lab = self.report_table.item(row, 0).text()
            work_type = self.report_table.item(row, 1).text()
            orders_count = self.report_table.item(row, 2).text()
            pieces_count = self.report_table.item(row, 3).text()
            amount = self.report_table.item(row, 4).text()
            payment_status = self.report_table.item(row, 5).text()
            
            html += f"""
                    <tr>
                        <td>{lab}</td>
                        <td>{work_type}</td>
                        <td>{orders_count}</td>
                        <td>{pieces_count}</td>
                        <td>{amount}</td>
                        <td>{payment_status}</td>
                    </tr>
            """
        
        # إضافة ملخص التقرير
        html += f"""
                </tbody>
            </table>
            
            <div class="summary">
                <div class="summary-item">{self.total_amount_label.text()}</div>
                <div class="summary-item">{self.paid_amount_label.text()}</div>
                <div class="summary-item">{self.remaining_amount_label.text()}</div>
            </div>
            
            <div class="footer">
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </body>
        </html>
        """
        
        # طباعة المستند
        self.print_html_document(html, "تقرير المخبر")
    
    def print_html_document(self, html, title):
        """طباعة مستند HTML"""
        from PyQt5.QtWebEngineWidgets import QWebEngineView
        
        # إنشاء عرض الويب
        web_view = QWebEngineView()
        web_view.setHtml(html)
        
        # إعداد الطابعة
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        printer.setOutputFormat(QPrinter.NativeFormat)
        
        # عرض نافذة معاينة الطباعة
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(web_view.print_)
        preview.exec_()

class ClinicReportWidget(QWidget):
    """واجهة تقارير العيادة"""
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # نموذج التقرير
        filter_group = QGroupBox("تصفية التقرير")
        filter_layout = QHBoxLayout(filter_group)
        
        # نوع التقرير
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["تقرير يومي", "تقرير أسبوعي", "تقرير شهري", "تقرير سنوي", "تقرير مخصص"])
        self.report_type_combo.currentIndexChanged.connect(self.on_report_type_changed)
        
        # فترة التقرير
        self.from_date = QDateEdit()
        self.from_date.setCalendarPopup(True)
        self.from_date.setDate(QDate.currentDate().addDays(-30))
        
        self.to_date = QDateEdit()
        self.to_date.setCalendarPopup(True)
        self.to_date.setDate(QDate.currentDate())
        
        self.generate_report_button = QPushButton("إنشاء التقرير")
        self.generate_report_button.clicked.connect(self.generate_clinic_report)
        
        self.print_report_button = QPushButton("طباعة التقرير")
        self.print_report_button.clicked.connect(self.print_clinic_report)
        self.print_report_button.setEnabled(False)
        
        filter_layout.addWidget(QLabel("نوع التقرير:"))
        filter_layout.addWidget(self.report_type_combo)
        filter_layout.addWidget(QLabel("من:"))
        filter_layout.addWidget(self.from_date)
        filter_layout.addWidget(QLabel("إلى:"))
        filter_layout.addWidget(self.to_date)
        filter_layout.addWidget(self.generate_report_button)
        filter_layout.addWidget(self.print_report_button)
        
        main_layout.addWidget(filter_group)
        
        # تبويبات التقرير
        self.report_tabs = QTabWidget()
        
        # تبويبة الوارد
        self.income_tab = QWidget()
        income_layout = QVBoxLayout(self.income_tab)
        
        self.income_table = QTableWidget()
        self.income_table.setColumnCount(4)
        self.income_table.setHorizontalHeaderLabels(["التاريخ", "المصدر", "الوصف", "المبلغ"])
        self.income_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.income_table.verticalHeader().setVisible(False)
        self.income_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        income_layout.addWidget(self.income_table)
        
        # تبويبة المصروفات
        self.expenses_tab = QWidget()
        expenses_layout = QVBoxLayout(self.expenses_tab)
        
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(4)
        self.expenses_table.setHorizontalHeaderLabels(["التاريخ", "النوع", "الوصف", "المبلغ"])
        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.verticalHeader().setVisible(False)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        expenses_layout.addWidget(self.expenses_table)
        
        # تبويبة المخبر
        self.lab_tab = QWidget()
        lab_layout = QVBoxLayout(self.lab_tab)
        
        self.lab_table = QTableWidget()
        self.lab_table.setColumnCount(4)
        self.lab_table.setHorizontalHeaderLabels(["التاريخ", "المخبر", "نوع العمل", "المبلغ"])
        self.lab_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.lab_table.verticalHeader().setVisible(False)
        self.lab_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        lab_layout.addWidget(self.lab_table)
        
        # تبويبة الأرباح
        self.profit_tab = QWidget()
        profit_layout = QVBoxLayout(self.profit_tab)
        
        # مخطط بياني للأرباح
        self.profit_chart_view = QChartView()
        self.profit_chart_view.setRenderHint(QPainter.Antialiasing)
        
        profit_layout.addWidget(self.profit_chart_view)
        
        # إضافة التبويبات
        self.report_tabs.addTab(self.income_tab, "الوارد")
        self.report_tabs.addTab(self.expenses_tab, "المصروفات")
        self.report_tabs.addTab(self.lab_tab, "المخبر")
        self.report_tabs.addTab(self.profit_tab, "الأرباح")
        
        main_layout.addWidget(self.report_tabs)
        
        # ملخص التقرير
        summary_layout = QHBoxLayout()
        
        self.total_income_label = QLabel("إجمالي الوارد: 0 ل.س")
        self.total_expenses_label = QLabel("إجمالي المصروفات: 0 ل.س")
        self.total_lab_expenses_label = QLabel("إجمالي مصروفات المخبر: 0 ل.س")
        self.total_profit_label = QLabel("صافي الربح: 0 ل.س")
        
        for label in [self.total_income_label, self.total_expenses_label, self.total_lab_expenses_label, self.total_profit_label]:
            label.setStyleSheet("font-weight: bold; font-size: 14px;")
            summary_layout.addWidget(label)
        
        main_layout.addLayout(summary_layout)
        
        # تعيين نسب التخطيط
        main_layout.setStretch(0, 0)  # نموذج التصفية
        main_layout.setStretch(1, 3)  # تبويبات التقرير
    
    def on_report_type_changed(self):
        """معالجة تغيير نوع التقرير"""
        report_type = self.report_type_combo.currentText()
        today = QDate.currentDate()
        
        if report_type == "تقرير يومي":
            self.from_date.setDate(today)
            self.to_date.setDate(today)
        elif report_type == "تقرير أسبوعي":
            self.from_date.setDate(today.addDays(-today.dayOfWeek() + 1))
            self.to_date.setDate(today)
        elif report_type == "تقرير شهري":
            self.from_date.setDate(QDate(today.year(), today.month(), 1))
            self.to_date.setDate(today)
        elif report_type == "تقرير سنوي":
            self.from_date.setDate(QDate(today.year(), 1, 1))
            self.to_date.setDate(today)
    
    def generate_clinic_report(self):
        """إنشاء تقرير العيادة"""
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # الحصول على بيانات التقرير
        income_data = self.db_handler.get_income_report(from_date, to_date)
        expenses_data = self.db_handler.get_expenses_report(from_date, to_date)
        lab_expenses_data = self.db_handler.get_lab_expenses_report(from_date, to_date)
        
        # تحديث جدول الوارد
        self.income_table.setRowCount(0)
        total_income = 0
        
        for row, item in enumerate(income_data):
            self.income_table.insertRow(row)
            
            # التاريخ
            date_item = QTableWidgetItem(item.get('date', ''))
            self.income_table.setItem(row, 0, date_item)
            
            # المصدر
            source_item = QTableWidgetItem(item.get('source', ''))
            self.income_table.setItem(row, 1, source_item)
            
            # الوصف
            description_item = QTableWidgetItem(item.get('description', ''))
            self.income_table.setItem(row, 2, description_item)
            
            # المبلغ
            amount = item.get('amount', 0)
            amount_item = QTableWidgetItem(f"{amount} ل.س")
            self.income_table.setItem(row, 3, amount_item)
            
            total_income += amount
        
        # تحديث جدول المصروفات
        self.expenses_table.setRowCount(0)
        total_expenses = 0
        
        for row, item in enumerate(expenses_data):
            self.expenses_table.insertRow(row)
            
            # التاريخ
            date_item = QTableWidgetItem(item.get('date', ''))
            self.expenses_table.setItem(row, 0, date_item)
            
            # النوع
            type_item = QTableWidgetItem(item.get('expense_type', ''))
            self.expenses_table.setItem(row, 1, type_item)
            
            # الوصف
            description_item = QTableWidgetItem(item.get('description', ''))
            self.expenses_table.setItem(row, 2, description_item)
            
            # المبلغ
            amount = item.get('amount', 0)
            amount_item = QTableWidgetItem(f"{amount} ل.س")
            self.expenses_table.setItem(row, 3, amount_item)
            
            total_expenses += amount
        
        # تحديث جدول المخبر
        self.lab_table.setRowCount(0)
        total_lab_expenses = 0
        
        for row, item in enumerate(lab_expenses_data):
            self.lab_table.insertRow(row)
            
            # التاريخ
            date_item = QTableWidgetItem(item.get('date', ''))
            self.lab_table.setItem(row, 0, date_item)
            
            # المخبر
            lab_item = QTableWidgetItem(item.get('lab_name', ''))
            self.lab_table.setItem(row, 1, lab_item)
            
            # نوع العمل
            work_type_item = QTableWidgetItem(item.get('work_type_name', ''))
            self.lab_table.setItem(row, 2, work_type_item)
            
            # المبلغ
            amount = item.get('amount', 0)
            amount_item = QTableWidgetItem(f"{amount} ل.س")
            self.lab_table.setItem(row, 3, amount_item)
            
            total_lab_expenses += amount
        
        # حساب صافي الربح
        total_profit = total_income - total_expenses - total_lab_expenses
        
        # تحديث ملخص التقرير
        self.total_income_label.setText(f"إجمالي الوارد: {total_income} ل.س")
        self.total_expenses_label.setText(f"إجمالي المصروفات: {total_expenses} ل.س")
        self.total_lab_expenses_label.setText(f"إجمالي مصروفات المخبر: {total_lab_expenses} ل.س")
        self.total_profit_label.setText(f"صافي الربح: {total_profit} ل.س")
        
        # إنشاء مخطط الأرباح
        self.create_profit_chart(total_income, total_expenses, total_lab_expenses, total_profit)
        
        # تمكين زر الطباعة
        self.print_report_button.setEnabled(True)
    
    def create_profit_chart(self, income, expenses, lab_expenses, profit):
        """إنشاء مخطط بياني للأرباح"""
        # إنشاء مخطط أعمدة
        bar_set = QBarSet("المبالغ")
        bar_set.append([income, expenses, lab_expenses, profit])
        
        bar_series = QBarSeries()
        bar_series.append(bar_set)
        
        chart = QChart()
        chart.addSeries(bar_series)
        chart.setTitle("ملخص الأرباح")
        chart.setAnimationOptions(QChart.SeriesAnimations)
        
        categories = ["الوارد", "المصروفات", "مصروفات المخبر", "صافي الربح"]
        axis_x = QBarCategoryAxis()
        axis_x.append(categories)
        chart.addAxis(axis_x, Qt.AlignBottom)
        bar_series.attachAxis(axis_x)
        
        axis_y = QValueAxis()
        axis_y.setRange(0, max(income, expenses, lab_expenses, profit) * 1.1)
        chart.addAxis(axis_y, Qt.AlignLeft)
        bar_series.attachAxis(axis_y)
        
        chart.legend().setVisible(True)
        chart.legend().setAlignment(Qt.AlignBottom)
        
        self.profit_chart_view.setChart(chart)
    
    def print_clinic_report(self):
        """طباعة تقرير العيادة"""
        if self.income_table.rowCount() == 0 and self.expenses_table.rowCount() == 0 and self.lab_table.rowCount() == 0:
            QMessageBox.warning(self, "تنبيه", "لا توجد بيانات للطباعة")
            return
        
        report_type = self.report_type_combo.currentText()
        from_date = self.from_date.date().toString("yyyy-MM-dd")
        to_date = self.to_date.date().toString("yyyy-MM-dd")
        
        # إنشاء مستند HTML للطباعة
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير العيادة</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; direction: rtl; }}
                .header {{ text-align: center; margin-bottom: 20px; }}
                .title {{ font-size: 24px; font-weight: bold; margin-bottom: 10px; }}
                .info {{ margin-bottom: 20px; }}
                .info-row {{ display: flex; margin-bottom: 5px; }}
                .info-label {{ font-weight: bold; width: 120px; }}
                .info-value {{ flex: 1; }}
                .section {{ margin-bottom: 30px; }}
                .section-title {{ font-size: 18px; font-weight: bold; margin-bottom: 10px; }}
                .table {{ width: 100%; border-collapse: collapse; margin-bottom: 20px; }}
                .table th, .table td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                .table th {{ background-color: #f2f2f2; }}
                .summary {{ margin-top: 20px; }}
                .summary-item {{ font-weight: bold; margin-bottom: 5px; }}
                .footer {{ margin-top: 30px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">تقرير العيادة</div>
                <div>العيادة السنية</div>
            </div>
            
            <div class="info">
                <div class="info-row">
                    <div class="info-label">نوع التقرير:</div>
                    <div class="info-value">{report_type}</div>
                </div>
                <div class="info-row">
                    <div class="info-label">الفترة:</div>
                    <div class="info-value">من {from_date} إلى {to_date}</div>
                </div>
            </div>
        """
        
        # إضافة قسم الوارد
        if self.income_table.rowCount() > 0:
            html += f"""
            <div class="section">
                <div class="section-title">الوارد</div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المصدر</th>
                            <th>الوصف</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            
            for row in range(self.income_table.rowCount()):
                date = self.income_table.item(row, 0).text()
                source = self.income_table.item(row, 1).text()
                description = self.income_table.item(row, 2).text()
                amount = self.income_table.item(row, 3).text()
                
                html += f"""
                        <tr>
                            <td>{date}</td>
                            <td>{source}</td>
                            <td>{description}</td>
                            <td>{amount}</td>
                        </tr>
                """
            
            html += f"""
                    </tbody>
                </table>
            </div>
            """
        
        # إضافة قسم المصروفات
        if self.expenses_table.rowCount() > 0:
            html += f"""
            <div class="section">
                <div class="section-title">المصروفات</div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>النوع</th>
                            <th>الوصف</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            
            for row in range(self.expenses_table.rowCount()):
                date = self.expenses_table.item(row, 0).text()
                expense_type = self.expenses_table.item(row, 1).text()
                description = self.expenses_table.item(row, 2).text()
                amount = self.expenses_table.item(row, 3).text()
                
                html += f"""
                        <tr>
                            <td>{date}</td>
                            <td>{expense_type}</td>
                            <td>{description}</td>
                            <td>{amount}</td>
                        </tr>
                """
            
            html += f"""
                    </tbody>
                </table>
            </div>
            """
        
        # إضافة قسم المخبر
        if self.lab_table.rowCount() > 0:
            html += f"""
            <div class="section">
                <div class="section-title">مصروفات المخبر</div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المخبر</th>
                            <th>نوع العمل</th>
                            <th>المبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
            """
            
            for row in range(self.lab_table.rowCount()):
                date = self.lab_table.item(row, 0).text()
                lab = self.lab_table.item(row, 1).text()
                work_type = self.lab_table.item(row, 2).text()
                amount = self.lab_table.item(row, 3).text()
                
                html += f"""
                        <tr>
                            <td>{date}</td>
                            <td>{lab}</td>
                            <td>{work_type}</td>
                            <td>{amount}</td>
                        </tr>
                """
            
            html += f"""
                    </tbody>
                </table>
            </div>
            """
        
        # إضافة ملخص التقرير
        html += f"""
            <div class="summary">
                <div class="summary-item">{self.total_income_label.text()}</div>
                <div class="summary-item">{self.total_expenses_label.text()}</div>
                <div class="summary-item">{self.total_lab_expenses_label.text()}</div>
                <div class="summary-item">{self.total_profit_label.text()}</div>
            </div>
            
            <div class="footer">
                <p>تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
            </div>
        </body>
        </html>
        """
        
        # طباعة المستند
        self.print_html_document(html, "تقرير العيادة")
    
    def print_html_document(self, html, title):
        """طباعة مستند HTML"""
        from PyQt5.QtWebEngineWidgets import QWebEngineView
        
        # إنشاء عرض الويب
        web_view = QWebEngineView()
        web_view.setHtml(html)
        
        # إعداد الطابعة
        printer = QPrinter(QPrinter.HighResolution)
        printer.setPageSize(QPrinter.A4)
        printer.setOutputFormat(QPrinter.NativeFormat)
        
        # عرض نافذة معاينة الطباعة
        preview = QPrintPreviewDialog(printer, self)
        preview.paintRequested.connect(web_view.print_)
        preview.exec_()

class ReportsTab(QWidget):
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # تبويبات التقارير
        self.reports_tabs = QTabWidget()

        # تبويبة تقارير المريض
        self.patient_report_widget = PatientReportWidget(self.db_handler)
        self.reports_tabs.addTab(self.patient_report_widget, "تقارير المريض")

        # تبويبة تقارير المخبر
        self.lab_report_widget = LabReportWidget(self.db_handler)
        self.reports_tabs.addTab(self.lab_report_widget, "تقارير المخبر")

        # تبويبة تقارير العيادة
        self.clinic_report_widget = ClinicReportWidget(self.db_handler)
        self.reports_tabs.addTab(self.clinic_report_widget, "تقارير العيادة")

        main_layout.addWidget(self.reports_tabs)