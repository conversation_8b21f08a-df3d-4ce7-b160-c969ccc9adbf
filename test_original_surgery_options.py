#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار استعادة خيارات الجراحة الأصلية مع الحفاظ على التحسينات
Test restoring original surgery options while maintaining improvements
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class OriginalSurgeryOptionsTestWindow(QMainWindow):
    """نافذة اختبار خيارات الجراحة الأصلية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار استعادة خيارات الجراحة الأصلية مع الحفاظ على التحسينات")
        self.setGeometry(50, 50, 1800, 1000)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار استعادة خيارات الجراحة الأصلية مع الحفاظ على التحسينات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التعديل
        info_label = QLabel("""
        🎯 التعديل المطبق - استعادة خيارات الجراحة الأصلية:
        
        ✅ خيارات الجراحة الأصلية المستعادة:
        • قلع بسيط - 30,000 ل.س
        • قلع جراحي - 75,000 ل.س
        • منحصرة - 100,000 ل.س
        • منطمرة - 120,000 ل.س
        • تطويل تاج - 80,000 ل.س
        • قطع ذروة - 90,000 ل.س
        • تضحيك - 150,000 ل.س
        
        ✅ ما تم الحفاظ عليه من التحسينات:
        • التخطيط الأفقي للمجموعات الثمانية
        • الترتيب الجديد: [لبية] [أسعار اللبية] [ترميمية] [أسعار الترميمية] [تيجان] [أسعار التيجان] [جراحة] [أسعار الجراحة]
        • زر "تعديل أسعار علاج الأسنان" ونافذة تعديل الأسعار المنبثقة
        • نظام الحساب التلقائي للكلفة
        • جميع تحسينات الأبعاد والتنسيق البصري (160-170px عرض)
        • توزيع متساوي للمجموعات الثمانية
        
        ✅ ما تم تعديله:
        • خيارات الجراحة في دالة create_surgery_group()
        • أسعار الجراحة في دالة create_surgery_prices_group()
        • خيارات الجراحة في نافذة تعديل الأسعار (TreatmentPricesEditWidget)
        • الأسعار الافتراضية في دالة reset_to_defaults()
        • ضمان تطابق مفاتيح مربعات الاختيار مع مفاتيح حقول الأسعار
        
        🔧 التحقق من صحة التطابق:
        
        مربعات الاختيار (surgery_*):
        • surgery_قلع بسيط
        • surgery_قلع جراحي
        • surgery_منحصرة
        • surgery_منطمرة
        • surgery_تطويل تاج
        • surgery_قطع ذروة
        • surgery_تضحيك
        
        حقول الأسعار (surgery_*):
        • surgery_قلع بسيط
        • surgery_قلع جراحي
        • surgery_منحصرة
        • surgery_منطمرة
        • surgery_تطويل تاج
        • surgery_قطع ذروة
        • surgery_تضحيك
        
        ✅ النتيجة: تطابق كامل 100% ✅
        
        📊 مقارنة قبل وبعد التعديل:
        
        قبل التعديل (المحدث):
        • خلع بسيط، خلع جراحي، خلع ضرس عقل، تضحيك، زرع، رفع جيب، تطعيم عظم
        
        بعد التعديل (الأصلي):
        • قلع بسيط، قلع جراحي، منحصرة، منطمرة، تطويل تاج، قطع ذروة، تضحيك
        
        🎨 الفوائد المحققة:
        • استعادة الخيارات الأصلية المطلوبة
        • الحفاظ على جميع التحسينات المطبقة مسبقاً
        • ضمان عمل الحساب التلقائي بدقة 100%
        • تطابق كامل بين مفاتيح الخيارات والأسعار
        • استمرار عمل نظام إدارة الأسعار الافتراضية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 10px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان
        treatment_title = QLabel("⚙️ تبويبة علاج الأسنان (مع خيارات الجراحة الأصلية)")
        treatment_title.setAlignment(Qt.AlignCenter)
        treatment_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(treatment_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab()
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار خيارات الجراحة الأصلية
        test_original_surgery_btn = QPushButton("اختبار خيارات الجراحة الأصلية")
        test_original_surgery_btn.clicked.connect(self.test_original_surgery_options)
        test_original_surgery_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(test_original_surgery_btn)
        
        # زر اختبار الحساب التلقائي
        test_calculation_btn = QPushButton("اختبار الحساب التلقائي")
        test_calculation_btn.clicked.connect(self.test_automatic_calculation)
        test_calculation_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_calculation_btn)
        
        # زر فتح نافذة تعديل الأسعار
        edit_prices_btn = QPushButton("فتح نافذة تعديل الأسعار")
        edit_prices_btn.clicked.connect(self.open_prices_editor)
        edit_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(edit_prices_btn)
        
        # زر اختبار جميع الفئات
        test_all_btn = QPushButton("اختبار جميع الفئات")
        test_all_btn.clicked.connect(self.test_all_categories)
        test_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_all_btn)
        
        # زر مسح جميع التحديدات
        clear_all_btn = QPushButton("مسح جميع التحديدات")
        clear_all_btn.clicked.connect(self.clear_all_selections)
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        buttons_layout.addWidget(clear_all_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار خيارات الجراحة الأصلية:
        
        🔍 ما يجب ملاحظته:
        • خيارات الجراحة الأصلية السبعة في مجموعة الجراحة
        • الأسعار المطابقة في مجموعة أسعار الجراحة
        • الحفاظ على الترتيب الجديد للمجموعات الثمانية
        • وجود زر "تعديل أسعار علاج الأسنان" في مجموعة الأزرار
        • عمل الحساب التلقائي للكلفة بدقة
        
        🧪 اختبارات يمكن إجراؤها:
        • اختبر "خيارات الجراحة الأصلية" لتحديد الخيارات السبعة
        • اختبر "الحساب التلقائي" للتأكد من دقة الحساب
        • افتح "نافذة تعديل الأسعار" وتحقق من خيارات الجراحة الأصلية
        • اختبر "جميع الفئات" للتأكد من عمل جميع المجموعات
        • لاحظ التحديث الفوري للكلفة عند تحديد/إلغاء تحديد الخيارات
        
        ✅ النتائج المتوقعة:
        • عرض خيارات الجراحة الأصلية السبعة
        • حساب دقيق 100% لجميع خيارات الجراحة
        • عمل نافذة تعديل الأسعار مع الخيارات الأصلية
        • الحفاظ على جميع التحسينات المطبقة مسبقاً
        • تطابق كامل بين مفاتيح الخيارات والأسعار
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم استعادة خيارات الجراحة الأصلية مع الحفاظ على جميع التحسينات!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_original_surgery_options(self):
        """اختبار خيارات الجراحة الأصلية"""
        original_surgery_options = [
            "surgery_قلع بسيط",
            "surgery_قلع جراحي",
            "surgery_منحصرة",
            "surgery_منطمرة",
            "surgery_تطويل تاج"
        ]
        
        for option_key in original_surgery_options:
            if option_key in self.dental_tab.treatment_options.checkboxes:
                self.dental_tab.treatment_options.checkboxes[option_key].setChecked(True)
                
    def test_automatic_calculation(self):
        """اختبار الحساب التلقائي"""
        test_options = [
            "surgery_قلع بسيط",      # 30,000
            "surgery_منحصرة",        # 100,000
            "surgery_تضحيك"          # 150,000
        ]
        # المجموع المتوقع: 280,000 ل.س
        
        for option_key in test_options:
            if option_key in self.dental_tab.treatment_options.checkboxes:
                self.dental_tab.treatment_options.checkboxes[option_key].setChecked(True)
                
    def test_all_categories(self):
        """اختبار جميع الفئات"""
        test_options = [
            "endodontic_Vital",        # 120,000
            "restorative_كومبوزت",     # 75,000
            "crowns_زيركون 4D",       # 200,000
            "surgery_تضحيك"           # 150,000
        ]
        # المجموع المتوقع: 545,000 ل.س
        
        for option_key in test_options:
            if option_key in self.dental_tab.treatment_options.checkboxes:
                self.dental_tab.treatment_options.checkboxes[option_key].setChecked(True)
                
    def open_prices_editor(self):
        """فتح نافذة تعديل الأسعار"""
        self.dental_tab.edit_default_prices()
        
    def clear_all_selections(self):
        """مسح جميع التحديدات"""
        for checkbox in self.dental_tab.treatment_options.checkboxes.values():
            checkbox.setChecked(False)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = OriginalSurgeryOptionsTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
