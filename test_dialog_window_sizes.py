#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تعديل حجم النوافذ المنبثقة لجداول البيانات
Test modifying dialog window sizes for data tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, TreatmentSessionDialog

class DialogWindowSizesTestWindow(QMainWindow):
    """نافذة اختبار تعديل حجم النوافذ المنبثقة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تعديل حجم النوافذ المنبثقة لجداول البيانات")
        # تعيين نفس حجم واجهة تبويبة علاج الأسنان الرئيسية
        self.setGeometry(100, 100, 1400, 900)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تعديل حجم النوافذ المنبثقة لجداول البيانات")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 تعديل حجم النوافذ المنبثقة لجداول البيانات:
        
        ✅ تعديل حجم نافذة جدول المعالجات السنية:
        • زيادة حجم النافذة المنبثقة لتكون بنفس حجم واجهة تبويبة علاج الأسنان الرئيسية
        • استخدام setGeometry(100, 100, 1400, 900) لتعيين أبعاد النافذة
        • توسيط النافذة على الشاشة باستخدام center_dialog_on_screen()
        
        ✅ تعديل حجم نافذة جدول الجلسات السنية:
        • تطبيق نفس التعديل على نافذة جدول الجلسات السنية
        • حجم مطابق لحجم واجهة تبويبة علاج الأسنان الرئيسية (1400x900)
        • تناسق في الأحجام بين النافذتين
        
        ✅ تعديل حجم نافذة جلسات المعالجة (TreatmentSessionDialog):
        • تطبيق نفس الحجم الكبير على نافذة إضافة/تعديل جلسات المعالجة
        • استخدام setGeometry(100, 100, 1400, 900) بدلاً من resize(800, 600)
        • توسيط النافذة باستخدام center_on_screen()
        
        🔧 التحسينات التقنية المطبقة:
        • أبعاد مناسبة: 1400x900 بكسل (مطابقة للنافذة الرئيسية)
        • توسيط تلقائي: جميع النوافذ تظهر في وسط الشاشة
        • تناسق في الأحجام: جميع النوافذ المنبثقة بنفس الحجم
        • نسبة عرض إلى ارتفاع مناسبة: لعرض الجداول بوضوح
        • عدم تجاوز حدود الشاشة: النوافذ تظهر ضمن حدود الشاشة
        
        📊 المقارنة قبل وبعد التحسين:
        • نافذة جدول المعالجات: 800x500 → 1400x900 (+75% عرض، +80% ارتفاع)
        • نافذة جدول الجلسات: 900x500 → 1400x900 (+56% عرض، +80% ارتفاع)
        • نافذة جلسات المعالجة: 800x600 → 1400x900 (+75% عرض، +50% ارتفاع)
        • مساحة العرض الإجمالية: زيادة كبيرة في جميع النوافذ
        
        ✅ النتائج المحققة:
        • مساحة عرض أكبر وأوضح لجداول المعالجات والجلسات السنية
        • تحسين قابلية القراءة وسهولة التنقل في البيانات
        • تناسق في أحجام جميع النوافذ المنبثقة
        • تجربة مستخدم محسنة مع مساحة عرض أكبر
        • الحفاظ على جميع الوظائف الحالية للنوافذ
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة تبويبة علاج الأسنان مع النوافذ المحسنة
        options_title = QLabel("⚙️ تبويبة علاج الأسنان (مع نوافذ منبثقة بحجم كبير)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء تبويبة علاج الأسنان
        self.dental_tab = DentalTreatmentsTab(None)  # بدون db_handler للاختبار
        layout.addWidget(self.dental_tab)
        
        # أزرار اختبار النوافذ
        buttons_layout = QHBoxLayout()
        
        # زر اختبار نافذة جدول المعالجات
        test_plans_btn = QPushButton("اختبار نافذة جدول المعالجات")
        test_plans_btn.clicked.connect(self.test_treatment_plans_dialog)
        test_plans_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        buttons_layout.addWidget(test_plans_btn)
        
        # زر اختبار نافذة جدول الجلسات
        test_sessions_btn = QPushButton("اختبار نافذة جدول الجلسات")
        test_sessions_btn.clicked.connect(self.test_treatment_sessions_dialog)
        test_sessions_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        buttons_layout.addWidget(test_sessions_btn)
        
        # زر اختبار نافذة جلسة المعالجة
        test_session_dialog_btn = QPushButton("اختبار نافذة جلسة المعالجة")
        test_session_dialog_btn.clicked.connect(self.test_session_dialog)
        test_session_dialog_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_session_dialog_btn)
        
        # زر عرض معلومات النافذة الحالية
        show_info_btn = QPushButton("عرض معلومات النافذة الحالية")
        show_info_btn.clicked.connect(self.show_current_window_info)
        show_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(show_info_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار النوافذ المنبثقة:
        
        🔍 ما يجب ملاحظته:
        • النافذة الحالية بحجم 1400x900 (نفس حجم النوافذ المنبثقة الجديدة)
        • جميع النوافذ المنبثقة ستظهر بنفس هذا الحجم الكبير
        • النوافذ تظهر في وسط الشاشة تلقائياً
        • مساحة عرض أكبر بكثير للجداول والبيانات
        • تحسين كبير في قابلية القراءة والتنقل
        
        🧪 اختبارات يمكن إجراؤها:
        • انقر على "اختبار نافذة جدول المعالجات" لفتح نافذة جدول المعالجات بالحجم الجديد
        • انقر على "اختبار نافذة جدول الجلسات" لفتح نافذة جدول الجلسات بالحجم الجديد
        • انقر على "اختبار نافذة جلسة المعالجة" لفتح نافذة إضافة جلسة بالحجم الجديد
        • انقر على "عرض معلومات النافذة الحالية" لرؤية أبعاد النافذة الحالية
        • قارن حجم النوافذ المنبثقة مع النافذة الرئيسية
        • لاحظ التوسيط التلقائي للنوافذ على الشاشة
        
        ✅ النتائج المتوقعة:
        • نوافذ منبثقة بحجم 1400x900 (مطابقة للنافذة الرئيسية)
        • توسيط تلقائي لجميع النوافذ على الشاشة
        • مساحة عرض كبيرة وواضحة للجداول
        • تحسين كبير في تجربة المستخدم
        • تناسق في أحجام جميع النوافذ
        • الحفاظ على جميع الوظائف الأصلية
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تعديل حجم النوافذ المنبثقة لجداول البيانات بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_treatment_plans_dialog(self):
        """اختبار نافذة جدول المعالجات"""
        self.dental_tab.view_treatment_plans()
        
    def test_treatment_sessions_dialog(self):
        """اختبار نافذة جدول الجلسات"""
        self.dental_tab.view_treatment_sessions()
        
    def test_session_dialog(self):
        """اختبار نافذة جلسة المعالجة"""
        dialog = TreatmentSessionDialog(
            plan_number="001",
            cost=100000,
            parent=self
        )
        dialog.exec_()
        
    def show_current_window_info(self):
        """عرض معلومات النافذة الحالية"""
        geometry = self.geometry()
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(
            self,
            "معلومات النافذة الحالية",
            f"أبعاد النافذة الحالية:\n\n"
            f"العرض: {geometry.width()} بكسل\n"
            f"الارتفاع: {geometry.height()} بكسل\n"
            f"الموقع X: {geometry.x()}\n"
            f"الموقع Y: {geometry.y()}\n\n"
            f"هذا هو نفس الحجم المطبق على جميع النوافذ المنبثقة الجديدة!"
        )

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = DialogWindowSizesTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
