#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simple test for the comprehensive treatment pricing dialog
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dialog():
    """اختبار بسيط للنافذة"""
    try:
        print("🔍 بدء الاختبار...")
        
        # استيراد النافذة
        from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog
        print("✅ تم استيراد النافذة بنجاح")
        
        # إنشاء تطبيق
        app = QApplication(sys.argv)
        print("✅ تم إنشاء التطبيق")
        
        # إنشاء النافذة
        dialog = ComprehensiveTreatmentPricingDialog()
        print("✅ تم إنشاء النافذة")
        
        # فحص المحتويات
        print(f"📊 عدد حقول الأسماء: {len(dialog.treatment_name_fields)}")
        print(f"📊 عدد حقول الأسعار: {len(dialog.price_spinboxes)}")
        print(f"📊 عدد الأسعار الحالية: {len(dialog.current_prices)}")
        
        # عرض النافذة
        dialog.show()
        print("✅ تم عرض النافذة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dialog()
