# واجهة إدارة خطط وجلسات المعالجة السنية 🦷

## نظرة عامة

تم تطوير واجهة شاملة ومتقدمة لإدارة خطط وجلسات المعالجة السنية تتضمن جميع الأدوات اللازمة لإدارة العلاجات بطريقة احترافية ومنظمة.

## الميزات الرئيسية ✨

### 1. مخطط الأسنان التفاعلي 🦷
- **32 سن للبالغين**: مرتبة حسب نظام FDI الدولي
- **تفاعل مباشر**: النقر على أي سن لتحديده
- **تعبئة تلقائية**: رقم السن يظهر تلقائياً في الحقول
- **تصميم مدمج**: حجم مناسب لا يشغل مساحة كبيرة

### 2. خيارات المعالجة الشاملة 🏥
تم تنظيم خيارات المعالجة في أربع مجموعات رئيسية:

#### أ) لبية (Endodontic):
- ✅ Vital
- ✅ Necrotic  
- ✅ إعادة معالجة
- ✅ متكلسة
- ✅ منحنية بشدة
- ✅ C shape
- ✅ ذروة مفتوحة
- ✅ أداة مكسورة

#### ب) ترميمية (Restorative):
- ✅ كومبوزت
- ✅ أملغم
- ✅ GIC
- ✅ وتد فايبر
- ✅ قلب معدني

#### ج) تيجان (Crowns):
- ✅ خزف معدن
- ✅ زيركون 4D
- ✅ زيركون مغطى إيماكس
- ✅ زيركون مغطى خزف
- ✅ زيركون cutback
- ✅ ستانلس

#### د) جراحة (Surgery):
- ✅ قلع بسيط
- ✅ قلع جراحي
- ✅ منحصرة
- ✅ منطمرة
- ✅ تطويل تاج
- ✅ قطع ذروة
- ✅ تجميل

### 3. خطة المعالجة السنية 📋

#### الحقول المتاحة:
- **رقم خطة المعالجة السنية**: حقل نصي (يمكن للمريض الواحد أن يكون له عدة خطط)
- **رقم السن**: يتم تعبئته تلقائياً عند النقر على سن من المخطط
- **المعالجة السنية**: حقل نص طويل يتم تعبئته تلقائياً بالخيارات المحددة
- **الكلفة**: حقل رقمي لكلفة معالجة السن المحدد
- **التاريخ**: حقل تاريخ مع تقويم منبثق
- **الكلفة الإجمالية**: حقل محسوب تلقائياً

#### الميزات الذكية:
- **تأكيد تغيير السن**: رسالة تأكيد عند اختيار سن جديد
- **تعبئة تلقائية**: نص المعالجة يتم تعبئته من الخيارات المحددة
- **حساب تلقائي**: الكلفة الإجمالية تُحسب تلقائياً

### 4. أزرار التحكم الرئيسية 🎛️
- **حفظ**: حفظ خطة المعالجة مع التحقق من صحة البيانات
- **إلغاء**: إلغاء التغييرات مع رسالة تأكيد
- **حذف**: حذف خطة المعالجة مع رسالة تأكيد
- **إضافة جلسة معالجة سنية**: فتح نافذة الجلسات

### 5. نافذة جلسات المعالجة السنية 🏥

#### المكونات:
- **مخطط أسنان تفاعلي**: نفس المخطط الرئيسي
- **رقم خطة المعالجة**: مرتبط بالخطة الحالية
- **التاريخ**: حقل تاريخ مع تقويم
- **رقم السن**: يتم تعبئته من مخطط الأسنان
- **الإجراء**: حقل نص طويل لتفاصيل الإجراء
- **الكلفة الإجمالية**: نفس قيمة الكلفة من خطة المعالجة
- **دفعة**: حقل رقمي للمبلغ المدفوع
- **مجموع الدفعات**: حقل محسوب تلقائياً
- **المتبقي**: حقل محسوب (الكلفة الإجمالية - مجموع الدفعات)

#### أزرار التحكم:
- **حفظ**: حفظ جلسة المعالجة
- **إلغاء**: إغلاق النافذة بدون حفظ
- **حذف**: حذف الجلسة مع تأكيد

### 6. أزرار العرض الجانبية 📊
في الجهة اليسرى من الواجهة:

- **عرض خطط المعالجة**: جدول يعرض جميع خطط المعالجة للمريض
- **عرض جلسات المعالجة**: جدول يعرض جميع جلسات المعالجة للمريض

## التقنيات المستخدمة 💻

- **PyQt5**: واجهة المستخدم الرسومية
- **Python**: لغة البرمجة الأساسية
- **SQLite**: قاعدة البيانات (مُعدة للربط)
- **RTL Support**: دعم كامل للاتجاه العربي

## كيفية الاستخدام 📖

### 1. إنشاء خطة معالجة جديدة:
1. انقر على السن المطلوب في المخطط
2. اختر خيارات المعالجة المناسبة
3. أدخل رقم خطة المعالجة
4. أدخل الكلفة والتاريخ
5. انقر على "حفظ"

### 2. إضافة جلسة معالجة:
1. تأكد من حفظ خطة المعالجة أولاً
2. انقر على "إضافة جلسة معالجة سنية"
3. املأ بيانات الجلسة
4. أدخل المبلغ المدفوع
5. انقر على "حفظ"

### 3. عرض البيانات:
- استخدم أزرار العرض في الجانب الأيسر
- يمكن عرض خطط المعالجة أو الجلسات في جداول منفصلة

## الميزات التقنية 🔧

### التحقق من صحة البيانات:
- ✅ التأكد من إدخال رقم خطة المعالجة
- ✅ التأكد من تحديد رقم السن
- ✅ التأكد من إدخال تفاصيل المعالجة
- ✅ التأكد من إدخال كلفة صحيحة

### رسائل التأكيد:
- ✅ تأكيد تغيير السن
- ✅ تأكيد إلغاء التغييرات
- ✅ تأكيد حذف البيانات
- ✅ رسائل النجاح والخطأ

### الحسابات التلقائية:
- ✅ حساب الكلفة الإجمالية
- ✅ حساب مجموع الدفعات
- ✅ حساب المبلغ المتبقي

## الملفات الرئيسية 📁

```
ui/tabs/dental_treatments_tab.py    # الملف الرئيسي للواجهة
test_dental_treatment_interface.py  # ملف الاختبار
README_DENTAL_TREATMENT_INTERFACE.md # هذا الملف
```

## الفئات الرئيسية 🏗️

### `ToothButton`
- زر سن تفاعلي صغير الحجم
- يرسل إشارة عند النقر
- تنسيق متغير حسب التحديد

### `CompactTeethChart`
- مخطط أسنان مدمج للبالغين
- 32 سن مرتبة في صفين
- تفاعل كامل مع جميع الأسنان

### `TreatmentOptionsWidget`
- حاوية خيارات المعالجة الأربعة
- مربعات اختيار متعددة
- تحديث تلقائي لنص المعالجة

### `TreatmentPlanWidget`
- نموذج خطة المعالجة السنية
- حقول متكاملة مع التحقق
- حسابات تلقائية

### `TreatmentSessionDialog`
- نافذة منبثقة لجلسات المعالجة
- مخطط أسنان مدمج
- إدارة المدفوعات والمتبقي

### `DentalTreatmentsTab`
- الفئة الرئيسية للواجهة
- تنسيق وربط جميع المكونات
- إدارة الأحداث والبيانات

## التشغيل والاختبار 🚀

### تشغيل التطبيق الكامل:
```bash
python main.py
```

### تشغيل اختبار الواجهة:
```bash
python test_dental_treatment_interface.py
```

## التطوير المستقبلي 🔮

- [ ] ربط كامل مع قاعدة البيانات
- [ ] تقارير مفصلة للمعالجات
- [ ] إحصائيات المدفوعات
- [ ] تصدير البيانات PDF/Excel
- [ ] تذكيرات المواعيد
- [ ] صور الأشعة والتشخيص

## الدعم والمساعدة 🆘

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: +966-XX-XXX-XXXX
- 💬 الدردشة المباشرة: متاحة في التطبيق

---

**تم تطوير هذه الواجهة الشاملة لتوفير نظام متكامل لإدارة خطط وجلسات المعالجة السنية بطريقة احترافية ومنظمة.**
