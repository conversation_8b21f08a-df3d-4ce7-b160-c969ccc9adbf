#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل مشكلة فشل تهيئة قاعدة البيانات
Fix database initialization failure
"""

import sys
import os
import sqlite3
import shutil
from datetime import datetime

def diagnose_database_issue():
    """تشخيص مشكلة قاعدة البيانات"""
    print("🔍 تشخيص مشكلة قاعدة البيانات...")
    
    db_path = 'dental_clinic.db'
    
    # التحقق من وجود ملف قاعدة البيانات
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود")
        return "missing_file"
    
    print(f"✅ ملف قاعدة البيانات موجود: {db_path}")
    print(f"📏 حجم الملف: {os.path.getsize(db_path)} بايت")
    
    # التحقق من إمكانية الوصول للملف
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من سلامة قاعدة البيانات
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()
        
        if integrity_result[0] != 'ok':
            print(f"❌ قاعدة البيانات تالفة: {integrity_result[0]}")
            conn.close()
            return "corrupted"
        
        print("✅ قاعدة البيانات سليمة")
        
        # التحقق من الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📋 الجداول الموجودة: {tables}")
        
        # التحقق من بنية الجداول
        if 'treatment_plans' in tables:
            cursor.execute("PRAGMA table_info(treatment_plans)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"🔧 أعمدة جدول treatment_plans: {columns}")
            
            if 'plan_number' not in columns:
                print("⚠️ جدول treatment_plans يحتاج ترقية (العمود plan_number مفقود)")
                conn.close()
                return "needs_upgrade"
        
        if 'treatment_types' in tables:
            cursor.execute("PRAGMA table_info(treatment_types)")
            columns = [column[1] for column in cursor.fetchall()]
            print(f"🔧 أعمدة جدول treatment_types: {columns}")
            
            if 'category' not in columns:
                print("⚠️ جدول treatment_types يحتاج ترقية (العمود category مفقود)")
                conn.close()
                return "needs_upgrade"
        
        conn.close()
        print("✅ بنية قاعدة البيانات محدثة")
        return "ok"
        
    except sqlite3.Error as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return "error"
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return "error"

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_path = 'dental_clinic.db'
    
    if not os.path.exists(db_path):
        print("⚠️ لا يوجد ملف قاعدة بيانات للنسخ الاحتياطي")
        return None
    
    try:
        backup_path = f"dental_clinic_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return None

def fix_database():
    """إصلاح قاعدة البيانات"""
    print("🔧 بدء إصلاح قاعدة البيانات...")
    
    # تشخيص المشكلة
    issue = diagnose_database_issue()
    
    if issue == "ok":
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
        return True
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    if issue in ["corrupted", "error", "needs_upgrade"]:
        print("🔄 إعادة إنشاء قاعدة البيانات...")
        
        # حذف قاعدة البيانات القديمة
        db_path = 'dental_clinic.db'
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
                print("🗑️ تم حذف قاعدة البيانات القديمة")
            except Exception as e:
                print(f"❌ فشل في حذف قاعدة البيانات القديمة: {e}")
                return False
        
        # إنشاء قاعدة بيانات جديدة
        try:
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from database.db_handler import DatabaseHandler
            
            print("🆕 إنشاء قاعدة بيانات جديدة...")
            db_handler = DatabaseHandler()
            
            if db_handler.create_tables():
                print("✅ تم إنشاء قاعدة البيانات الجديدة")
                
                # إنشاء حساب المسؤول
                if db_handler.add_user('admin', 'admin123', 'مدير النظام', 'admin'):
                    print("✅ تم إنشاء حساب المسؤول")
                
                db_handler.close()
                
                # استعادة البيانات من النسخة الاحتياطية إذا أمكن
                if backup_path and issue == "needs_upgrade":
                    restore_patient_data(backup_path)
                
                return True
            else:
                print("❌ فشل في إنشاء قاعدة البيانات الجديدة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    elif issue == "missing_file":
        print("🆕 إنشاء قاعدة بيانات جديدة...")
        try:
            sys.path.append(os.path.dirname(os.path.abspath(__file__)))
            from database.db_handler import DatabaseHandler
            
            db_handler = DatabaseHandler()
            if db_handler.create_tables():
                print("✅ تم إنشاء قاعدة البيانات")
                
                # إنشاء حساب المسؤول
                if db_handler.add_user('admin', 'admin123', 'مدير النظام', 'admin'):
                    print("✅ تم إنشاء حساب المسؤول")
                
                db_handler.close()
                return True
            else:
                print("❌ فشل في إنشاء قاعدة البيانات")
                return False
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    return False

def restore_patient_data(backup_path):
    """استعادة بيانات المرضى من النسخة الاحتياطية"""
    try:
        print(f"📥 محاولة استعادة بيانات المرضى من {backup_path}...")
        
        # الاتصال بالنسخة الاحتياطية
        backup_conn = sqlite3.connect(backup_path)
        backup_conn.row_factory = sqlite3.Row
        backup_cursor = backup_conn.cursor()
        
        # جلب بيانات المرضى
        backup_cursor.execute("SELECT * FROM patients")
        patients = [dict(row) for row in backup_cursor.fetchall()]
        backup_conn.close()
        
        if not patients:
            print("⚠️ لا توجد بيانات مرضى للاستعادة")
            return
        
        # الاتصال بقاعدة البيانات الجديدة
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database.db_handler import DatabaseHandler
        
        db_handler = DatabaseHandler()
        
        # استعادة بيانات المرضى
        restored_count = 0
        for patient in patients:
            try:
                db_handler.cursor.execute("""
                    INSERT INTO patients (name, birth_year, mobile, whatsapp, 
                                        general_diseases, medications, notes, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    patient.get('name', ''),
                    patient.get('birth_year'),
                    patient.get('mobile', ''),
                    patient.get('whatsapp', ''),
                    patient.get('general_diseases', ''),
                    patient.get('medications', ''),
                    patient.get('notes', ''),
                    patient.get('created_at', datetime.now().isoformat())
                ))
                restored_count += 1
            except Exception as e:
                print(f"⚠️ خطأ في استعادة مريض: {e}")
        
        db_handler.conn.commit()
        db_handler.close()
        
        print(f"✅ تم استعادة {restored_count} مريض من أصل {len(patients)}")
        
    except Exception as e:
        print(f"❌ فشل في استعادة بيانات المرضى: {e}")

def test_application():
    """اختبار تشغيل التطبيق"""
    try:
        print("🧪 اختبار تشغيل التطبيق...")
        
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from database.db_handler import DatabaseHandler
        
        db_handler = DatabaseHandler()
        
        # اختبار إنشاء الجداول
        if not db_handler.create_tables():
            print("❌ فشل في إنشاء الجداول")
            return False
        
        # اختبار إضافة مستخدم
        if not db_handler.check_user_exists('admin'):
            if not db_handler.add_user('admin', 'admin123', 'مدير النظام', 'admin'):
                print("❌ فشل في إنشاء حساب المسؤول")
                return False
        
        # اختبار جلب المستخدمين
        users = db_handler.get_all_users()
        print(f"✅ تم جلب {len(users)} مستخدم")
        
        # اختبار جلب أنواع المعالجات
        treatment_types = db_handler.get_all_treatment_types()
        print(f"✅ تم جلب {len(treatment_types)} نوع معالجة")
        
        db_handler.close()
        print("✅ جميع اختبارات قاعدة البيانات نجحت")
        return True
        
    except Exception as e:
        print(f"❌ فشل في اختبار التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية لحل مشكلة قاعدة البيانات"""
    print("🚀 أداة إصلاح مشكلة قاعدة البيانات")
    print("=" * 50)
    
    # إصلاح قاعدة البيانات
    if fix_database():
        print("\n🎉 تم إصلاح قاعدة البيانات بنجاح!")
        
        # اختبار التطبيق
        if test_application():
            print("\n✅ التطبيق جاهز للتشغيل")
            print("\nيمكنك الآن تشغيل التطبيق باستخدام:")
            print("python main.py")
        else:
            print("\n❌ لا يزال هناك مشاكل في التطبيق")
    else:
        print("\n❌ فشل في إصلاح قاعدة البيانات")
        print("\nيرجى التحقق من:")
        print("• صلاحيات الكتابة في مجلد التطبيق")
        print("• مساحة القرص المتاحة")
        print("• عدم استخدام قاعدة البيانات من تطبيق آخر")

if __name__ == "__main__":
    main()
