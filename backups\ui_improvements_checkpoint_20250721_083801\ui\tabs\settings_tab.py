import os
import sys
import shutil
from datetime import datetime
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea,
                             QGridLayout, QFileDialog, QInputDialog, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate, QDateTime, QTimer
from PyQt5.QtGui import QIcon, QFont, QPainter, QPixmap

class UserDialog(QDialog):
    """نافذة حوار إضافة/تعديل مستخدم"""
    def __init__(self, parent=None, user=None):
        super().__init__(parent)
        self.user = user
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        self.setWindowTitle("إضافة مستخدم جديد" if not self.user else "تعديل بيانات المستخدم")
        self.setMinimumWidth(400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج بيانات المستخدم
        form_layout = QFormLayout()
        
        # اسم المستخدم
        self.username_input = QLineEdit()
        if self.user:
            self.username_input.setText(self.user.get('username', ''))
            # يمكن تعديل اسم المستخدم
        form_layout.addRow("اسم المستخدم:", self.username_input)
        
        # كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("كلمة المرور:", self.password_input)
        
        # تأكيد كلمة المرور
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_input)
        
        # الاسم الكامل
        self.fullname_input = QLineEdit()
        if self.user:
            self.fullname_input.setText(self.user.get('fullname', ''))
        form_layout.addRow("الاسم الكامل:", self.fullname_input)
        
        # نوع المستخدم
        self.user_type_combo = QComboBox()
        self.user_type_combo.addItems(["مستخدم", "مدير"])
        if self.user:
            self.user_type_combo.setCurrentText("مدير" if self.user.get('is_admin', False) else "مستخدم")
        form_layout.addRow("نوع المستخدم:", self.user_type_combo)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def get_user_data(self):
        """الحصول على بيانات المستخدم من النموذج"""
        return {
            'username': self.username_input.text(),
            'password': self.password_input.text(),
            'confirm_password': self.confirm_password_input.text(),
            'fullname': self.fullname_input.text(),
            'is_admin': self.user_type_combo.currentText() == "مدير"
        }

class UsersWidget(QWidget):
    """واجهة إدارة المستخدمين"""
    def __init__(self, db_handler, current_username=None):
        super().__init__()
        self.db_handler = db_handler
        self.current_username = current_username
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        self.add_user_button = QPushButton("إضافة مستخدم")
        self.add_user_button.clicked.connect(self.add_user)
        
        self.edit_user_button = QPushButton("تعديل مستخدم")
        self.edit_user_button.clicked.connect(self.edit_user)
        self.edit_user_button.setEnabled(False)
        
        self.delete_user_button = QPushButton("حذف مستخدم")
        self.delete_user_button.clicked.connect(self.delete_user)
        self.delete_user_button.setEnabled(False)
        
        actions_layout.addWidget(self.add_user_button)
        actions_layout.addWidget(self.edit_user_button)
        actions_layout.addWidget(self.delete_user_button)
        actions_layout.addStretch()
        
        main_layout.addLayout(actions_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الاسم الكامل", "نوع المستخدم", "تاريخ الإنشاء"])
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.verticalHeader().setVisible(False)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setSelectionMode(QTableWidget.SingleSelection)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)
        
        main_layout.addWidget(self.users_table)
        
        # تحميل المستخدمين
        self.load_users()
    
    def load_users(self):
        """تحميل المستخدمين من قاعدة البيانات"""
        users = self.db_handler.get_all_users()
        
        self.users_table.setRowCount(0)
        
        for row, user in enumerate(users):
            self.users_table.insertRow(row)
            
            # اسم المستخدم
            username_item = QTableWidgetItem(user.get('username', ''))
            username_item.setData(Qt.UserRole, user['id'])  # تخزين معرف المستخدم
            self.users_table.setItem(row, 0, username_item)
            
            # الاسم الكامل
            fullname_item = QTableWidgetItem(user.get('fullname', ''))
            self.users_table.setItem(row, 1, fullname_item)
            
            # نوع المستخدم
            user_type = "مدير" if user.get('is_admin', False) else "مستخدم"
            user_type_item = QTableWidgetItem(user_type)
            self.users_table.setItem(row, 2, user_type_item)
            
            # تاريخ الإنشاء
            created_at_item = QTableWidgetItem(user.get('created_at', ''))
            self.users_table.setItem(row, 3, created_at_item)
    
    def on_user_selected(self):
        """معالجة تحديد مستخدم"""
        selected_items = self.users_table.selectedItems()
        
        if selected_items:
            self.edit_user_button.setEnabled(True)
            self.delete_user_button.setEnabled(True)

            # لا يمكن حذف المستخدم الحالي
            selected_username = selected_items[0].text()

            if self.current_username and selected_username == self.current_username:
                self.delete_user_button.setEnabled(False)
        else:
            self.edit_user_button.setEnabled(False)
            self.delete_user_button.setEnabled(False)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self)
        
        if dialog.exec_() == QDialog.Accepted:
            user_data = dialog.get_user_data()
            
            # التحقق من صحة البيانات
            if not user_data['username'] or not user_data['password'] or not user_data['fullname']:
                QMessageBox.warning(self, "تنبيه", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            if user_data['password'] != user_data['confirm_password']:
                QMessageBox.warning(self, "تنبيه", "كلمة المرور وتأكيدها غير متطابقين")
                return
            
            # التحقق من عدم وجود مستخدم بنفس الاسم
            if self.db_handler.check_user_exists(user_data['username']):
                QMessageBox.warning(self, "تنبيه", "اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر")
                return
            
            # إضافة المستخدم
            result = self.db_handler.add_user(
                username=user_data['username'],
                password=user_data['password'],
                fullname=user_data['fullname'],
                is_admin=user_data['is_admin']
            )
            
            if result:
                QMessageBox.information(self, "نجاح", "تمت إضافة المستخدم بنجاح")
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إضافة المستخدم")
    
    def edit_user(self):
        """تعديل بيانات مستخدم"""
        selected_items = self.users_table.selectedItems()
        
        if not selected_items:
            return
        
        user_id = selected_items[0].data(Qt.UserRole)
        user = self.db_handler.get_user_by_id(user_id)
        
        if not user:
            QMessageBox.warning(self, "تنبيه", "لم يتم العثور على المستخدم")
            return
        
        dialog = UserDialog(self, user)
        
        if dialog.exec_() == QDialog.Accepted:
            user_data = dialog.get_user_data()
            
            # التحقق من صحة البيانات
            if not user_data['username'] or not user_data['fullname']:
                QMessageBox.warning(self, "تنبيه", "يرجى ملء جميع الحقول المطلوبة")
                return

            if user_data['password'] and user_data['password'] != user_data['confirm_password']:
                QMessageBox.warning(self, "تنبيه", "كلمة المرور وتأكيدها غير متطابقين")
                return

            # التحقق من عدم وجود مستخدم آخر بنفس اسم المستخدم
            if user_data['username'] != user.get('username'):
                if self.db_handler.check_user_exists(user_data['username']):
                    QMessageBox.warning(self, "تنبيه", "اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر")
                    return

            # تعديل بيانات المستخدم
            result = self.db_handler.update_user(
                user_id=user_id,
                username=user_data['username'] if user_data['username'] != user.get('username') else None,
                password=user_data['password'] if user_data['password'] else None,
                full_name=user_data['fullname'],
                role="admin" if user_data['is_admin'] else "user"
            )
            
            if result:
                QMessageBox.information(self, "نجاح", "تم تعديل بيانات المستخدم بنجاح")
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تعديل بيانات المستخدم")
    
    def delete_user(self):
        """حذف مستخدم"""
        selected_items = self.users_table.selectedItems()
        
        if not selected_items:
            return
        
        user_id = selected_items[0].data(Qt.UserRole)
        username = selected_items[0].text()
        
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم '{username}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حذف المستخدم
            result = self.db_handler.delete_user(user_id)
            
            if result:
                QMessageBox.information(self, "نجاح", "تم حذف المستخدم بنجاح")
                self.load_users()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حذف المستخدم")

class BackupWidget(QWidget):
    """واجهة النسخ الاحتياطي واستعادة البيانات"""
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # قسم النسخ الاحتياطي
        backup_group = QGroupBox("النسخ الاحتياطي")
        backup_layout = QVBoxLayout(backup_group)
        
        # وصف النسخ الاحتياطي
        backup_description = QLabel(
            "قم بإنشاء نسخة احتياطية من قاعدة البيانات للحفاظ على بياناتك. "
            "يُنصح بإنشاء نسخة احتياطية بشكل دوري."
        )
        backup_description.setWordWrap(True)
        backup_layout.addWidget(backup_description)
        
        # مسار النسخ الاحتياطي
        backup_path_layout = QHBoxLayout()
        
        self.backup_path_input = QLineEdit()
        self.backup_path_input.setReadOnly(True)
        self.backup_path_input.setPlaceholderText("اختر مسار حفظ النسخة الاحتياطية")
        
        self.browse_backup_path_button = QPushButton("استعراض...")
        self.browse_backup_path_button.clicked.connect(self.browse_backup_path)
        
        backup_path_layout.addWidget(self.backup_path_input)
        backup_path_layout.addWidget(self.browse_backup_path_button)
        
        backup_layout.addLayout(backup_path_layout)
        
        # زر إنشاء النسخة الاحتياطية
        self.create_backup_button = QPushButton("إنشاء نسخة احتياطية")
        self.create_backup_button.clicked.connect(self.create_backup)
        backup_layout.addWidget(self.create_backup_button)
        
        main_layout.addWidget(backup_group)
        
        # قسم استعادة البيانات
        restore_group = QGroupBox("استعادة البيانات")
        restore_layout = QVBoxLayout(restore_group)
        
        # وصف استعادة البيانات
        restore_description = QLabel(
            "استعادة البيانات من نسخة احتياطية سابقة. "
            "تنبيه: سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية."
        )
        restore_description.setWordWrap(True)
        restore_layout.addWidget(restore_description)
        
        # مسار ملف الاستعادة
        restore_path_layout = QHBoxLayout()
        
        self.restore_path_input = QLineEdit()
        self.restore_path_input.setReadOnly(True)
        self.restore_path_input.setPlaceholderText("اختر ملف النسخة الاحتياطية للاستعادة")
        
        self.browse_restore_path_button = QPushButton("استعراض...")
        self.browse_restore_path_button.clicked.connect(self.browse_restore_path)
        
        restore_path_layout.addWidget(self.restore_path_input)
        restore_path_layout.addWidget(self.browse_restore_path_button)
        
        restore_layout.addLayout(restore_path_layout)
        
        # زر استعادة البيانات
        self.restore_backup_button = QPushButton("استعادة البيانات")
        self.restore_backup_button.clicked.connect(self.restore_backup)
        restore_layout.addWidget(self.restore_backup_button)
        
        main_layout.addWidget(restore_group)
        
        # قسم النسخ الاحتياطية السابقة
        previous_backups_group = QGroupBox("النسخ الاحتياطية السابقة")
        previous_backups_layout = QVBoxLayout(previous_backups_group)
        
        # جدول النسخ الاحتياطية السابقة
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(3)
        self.backups_table.setHorizontalHeaderLabels(["اسم الملف", "التاريخ", "الحجم"])
        self.backups_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.backups_table.verticalHeader().setVisible(False)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.backups_table.setSelectionMode(QTableWidget.SingleSelection)
        self.backups_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.backups_table.itemDoubleClicked.connect(self.on_backup_double_clicked)
        
        previous_backups_layout.addWidget(self.backups_table)
        
        # أزرار إجراءات النسخ الاحتياطية
        backup_actions_layout = QHBoxLayout()
        
        self.refresh_backups_button = QPushButton("تحديث القائمة")
        self.refresh_backups_button.clicked.connect(self.load_backups)
        
        self.delete_backup_button = QPushButton("حذف النسخة المحددة")
        self.delete_backup_button.clicked.connect(self.delete_backup)
        self.delete_backup_button.setEnabled(False)
        
        backup_actions_layout.addWidget(self.refresh_backups_button)
        backup_actions_layout.addWidget(self.delete_backup_button)
        
        previous_backups_layout.addLayout(backup_actions_layout)
        
        main_layout.addWidget(previous_backups_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # تحميل النسخ الاحتياطية السابقة
        self.load_backups()
        
        # تعيين نسب التخطيط
        main_layout.setStretch(0, 1)  # قسم النسخ الاحتياطي
        main_layout.setStretch(1, 1)  # قسم استعادة البيانات
        main_layout.setStretch(2, 2)  # قسم النسخ الاحتياطية السابقة
    
    def browse_backup_path(self):
        """اختيار مسار حفظ النسخة الاحتياطية"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد حفظ النسخة الاحتياطية",
            os.path.expanduser("~")
        )
        
        if directory:
            self.backup_path_input.setText(directory)
    
    def browse_restore_path(self):
        """اختيار ملف النسخة الاحتياطية للاستعادة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            os.path.expanduser("~"),
            "ملفات النسخ الاحتياطي (*.db);;جميع الملفات (*)"
        )
        
        if file_path:
            self.restore_path_input.setText(file_path)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        backup_dir = self.backup_path_input.text()
        
        if not backup_dir:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار مجلد لحفظ النسخة الاحتياطية")
            return
        
        if not os.path.isdir(backup_dir):
            QMessageBox.warning(self, "تنبيه", "المسار المحدد غير صالح")
            return
        
        # إنشاء اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"dental_clinic_backup_{timestamp}.db"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # تعطيل الأزرار أثناء النسخ الاحتياطي
        self.create_backup_button.setEnabled(False)
        self.restore_backup_button.setEnabled(False)
        
        # محاكاة تقدم النسخ الاحتياطي
        self.backup_progress = 0
        self.backup_timer = QTimer()
        self.backup_timer.timeout.connect(self.update_backup_progress)
        self.backup_timer.start(100)
        
        # إنشاء النسخة الاحتياطية
        try:
            result = self.db_handler.backup_database(backup_path)
            
            if result:
                # إكمال شريط التقدم
                self.progress_bar.setValue(100)
                QMessageBox.information(
                    self,
                    "نجاح",
                    f"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backup_path}"
                )
                self.load_backups()
            else:
                QMessageBox.critical(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n{str(e)}")
        finally:
            # إيقاف المؤقت وإعادة تمكين الأزرار
            self.backup_timer.stop()
            self.progress_bar.setVisible(False)
            self.create_backup_button.setEnabled(True)
            self.restore_backup_button.setEnabled(True)
    
    def update_backup_progress(self):
        """تحديث تقدم النسخ الاحتياطي"""
        self.backup_progress += 5
        if self.backup_progress >= 95:
            self.backup_timer.stop()
        self.progress_bar.setValue(min(self.backup_progress, 95))
    
    def restore_backup(self):
        """استعادة البيانات من نسخة احتياطية"""
        backup_path = self.restore_path_input.text()
        
        if not backup_path:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار ملف النسخة الاحتياطية للاستعادة")
            return
        
        if not os.path.isfile(backup_path):
            QMessageBox.warning(self, "تنبيه", "ملف النسخة الاحتياطية غير موجود")
            return
        
        # التأكيد قبل الاستعادة
        reply = QMessageBox.warning(
            self,
            "تأكيد الاستعادة",
            "سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.\n"
            "هل أنت متأكد من استعادة البيانات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # عرض شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # تعطيل الأزرار أثناء الاستعادة
        self.create_backup_button.setEnabled(False)
        self.restore_backup_button.setEnabled(False)
        
        # محاكاة تقدم الاستعادة
        self.restore_progress = 0
        self.restore_timer = QTimer()
        self.restore_timer.timeout.connect(self.update_restore_progress)
        self.restore_timer.start(100)
        
        # استعادة البيانات
        try:
            result = self.db_handler.restore_database(backup_path)
            
            if result:
                # إكمال شريط التقدم
                self.progress_bar.setValue(100)
                QMessageBox.information(
                    self,
                    "نجاح",
                    "تم استعادة البيانات بنجاح.\n"
                    "يرجى إعادة تشغيل البرنامج لتطبيق التغييرات."
                )
            else:
                QMessageBox.critical(self, "خطأ", "فشل في استعادة البيانات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء استعادة البيانات:\n{str(e)}")
        finally:
            # إيقاف المؤقت وإعادة تمكين الأزرار
            self.restore_timer.stop()
            self.progress_bar.setVisible(False)
            self.create_backup_button.setEnabled(True)
            self.restore_backup_button.setEnabled(True)
    
    def update_restore_progress(self):
        """تحديث تقدم استعادة البيانات"""
        self.restore_progress += 5
        if self.restore_progress >= 95:
            self.restore_timer.stop()
        self.progress_bar.setValue(min(self.restore_progress, 95))
    
    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية السابقة"""
        backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../../backup")
        
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        self.backups_table.setRowCount(0)
        
        # البحث عن ملفات النسخ الاحتياطي
        backup_files = []
        for file in os.listdir(backup_dir):
            if file.startswith("dental_clinic_backup_") and file.endswith(".db"):
                file_path = os.path.join(backup_dir, file)
                file_stat = os.stat(file_path)
                
                # استخراج التاريخ من اسم الملف
                try:
                    date_str = file.replace("dental_clinic_backup_", "").replace(".db", "")
                    date_obj = datetime.strptime(date_str, "%Y%m%d_%H%M%S")
                    date_formatted = date_obj.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    date_formatted = "-"
                
                # حساب حجم الملف
                size_bytes = file_stat.st_size
                if size_bytes < 1024:
                    size_str = f"{size_bytes} بايت"
                elif size_bytes < 1024 * 1024:
                    size_str = f"{size_bytes / 1024:.2f} كيلوبايت"
                else:
                    size_str = f"{size_bytes / (1024 * 1024):.2f} ميجابايت"
                
                backup_files.append((file, date_formatted, size_str, date_obj, file_path))
        
        # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
        backup_files.sort(key=lambda x: x[3], reverse=True)
        
        # إضافة الملفات إلى الجدول
        for row, (file, date, size, _, file_path) in enumerate(backup_files):
            self.backups_table.insertRow(row)
            
            # اسم الملف
            file_item = QTableWidgetItem(file)
            file_item.setData(Qt.UserRole, file_path)  # تخزين مسار الملف
            self.backups_table.setItem(row, 0, file_item)
            
            # التاريخ
            date_item = QTableWidgetItem(date)
            self.backups_table.setItem(row, 1, date_item)
            
            # الحجم
            size_item = QTableWidgetItem(size)
            self.backups_table.setItem(row, 2, size_item)
        
        # تمكين/تعطيل زر الحذف
        self.backups_table.itemSelectionChanged.connect(self.on_backup_selected)
    
    def on_backup_selected(self):
        """معالجة تحديد نسخة احتياطية"""
        selected_items = self.backups_table.selectedItems()
        self.delete_backup_button.setEnabled(len(selected_items) > 0)
    
    def on_backup_double_clicked(self, item):
        """معالجة النقر المزدوج على نسخة احتياطية"""
        file_path = item.data(Qt.UserRole)
        self.restore_path_input.setText(file_path)
    
    def delete_backup(self):
        """حذف نسخة احتياطية"""
        selected_items = self.backups_table.selectedItems()
        
        if not selected_items:
            return
        
        file_path = selected_items[0].data(Qt.UserRole)
        file_name = selected_items[0].text()
        
        # التأكيد قبل الحذف
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية '{file_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                os.remove(file_path)
                QMessageBox.information(self, "نجاح", "تم حذف النسخة الاحتياطية بنجاح")
                self.load_backups()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف النسخة الاحتياطية:\n{str(e)}")

class SettingsTab(QWidget):
    def __init__(self, db_handler, current_username=None):
        super().__init__()
        self.db_handler = db_handler
        self.current_username = current_username
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # تبويبات الإعدادات
        self.settings_tabs = QTabWidget()

        # تبويبة إدارة المستخدمين
        self.users_widget = UsersWidget(self.db_handler, self.current_username)
        self.settings_tabs.addTab(self.users_widget, "إدارة المستخدمين")

        # تبويبة النسخ الاحتياطي واستعادة البيانات
        self.backup_widget = BackupWidget(self.db_handler)
        self.settings_tabs.addTab(self.backup_widget, "النسخ الاحتياطي واستعادة البيانات")

        main_layout.addWidget(self.settings_tabs)