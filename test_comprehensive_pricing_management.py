#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار نظام إدارة أسعار وأسماء المعالجة الشامل
Test script for Comprehensive Treatment Pricing Management Interface
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                             QVBoxLayout, QWidget, QLabel, QMessageBox,
                             QHBoxLayout, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة الشاملة
from ui.tabs.dental_treatments_tab import ComprehensiveTreatmentPricingDialog, PricesManager

class TestComprehensivePricingWindow(QMainWindow):
    """نافذة اختبار نظام إدارة الأسعار الشامل"""
    
    def __init__(self):
        super().__init__()
        self.prices_manager = PricesManager()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار نظام إدارة أسعار وأسماء المعالجة الشامل")
        self.setGeometry(100, 100, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(25)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان الاختبار
        self.create_title_section(layout)
        
        # وصف النظام الجديد
        self.create_description_section(layout)
        
        # أزرار الاختبار
        self.create_test_buttons(layout)
        
        # معلومات النظام
        self.create_system_info_section(layout)
        
    def create_title_section(self, layout):
        """إنشاء قسم العنوان"""
        title = QLabel("اختبار نظام إدارة أسعار وأسماء المعالجة الشامل")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: white;
                padding: 25px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 15px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
    def create_description_section(self, layout):
        """إنشاء قسم الوصف"""
        description_frame = QFrame()
        description_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        desc_layout = QVBoxLayout(description_frame)
        desc_layout.setSpacing(15)
        
        desc_title = QLabel("🎯 الميزات الجديدة في النظام الشامل:")
        desc_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        desc_layout.addWidget(desc_title)
        
        features_text = """
✅ عرض جميع المجموعات الثمانية للمعالجة في واجهة واحدة شاملة
✅ تعديل أسماء المعالجات مباشرة - يمكن تخصيص الأسماء حسب احتياجات العيادة
✅ تعديل أسعار جميع المعالجات مع التحقق من صحة البيانات
✅ حفظ دائم لجميع التغييرات - تبقى محفوظة عند إعادة تشغيل التطبيق
✅ واجهة عربية RTL مع تصميم حديث ومتجاوب
✅ أزرار حفظ/إلغاء/استعادة افتراضي مع رسائل تأكيد
✅ التحقق من صحة البيانات قبل الحفظ
✅ تحديث فوري للواجهة الرئيسية بعد الحفظ

🔧 المجموعات المدعومة:
• مجموعة اللبية (Endodontic) - 8 معالجات
• مجموعة الترميمية (Restorative) - 8 معالجات  
• مجموعة التيجان (Crowns) - 8 معالجات
• مجموعة الجراحة (Surgery) - 8 معالجات

📊 إجمالي: 32 معالجة قابلة للتخصيص الكامل
        """
        
        features_label = QLabel(features_text)
        features_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #495057;
                line-height: 1.6;
                padding: 10px;
                background-color: #ffffff;
                border-radius: 8px;
                border: 1px solid #e9ecef;
            }
        """)
        features_label.setWordWrap(True)
        desc_layout.addWidget(features_label)
        
        layout.addWidget(description_frame)
        
    def create_test_buttons(self, layout):
        """إنشاء أزرار الاختبار"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 2px solid #4a90e2;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # عنوان قسم الأزرار
        buttons_title = QLabel("🧪 اختبارات النظام:")
        buttons_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #4a90e2;
                margin-bottom: 10px;
            }
        """)
        buttons_layout.addWidget(buttons_title)
        
        # تخطيط أفقي للأزرار
        buttons_row = QHBoxLayout()
        buttons_row.setSpacing(15)
        
        # زر فتح النظام الشامل
        open_comprehensive_btn = QPushButton("🚀 فتح نظام إدارة الأسعار الشامل")
        open_comprehensive_btn.setStyleSheet(self.get_button_style("#28a745"))
        open_comprehensive_btn.clicked.connect(self.open_comprehensive_pricing_dialog)
        buttons_row.addWidget(open_comprehensive_btn)
        
        # زر عرض الأسعار الحالية
        show_prices_btn = QPushButton("📊 عرض الأسعار الحالية")
        show_prices_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        show_prices_btn.clicked.connect(self.show_current_prices)
        buttons_row.addWidget(show_prices_btn)
        
        # زر اختبار الحفظ
        test_save_btn = QPushButton("💾 اختبار نظام الحفظ")
        test_save_btn.setStyleSheet(self.get_button_style("#fd7e14"))
        test_save_btn.clicked.connect(self.test_save_system)
        buttons_row.addWidget(test_save_btn)
        
        buttons_layout.addLayout(buttons_row)
        layout.addWidget(buttons_frame)
        
    def create_system_info_section(self, layout):
        """إنشاء قسم معلومات النظام"""
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f4fd;
                border: 2px solid #4a90e2;
                border-radius: 12px;
                padding: 15px;
            }
        """)
        
        info_layout = QVBoxLayout(info_frame)
        
        info_title = QLabel("ℹ️ معلومات النظام:")
        info_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #4a90e2;
            }
        """)
        info_layout.addWidget(info_title)
        
        # معلومات الملفات
        files_info = """
📁 ملفات النظام:
• dental_prices_config.json - ملف حفظ الأسعار
• dental_treatment_names_config.json - ملف حفظ أسماء المعالجات
• ui/tabs/dental_treatments_tab.py - الكود الرئيسي للنظام
        """
        
        files_label = QLabel(files_info)
        files_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #495057;
                padding: 10px;
                background-color: #ffffff;
                border-radius: 6px;
            }
        """)
        info_layout.addWidget(files_label)
        
        layout.addWidget(info_frame)
        
    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background: linear-gradient(135deg, {color}, {color}dd);
                color: white;
                border: none;
                padding: 15px 20px;
                font-size: 14px;
                font-weight: bold;
                border-radius: 8px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background: linear-gradient(135deg, {color}dd, {color}bb);
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: linear-gradient(135deg, {color}bb, {color}99);
            }}
        """
        
    def open_comprehensive_pricing_dialog(self):
        """فتح نافذة إدارة الأسعار الشاملة"""
        try:
            dialog = ComprehensiveTreatmentPricingDialog(self)
            result = dialog.exec_()
            
            if result == dialog.Accepted:
                QMessageBox.information(self, "نجح", "تم حفظ التغييرات بنجاح!")
            else:
                QMessageBox.information(self, "ملغي", "تم إلغاء العملية")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في فتح النافذة: {str(e)}")
            
    def show_current_prices(self):
        """عرض الأسعار الحالية"""
        try:
            current_prices = self.prices_manager.load_prices()
            
            prices_text = "الأسعار الحالية:\n\n"
            
            # تجميع الأسعار حسب الفئة
            categories = {
                'endodontic': 'اللبية (Endodontic)',
                'restorative': 'الترميمية (Restorative)', 
                'crowns': 'التيجان (Crowns)',
                'surgery': 'الجراحة (Surgery)'
            }
            
            for category, category_name in categories.items():
                prices_text += f"🔹 {category_name}:\n"
                category_prices = {k: v for k, v in current_prices.items() if k.startswith(category)}
                
                for key, price in category_prices.items():
                    treatment_name = key.split('_', 1)[1]
                    prices_text += f"   • {treatment_name}: {price:,} ل.س\n"
                prices_text += "\n"
                
            QMessageBox.information(self, "الأسعار الحالية", prices_text)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في عرض الأسعار: {str(e)}")
            
    def test_save_system(self):
        """اختبار نظام الحفظ"""
        QMessageBox.information(
            self, 
            "اختبار نظام الحفظ", 
            "لاختبار نظام الحفظ:\n\n"
            "1. افتح نظام إدارة الأسعار الشامل\n"
            "2. قم بتعديل بعض الأسعار أو الأسماء\n"
            "3. اضغط على 'حفظ جميع التغييرات'\n"
            "4. أعد تشغيل التطبيق للتأكد من حفظ التغييرات\n"
            "5. تحقق من وجود الملفات:\n"
            "   • dental_prices_config.json\n"
            "   • dental_treatment_names_config.json"
        )

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestComprehensivePricingWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
