#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسين توزيع المساحة العمودية في واجهة علاج الأسنان
Test improved vertical space distribution in dental treatment interface
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QScrollArea
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab, CompactTeethChart, TreatmentOptionsWidget

class SpaceDistributionTestWindow(QMainWindow):
    """نافذة اختبار توزيع المساحة العمودية"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار تحسين توزيع المساحة العمودية")
        self.setGeometry(100, 100, 1200, 800)
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار تحسين توزيع المساحة العمودية")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسينات
        info_label = QLabel("""
        🎯 التحسينات المطبقة:
        
        📉 تقليل ارتفاع "مخطط الأسنان التفاعلي":
        • تقليل ارتفاع chart_container من 80px إلى 65px
        • تقليل الهوامش من (10,15,10,10) إلى (8,12,8,8)
        • تقليل المسافات من 5px إلى 3px
        • تقليل هوامش المخطط من (2,2,2,2) إلى (1,1,1,1)
        
        📈 زيادة ارتفاع "خيارات المعالجة":
        • زيادة ارتفاع المجموعات من 250px إلى 290px
        • زيادة الحد الأدنى في CSS من 220px إلى 280px
        • مساحة أكبر لعرض مربعات الاختيار بوضوح
        
        ✅ النتيجة: توزيع أفضل للمساحة العمودية
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 12px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إنشاء منطقة تمرير للمحتوى
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # محتوى الاختبار
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(15)
        content_layout.setContentsMargins(10, 10, 10, 10)
        
        # اختبار مخطط الأسنان المحسن
        teeth_title = QLabel("🦷 مخطط الأسنان التفاعلي (ارتفاع مقلل)")
        teeth_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #e74c3c;
                padding: 8px;
                background-color: #fadbd8;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(teeth_title)
        
        self.teeth_chart = CompactTeethChart()
        content_layout.addWidget(self.teeth_chart)
        
        # اختبار خيارات المعالجة المحسنة
        options_title = QLabel("⚙️ خيارات المعالجة (ارتفاع مزيد)")
        options_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(options_title)
        
        self.treatment_options = TreatmentOptionsWidget()
        content_layout.addWidget(self.treatment_options)
        
        # معلومات القياسات
        measurements_label = QLabel("""
        📏 القياسات الجديدة:
        
        مخطط الأسنان:
        • الارتفاع: 65px (كان 80px) - توفير 15px
        • الهوامش: مقللة لتوفير مساحة إضافية
        
        خيارات المعالجة:
        • ارتفاع المجموعات: 290px (كان 250px) - زيادة 40px
        • الحد الأدنى في CSS: 280px (كان 220px) - زيادة 60px
        
        📊 النتيجة الإجمالية:
        • توفير 15px من مخطط الأسنان
        • إضافة 40-60px لخيارات المعالجة
        • تحسين توزيع المساحة بشكل عام
        """)
        measurements_label.setStyleSheet("""
            QLabel {
                background-color: #eaf2f8;
                padding: 12px;
                border-radius: 8px;
                font-size: 10px;
                color: #2980b9;
                border: 1px solid #3498db;
                line-height: 1.3;
            }
        """)
        content_layout.addWidget(measurements_label)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تحسين توزيع المساحة العمودية بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 10px;
            }
        """)
        layout.addWidget(result_label)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = SpaceDistributionTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
