#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار مربعي اختيار حالة خطة المعالجة
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد النافذة والويدجت
from ui.tabs.dental_treatments_tab import TreatmentPlanDialog, TreatmentPlanWidget

class TestTreatmentPlanStatusWindow(QMainWindow):
    """نافذة اختبار مربعي اختيار حالة خطة المعالجة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """إعداد واجهة الاختبار"""
        self.setWindowTitle("اختبار مربعي اختيار حالة خطة المعالجة")
        self.setGeometry(200, 200, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # عنوان
        title = QLabel("اختبار مربعي اختيار حالة خطة المعالجة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # وصف الميزات الجديدة
        description = QLabel("""
الميزات الجديدة المضافة:

✅ مربع اختيار "نشطة" (أخضر) - محدد افتراضياً
✅ مربع اختيار "مكتملة" (أحمر)
✅ استخدام QButtonGroup لضمان اختيار واحد فقط
✅ تسمية "الحالة" متسقة مع باقي الحقول
✅ تخطيط أفقي (RTL) للغة العربية
✅ حفظ الحالة مع بيانات خطة المعالجة
✅ تحميل الحالة عند تعديل خطة موجودة
✅ إعادة تعيين إلى "نشطة" عند مسح النموذج

الموقع الجديد: في نفس الصف الأفقي مع باقي الحقول (بعد التاريخ)
        """)
        description.setAlignment(Qt.AlignRight)
        description.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #34495e;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #007bff;
                line-height: 1.6;
            }
        """)
        layout.addWidget(description)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        buttons_layout.setSpacing(15)
        
        # زر اختبار الويدجت منفرداً
        test_widget_btn = QPushButton("🧪 اختبار ويدجت خطة المعالجة منفرداً")
        test_widget_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #17a2b8, #138496);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #138496, #117a8b);
            }
        """)
        test_widget_btn.clicked.connect(self.test_widget_standalone)
        buttons_layout.addWidget(test_widget_btn)
        
        # زر فتح نافذة إضافة خطة معالجة كاملة
        open_dialog_btn = QPushButton("🔍 فتح نافذة إضافة خطة معالجة كاملة")
        open_dialog_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #007bff, #0056b3);
                color: white;
                border: none;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                border-radius: 10px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #0056b3, #004085);
            }
        """)
        open_dialog_btn.clicked.connect(self.open_treatment_plan_dialog)
        buttons_layout.addWidget(open_dialog_btn)
        
        layout.addLayout(buttons_layout)
        
        # منطقة الحالة
        self.status_label = QLabel("جاهز لاختبار مربعي اختيار حالة خطة المعالجة...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 10px;
                background-color: #e9ecef;
                border-radius: 5px;
                margin-top: 20px;
            }
        """)
        layout.addWidget(self.status_label)
        
    def test_widget_standalone(self):
        """اختبار ويدجت خطة المعالجة منفرداً"""
        try:
            print("=" * 60)
            print("🧪 اختبار ويدجت خطة المعالجة منفرداً...")
            print("=" * 60)
            
            # إنشاء نافذة جديدة للويدجت
            widget_window = QWidget()
            widget_window.setWindowTitle("ويدجت خطة المعالجة مع مربعي الاختيار")
            widget_window.setGeometry(300, 300, 800, 400)
            widget_window.setLayoutDirection(Qt.RightToLeft)
            
            layout = QVBoxLayout(widget_window)
            layout.setContentsMargins(20, 20, 20, 20)
            
            # إضافة الويدجت
            treatment_plan_widget = TreatmentPlanWidget()
            layout.addWidget(treatment_plan_widget)
            
            # زر لطباعة البيانات
            test_btn = QPushButton("طباعة بيانات الخطة (مع الحالة)")
            test_btn.clicked.connect(lambda: self.print_plan_data(treatment_plan_widget))
            layout.addWidget(test_btn)
            
            widget_window.show()
            
            self.status_label.setText("✅ تم فتح ويدجت خطة المعالجة - تحقق من مربعي الاختيار!")
            print("✅ تم فتح ويدجت خطة المعالجة")
            print("🔍 تحقق من:")
            print("   📍 مربع اختيار 'نشطة' (أخضر) - محدد افتراضياً")
            print("   📍 مربع اختيار 'مكتملة' (أحمر)")
            print("   📍 تسمية 'الحالة' في نفس الصف مع باقي الحقول")
            print("   📍 الموقع: بعد حقل التاريخ في نفس الصف الأفقي")
            print("   📍 يمكن اختيار واحد فقط في نفس الوقت")
            
        except Exception as e:
            self.status_label.setText(f"❌ خطأ: {e}")
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()
    
    def print_plan_data(self, widget):
        """طباعة بيانات الخطة مع الحالة"""
        try:
            data = widget.get_plan_data()
            print("=" * 40)
            print("📊 بيانات خطة المعالجة:")
            for key, value in data.items():
                print(f"   {key}: {value}")
            print("=" * 40)
            
            status = data.get('status', 'غير محدد')
            self.status_label.setText(f"✅ تم طباعة البيانات - الحالة: {status}")
            
        except Exception as e:
            print(f"❌ خطأ في طباعة البيانات: {e}")
            self.status_label.setText(f"❌ خطأ في طباعة البيانات: {e}")
    
    def open_treatment_plan_dialog(self):
        """فتح نافذة إضافة خطة معالجة كاملة"""
        try:
            print("=" * 60)
            print("🚀 فتح نافذة إضافة خطة معالجة كاملة...")
            print("=" * 60)
            
            # ملاحظة: نحتاج db_handler للنافذة الكاملة
            # هذا مجرد اختبار للواجهة
            self.status_label.setText("ℹ️ لفتح النافذة الكاملة، استخدم التطبيق الرئيسي")
            print("ℹ️ لفتح النافذة الكاملة، استخدم التطبيق الرئيسي")
            print("📍 اذهب إلى تبويبة المعالجة السنية")
            print("📍 اضغط 'إضافة خطة معالجة سنية'")
            print("📍 تحقق من مربعي الاختيار في حاوية خطة المعالجة")
            
        except Exception as e:
            self.status_label.setText(f"❌ خطأ: {e}")
            print(f"❌ خطأ: {e}")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TestTreatmentPlanStatusWindow()
    window.show()
    
    print("تم تشغيل اختبار مربعي اختيار حالة خطة المعالجة")
    print("اضغط على الأزرار لاختبار الميزات الجديدة")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
