#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لإصلاح مشاكل الاتجاه العربي في جميع تبويبات التطبيق
"""

import os
import re

# قائمة ملفات التبويبات
tab_files = [
    'ui/tabs/patients_tab.py',
    'ui/tabs/treatment_tab.py', 
    'ui/tabs/appointments_tab.py',
    'ui/tabs/lab_tab.py',
    'ui/tabs/expenses_tab.py',
    'ui/tabs/reports_tab.py',
    'ui/tabs/settings_tab.py'
]

def fix_rtl_in_file(file_path):
    """إصلاح الاتجاه العربي في ملف واحد"""
    if not os.path.exists(file_path):
        print(f"الملف غير موجود: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # إضافة تعيين الاتجاه العربي في init_ui إذا لم يكن موجوداً
        if 'setLayoutDirection(Qt.RightToLeft)' not in content:
            # البحث عن def init_ui(self):
            init_ui_pattern = r'(def init_ui\(self\):\s*\n)'
            if re.search(init_ui_pattern, content):
                replacement = r'\1        # تعيين الاتجاه العربي\n        self.setLayoutDirection(Qt.RightToLeft)\n        \n'
                content = re.sub(init_ui_pattern, replacement, content)
        
        # إصلاح FormLayout alignment
        content = re.sub(
            r'form_layout\.setLabelAlignment\(Qt\.AlignLeft\)',
            'form_layout.setLabelAlignment(Qt.AlignRight)',
            content
        )
        
        # إصلاح TableWidget alignment
        content = re.sub(
            r'\.setAlternatingRowColors\(True\)',
            '.setAlternatingRowColors(True)\n        table.setLayoutDirection(Qt.RightToLeft)',
            content
        )
        
        # كتابة المحتوى المُحدث إذا تغير
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"تم إصلاح الملف: {file_path}")
            return True
        else:
            print(f"الملف لا يحتاج إصلاح: {file_path}")
            return False
            
    except Exception as e:
        print(f"خطأ في إصلاح الملف {file_path}: {e}")
        return False

def main():
    print("🔧 بدء إصلاح مشاكل الاتجاه العربي في التطبيق...")
    
    fixed_count = 0
    for file_path in tab_files:
        if fix_rtl_in_file(file_path):
            fixed_count += 1
    
    print(f"\n✅ تم إصلاح {fixed_count} ملف من أصل {len(tab_files)} ملف")
    print("🎯 انتهاء عملية الإصلاح")

if __name__ == "__main__":
    main()