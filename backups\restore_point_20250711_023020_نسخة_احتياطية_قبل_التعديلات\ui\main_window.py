import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QLabel, QPushButton, QStackedWidget, QSplitter, QFrame,
                             QMessageBox, QAction, QMenu, QToolBar, QStatusBar)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QTimer
from PyQt5.QtGui import QIcon, QFont, QCursor

# استيراد التبويبات
from ui.tabs.patients_tab import PatientsTab
from ui.tabs.treatment_tab import TreatmentTab
from ui.tabs.appointments_tab import AppointmentsTab
from ui.tabs.lab_tab import LabTab
from ui.tabs.expenses_tab import ExpensesTab
from ui.tabs.reports_tab import ReportsTab
from ui.tabs.settings_tab import SettingsTab
from ui.style_helper import StyleHelper
from ui.combobox_fix import <PERSON>mboBoxFixer
from ui.style_helper import Icons
from ui.modern_enhancements import ModernEnhancements

class MainWindow(QMainWindow):
    # إشارة تسجيل الخروج
    logout_signal = pyqtSignal()
    
    def __init__(self, db_handler, username, user_role):
        super().__init__()
        self.db_handler = db_handler
        self.username = username
        self.user_role = user_role
        self.init_ui()
    
    def init_ui(self):
        # إعداد النافذة الرئيسية
        self.setWindowTitle("إدارة العيادة السنية - نظام إدارة متطور")
        self.setMinimumSize(1400, 800)
        self.resize(1600, 900)
        
        # تعيين الاتجاه العربي للنافذة
        self.setLayoutDirection(Qt.RightToLeft)

        # تحميل النمط الحديث
        self.load_modern_style()

        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)



        # إنشاء التبويبات الرئيسية في الأعلى
        self.create_main_tabs(main_layout)

        # إنشاء شريط الحالة
        self.create_status_bar()

        # نقل زر تسجيل الخروج ومعلومات المستخدم إلى شريط التبويبات الرئيسي

        # ربط إشارات التبويبات
        self.connect_tab_signals()
        
        # تطبيق التحسينات الحديثة
        self.apply_modern_enhancements()

        # تطبيق تأثير اليد لجميع العناصر التفاعلية
        self.apply_hand_cursor_to_all_widgets()

    def load_modern_style(self):
        """تحميل النمط الحديث"""
        try:
            # تجربة التصميم الحديث المتوافق مع Qt أولاً
            qt_modern_style_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'qt_modern_style.css')
            if os.path.exists(qt_modern_style_path):
                with open(qt_modern_style_path, 'r', encoding='utf-8') as f:
                    style = f.read()
                self.setStyleSheet(style)
                print("تم تحميل النمط الحديث بنجاح")
                return
            
            # التصميم الافتراضي كخيار ثاني    
            style_path = os.path.join(os.path.dirname(__file__), '..', 'assets', 'modern_style.css')
            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as f:
                    style = f.read()
                self.setStyleSheet(style)
                print("تم تحميل النمط الافتراضي")
            else:
                # نمط احتياطي إذا لم يوجد الملف
                self.setStyleSheet(self.get_fallback_style())
                print("تم تحميل النمط الاحتياطي")
        except Exception as e:
            print(f"خطأ في تحميل النمط: {e}")
            self.setStyleSheet(self.get_fallback_style())

    def get_fallback_style(self):
        """نمط احتياطي"""
        return """
            QMainWindow {
                background-color: #ecf0f1;
                color: #2c3e50;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 10pt;
            }
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: #ffffff;
                border-radius: 8px;
                margin-top: 10px;
            }
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 1px solid #bdc3c7;
                border-bottom: none;
                border-radius: 8px 8px 0 0;
                padding: 12px 20px;
                margin-right: 2px;
                color: #2c3e50;
                font-weight: 500;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-color: #2980b9;
                font-weight: bold;
            }
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 16px;
                color: #2c3e50;
                font-weight: 500;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e8f4fd, stop: 1 #d6eaf8);
                border-color: #3498db;
            }

            /* تحسينات إضافية للعناصر التفاعلية */
            QPushButton {
                font-weight: 500;
            }

            QComboBox {
                padding: 4px 8px;
            }

            QCheckBox {
                spacing: 5px;
            }

            QRadioButton {
                spacing: 5px;
            }
        """

    def create_main_tabs(self, main_layout):
        """إنشاء التبويبات الرئيسية في الأعلى مع منطقة المستخدم"""
        # إنشاء حاوية للتبويبات ومنطقة المستخدم
        tabs_container = QWidget()
        tabs_container_layout = QVBoxLayout(tabs_container)
        tabs_container_layout.setContentsMargins(0, 0, 0, 0)
        tabs_container_layout.setSpacing(0)
        
        # إنشاء شريط التبويبات مع منطقة المستخدم
        self.create_tab_bar_with_user_area(tabs_container_layout)
        
        # إنشاء ويدجت التبويبات
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)  # التبويبات في الأعلى
        self.tabs.setMovable(False)  # منع تحريك التبويبات
        self.tabs.setTabsClosable(False)  # منع إغلاق التبويبات
        
        # إخفاء شريط التبويبات الافتراضي (سنستخدم الشريط المخصص)
        self.tabs.tabBar().hide()

        # تطبيق أنماط محسنة للتبويبات
        self.apply_enhanced_tab_styles()

        # إضافة تأثيرات إضافية
        self.add_tab_animations()

        # إزالة تأثير الظل نهائياً
        self.remove_all_shadows()

        # إنشاء التبويبات
        self.create_tabs()

        # إصلاح جميع القوائم المنسدلة في التطبيق
        ComboBoxFixer.fix_all_comboboxes(self)

        # ربط إشارة تغيير التبويب
        self.tabs.currentChanged.connect(self.on_tab_changed)

        # إضافة التبويبات إلى الحاوية
        tabs_container_layout.addWidget(self.tabs)
        
        # إضافة الحاوية إلى التخطيط الرئيسي
        main_layout.addWidget(tabs_container)

    def create_tab_bar_with_user_area(self, container_layout):
        """إنشاء شريط التبويبات المخصص مع منطقة المستخدم"""
        # إنشاء الشريط الرئيسي
        tab_bar_widget = QWidget()
        tab_bar_layout = QHBoxLayout(tab_bar_widget)
        tab_bar_layout.setContentsMargins(15, 8, 15, 8)
        tab_bar_layout.setSpacing(5)
        
        # تطبيق نمط الشريط الأزرق
        tab_bar_widget.setStyleSheet("""
            QWidget {
                background-color: #1e3a8a;
                border: none;
                padding: 0px;
            }
        """)
        
        # إنشاء التبويبات المخصصة
        self.custom_tab_buttons = []
        tab_names = ["👥 المرضى", "🦷 المعالجة", "📅 المواعيد", "🔬 المخبر", "💰 المصاريف", "📊 التقارير", "⚙️ الإعدادات"]
        
        for i, tab_name in enumerate(tab_names):
            btn = QPushButton(tab_name)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 20px;
                    margin: 2px 3px;
                    color: white;
                    font-weight: 600;
                    font-size: 13px;
                    min-width: 80px;
                    max-width: 120px;
                }
                QPushButton:hover {
                    background-color: rgba(255, 255, 255, 0.15);
                    border: none;
                }
                QPushButton:pressed {
                    background-color: rgba(255, 255, 255, 0.25);
                    border: none;
                }
                QPushButton:checked {
                    background-color: white;
                    color: #1e3a8a;
                    font-weight: 700;
                    border: none;
                }
            """)
            btn.setCheckable(True)
            btn.setCursor(QCursor(Qt.PointingHandCursor))
            btn.clicked.connect(lambda checked, index=i: self.switch_tab(index))
            self.custom_tab_buttons.append(btn)
            tab_bar_layout.addWidget(btn)
        
        # تعيين التبويب الأول كمختار
        self.custom_tab_buttons[0].setChecked(True)
        
        # إضافة مساحة مرنة لدفع منطقة المستخدم إلى اليسار
        tab_bar_layout.addStretch()
        
        # إنشاء منطقة المستخدم
        user_area_widget = QWidget()
        user_area_layout = QHBoxLayout(user_area_widget)
        user_area_layout.setContentsMargins(0, 0, 0, 0)
        user_area_layout.setSpacing(10)
        
        # إضافة معلومات المستخدم
        user_info = QLabel(f"👤 {self.username} ({self.user_role})")
        user_info.setStyleSheet("""
            QLabel {
                color: white;
                font-weight: 600;
                font-size: 12px;
                padding: 8px 12px;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }
        """)
        user_area_layout.addWidget(user_info)
        
        # إضافة زر تسجيل الخروج
        logout_btn = QPushButton("🚪 تسجيل الخروج")
        logout_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: 600;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        logout_btn.setCursor(QCursor(Qt.PointingHandCursor))
        logout_btn.clicked.connect(self.logout_signal.emit)
        user_area_layout.addWidget(logout_btn)
        
        # إضافة منطقة المستخدم إلى الشريط الرئيسي
        tab_bar_layout.addWidget(user_area_widget)
        
        # إضافة الشريط إلى الحاوية
        container_layout.addWidget(tab_bar_widget)
    
    def switch_tab(self, index):
        """تبديل التبويب المحدد"""
        # إلغاء تحديد جميع الأزرار
        for btn in self.custom_tab_buttons:
            btn.setChecked(False)
        
        # تحديد الزر المختار
        self.custom_tab_buttons[index].setChecked(True)
        
        # تغيير التبويب في QTabWidget
        self.tabs.setCurrentIndex(index)
    
    def on_tab_changed(self, index):
        """معالجة تغيير التبويب برمجياً"""
        # تحديث حالة الأزرار المخصصة
        for btn in self.custom_tab_buttons:
            btn.setChecked(False)
        
        if 0 <= index < len(self.custom_tab_buttons):
            self.custom_tab_buttons[index].setChecked(True)
    
    def switch_to_tab(self, tab_name):
        """التبديل إلى تبويب معين بالاسم"""
        tab_names = ["المرضى", "المعالجة", "المواعيد", "المخبر", "المصاريف", "التقارير", "الإعدادات"]
        if tab_name in tab_names:
            index = tab_names.index(tab_name)
            self.switch_tab(index)

    def apply_enhanced_tab_styles(self):
        """تطبيق تصميم منطقة المحتوى"""
        content_area_style = """
            QTabWidget {
                background-color: #f8f9fa;
                border: none;
                padding: 0px;
            }

            QTabWidget::pane {
                background-color: #f8f9fa;
                border: none;
                margin-top: 0px;
                padding: 20px;
            }

            QTabBar {
                background-color: transparent;
                border: none;
                height: 0px;
                margin: 0px;
            }

            QTabBar::tab {
                height: 0px;
                width: 0px;
                margin: 0px;
                padding: 0px;
                border: none;
                background-color: transparent;
            }
        """
        self.tabs.setStyleSheet(content_area_style)

    def add_tab_animations(self):
        """إضافة تأثيرات حركية للتبويبات"""
        # تفعيل الخصائص المتقدمة للتبويبات
        self.tabs.setDocumentMode(True)
        self.tabs.setElideMode(Qt.ElideNone)

        # تحسين مظهر التبويبات (تم إزالة CSS transition غير المدعوم)
        animation_style = """
            QTabBar::tab {
                padding: 8px 12px;
            }

            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """

        # دمج الأنماط الحالية مع أنماط التحسين
        current_style = self.tabs.styleSheet()
        self.tabs.setStyleSheet(current_style + animation_style)

    def create_tabs(self):
        """إنشاء جميع التبويبات"""
        # إنشاء التبويبات
        self.patients_tab = PatientsTab(self.db_handler)
        self.treatment_tab = TreatmentTab(self.db_handler, self)
        self.appointments_tab = AppointmentsTab(self.db_handler)
        self.lab_tab = LabTab(self.db_handler)
        self.expenses_tab = ExpensesTab(self.db_handler)
        self.reports_tab = ReportsTab(self.db_handler)
        self.settings_tab = SettingsTab(self.db_handler, self.username)

        # إضافة التبويبات بترتيب من اليمين إلى اليسار (العربية)
        self.tabs.addTab(self.patients_tab, "👥 المرضى")
        self.tabs.addTab(self.treatment_tab, "🦷 المعالجة")
        self.tabs.addTab(self.appointments_tab, "📅 المواعيد")
        self.tabs.addTab(self.lab_tab, "🔬 المخبر")
        self.tabs.addTab(self.expenses_tab, "💰 المصاريف")
        self.tabs.addTab(self.reports_tab, "📊 التقارير")
        self.tabs.addTab(self.settings_tab, "⚙️ الإعدادات")

        # تطبيق الأنماط الزرقاء مباشرة
        self.force_apply_blue_style()

        # تعيين التبويب الافتراضي (المرضى - الأول في الترتيب)
        self.tabs.setCurrentIndex(0)


    
    def connect_tab_signals(self):
        """ربط إشارات التبويبات"""
        # ربط إشارة اختيار المريض من تبويب المرضى إلى تبويب المعالجة
        self.patients_tab.patient_selected.connect(self.treatment_tab.set_patient)

        # ربط إشارة اختيار المريض من تبويب المرضى إلى تبويب المخبر
        self.patients_tab.patient_selected.connect(self.lab_tab.set_patient)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة المحسن"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # إضافة معلومات الإصدار
        version_label = QLabel("🏥 نظام إدارة العيادة السنية - الإصدار 2.0")
        version_label.setStyleSheet("color: #7f8c8d; font-weight: 500;")
        status_bar.addWidget(version_label)

        # إضافة مساحة فارغة
        status_bar.addWidget(QLabel(""))

        # إضافة التاريخ والوقت الحالي
        self.date_label = QLabel()
        self.date_label.setStyleSheet("color: #34495e; font-weight: 500;")
        self.update_time()
        status_bar.addPermanentWidget(self.date_label)
        
        # إعداد مؤقت لتحديث الوقت كل دقيقة
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(60000)  # تحديث كل 60 ثانية
    
    def update_time(self):
        """تحديث التاريخ والوقت الحالي"""
        current_datetime = datetime.now().strftime("%Y-%m-%d | %H:%M")
        self.date_label.setText(f"📅 {current_datetime}")

    def closeEvent(self, event):
        """معالجة حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل أنت متأكد من رغبتك في الخروج من التطبيق؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
    
    def apply_modern_enhancements(self):
        """تطبيق التحسينات الحديثة على التطبيق"""
        try:
            # تحسين النافذة الرئيسية
            ModernEnhancements.enhance_window(self)
            
            # تحسين التبويبات
            ModernEnhancements.enhance_tab_widget(self.tabs)
            
            # تطبيق التحسينات على كل تبويبة
            for i in range(self.tabs.count()):
                tab_widget = self.tabs.widget(i)
                ModernEnhancements.apply_modern_theme_to_widget(tab_widget)
                
            # إعادة تطبيق الأنماط للتأكد
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()
                
            print("تم تطبيق التحسينات الحديثة بنجاح")
                
        except Exception as e:
            print(f"خطأ في تطبيق التحسينات الحديثة: {e}")

    def apply_hand_cursor_to_all_widgets(self):
        """تطبيق تأثير اليد لجميع العناصر التفاعلية في التطبيق"""
        try:
            # تطبيق تأثير اليد للتبويبات
            if hasattr(self, 'tabs'):
                self.tabs.tabBar().setCursor(QCursor(Qt.PointingHandCursor))

            # البحث عن جميع العناصر التفاعلية وتطبيق تأثير اليد
            self.apply_hand_cursor_recursive(self)

            print("تم تطبيق تأثير اليد بنجاح")
        except Exception as e:
            print(f"خطأ في تطبيق تأثير اليد: {e}")

    def apply_hand_cursor_recursive(self, widget):
        """تطبيق تأثير اليد بشكل تكراري لجميع العناصر الفرعية"""
        try:
            from PyQt5.QtWidgets import (QPushButton, QComboBox, QCheckBox, QRadioButton,
                                       QTableWidget, QListWidget, QTreeWidget, QSpinBox,
                                       QDoubleSpinBox, QWidget, QTabBar)

            # قائمة أنواع العناصر التي تحتاج تأثير اليد
            interactive_types = (
                QPushButton,
                QComboBox,
                QCheckBox,
                QRadioButton,
                QTableWidget,
                QListWidget,
                QTreeWidget,
                QSpinBox,
                QDoubleSpinBox,
                QTabBar
            )

            # تطبيق تأثير اليد إذا كان العنصر من الأنواع التفاعلية
            if isinstance(widget, interactive_types):
                widget.setCursor(QCursor(Qt.PointingHandCursor))

            # تطبيق تأثير اليد للعناصر الفرعية
            for child in widget.findChildren(QWidget):
                if isinstance(child, interactive_types):
                    child.setCursor(QCursor(Qt.PointingHandCursor))

        except Exception as e:
            print(f"خطأ في تطبيق تأثير اليد للعنصر: {e}")

    def remove_all_shadows(self):
        """تطبيق أنماط بديلة للتبويبات (تم إزالة CSS غير المدعوم)"""
        try:
            # أنماط بديلة تركز على التحسينات المدعومة
            clean_style = """
                QTabWidget {
                    background-color: #ffffff;
                    border: 1px solid #dee2e6;
                }

                QTabBar {
                    background-color: #f8f9fa;
                    border: none;
                }

                QTabBar::tab {
                    background-color: #f8f9fa;
                    border: 1px solid #dee2e6;
                    padding: 8px 12px;
                    margin-right: 2px;
                }

                QTabBar::tab:selected {
                    background-color: #ffffff;
                    border-bottom: 1px solid #ffffff;
                }

                QTabBar::tab:hover {
                    background-color: #e9ecef;
                }
            """

            # تطبيق الأنماط على التبويبات
            self.tabs.setStyleSheet(clean_style)

            print("تم تطبيق الأنماط البديلة بنجاح")

        except Exception as e:
            print(f"خطأ في تطبيق الأنماط: {e}")

    def force_apply_blue_style(self):
        """تطبيق الأنماط الزرقاء بقوة"""
        try:
            blue_style = """
                QTabWidget {
                    background-color: #1e3a8a !important;
                    border: none !important;
                    padding: 0px !important;
                }

                QTabWidget::pane {
                    background-color: #f8f9fa !important;
                    border: none !important;
                    margin-top: 0px !important;
                    padding: 20px !important;
                }

                QTabBar {
                    background-color: #1e3a8a !important;
                    border: none !important;
                    padding: 8px 0px !important;
                    margin: 0px !important;
                    spacing: 4px !important;
                }

                QTabBar::tab {
                    background-color: transparent !important;
                    border: none !important;
                    border-radius: 0px !important;
                    padding: 12px 20px !important;
                    margin: 0px 2px !important;
                    color: white !important;
                    font-weight: 600 !important;
                    font-size: 14px !important;
                    min-width: 80px !important;
                    max-width: 120px !important;
                    text-align: center !important;
                }

                /* Style for the container of tabs and user controls */
                QHBoxLayout {
                    background-color: #1e3a8a; /* Match QTabBar background */
                }

                QTabBar::tab:selected {
                    background-color: white !important;
                    color: #1e3a8a !important;
                    font-weight: 700 !important;
                    border: none !important;
                    border-radius: 0px !important;
                }

                QTabBar::tab:hover:!selected {
                    background-color: rgba(255, 255, 255, 0.15) !important;
                    color: white !important;
                    border: none !important;
                }

                QTabBar::tab:pressed {
                    background-color: rgba(255, 255, 255, 0.25) !important;
                }
            """

            # تطبيق الأنماط على التبويبات
            self.tabs.setStyleSheet(blue_style)

            # تطبيق الأنماط على شريط التبويبات مباشرة
            self.tabs.tabBar().setStyleSheet(blue_style)

            # تحديث التطبيق
            self.tabs.update()
            self.update()

            print("تم تطبيق الأنماط الزرقاء بنجاح")

        except Exception as e:
            print(f"خطأ في تطبيق الأنماط الزرقاء: {e}")
