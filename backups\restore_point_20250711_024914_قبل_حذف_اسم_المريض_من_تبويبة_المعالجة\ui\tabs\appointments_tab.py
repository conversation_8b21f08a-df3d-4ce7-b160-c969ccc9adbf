import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QTimeEdit, QCheckBox,
                             QScrollArea, QGridLayout, QCalendarWidget, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate, QTime, QDateTime
from PyQt5.QtGui import QIcon, QFont, QPainter, QPen, QColor, QBrush

class TimeSlotWidget(QWidget):
    """ويدجت فترة زمنية في التقويم"""
    clicked = pyqtSignal(QDateTime, QDateTime)  # إشارة النقر (وقت البداية، وقت النهاية)
    
    def __init__(self, start_time, end_time, appointment_data=None, parent=None):
        super().__init__(parent)
        self.start_time = start_time
        self.end_time = end_time
        self.appointment_data = appointment_data
        self.is_selected = False
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        self.setMinimumHeight(30)
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # تعيين التلميح
        if self.appointment_data:
            tooltip = f"المريض: {self.appointment_data.get('patient_name', '')}\n"
            tooltip += f"الوقت: {self.start_time.toString('hh:mm')} - {self.end_time.toString('hh:mm')}\n"
            tooltip += f"ملاحظات: {self.appointment_data.get('notes', '')}"
            self.setToolTip(tooltip)
    
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # تعيين لون الخلفية
        if self.appointment_data:
            # خلية محجوزة
            if self.is_selected:
                painter.setBrush(QBrush(QColor(52, 152, 219)))  # أزرق داكن عند التحديد
            else:
                painter.setBrush(QBrush(QColor(52, 152, 219, 180)))  # أزرق
        else:
            # خلية فارغة
            if self.is_selected:
                painter.setBrush(QBrush(QColor(200, 200, 200)))  # رمادي عند التحديد
            else:
                painter.setBrush(QBrush(QColor(240, 240, 240)))  # رمادي فاتح
        
        # رسم الخلفية
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 5, 5)
        
        # رسم الحدود
        if self.appointment_data:
            painter.setPen(QPen(QColor(41, 128, 185), 1))  # أزرق داكن
        else:
            painter.setPen(QPen(QColor(200, 200, 200), 1))  # رمادي
        
        painter.drawRoundedRect(self.rect().adjusted(0, 0, -1, -1), 5, 5)
        
        # كتابة النص
        if self.appointment_data:
            painter.setPen(QPen(QColor(255, 255, 255)))  # أبيض
            font = painter.font()
            font.setBold(True)
            painter.setFont(font)
            
            # اسم المريض
            patient_name = self.appointment_data.get('patient_name', '')
            painter.drawText(self.rect().adjusted(5, 5, -5, -5), Qt.AlignCenter, patient_name)
    
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.is_selected = True
            self.update()
            self.clicked.emit(self.start_time, self.end_time)
    
    def show_context_menu(self, position):
        if self.appointment_data:
            menu = QMenu(self)
            edit_action = menu.addAction("تعديل الموعد")
            delete_action = menu.addAction("إلغاء الموعد")
            
            action = menu.exec_(self.mapToGlobal(position))
            
            if action == edit_action:
                self.parent().parent().edit_appointment(self.appointment_data['id'])
            elif action == delete_action:
                self.parent().parent().delete_appointment(self.appointment_data['id'])

class DayViewWidget(QWidget):
    """ويدجت عرض اليوم"""
    time_slot_clicked = pyqtSignal(QDateTime, QDateTime)  # إشارة النقر على فترة زمنية
    
    def __init__(self, date, parent=None):
        super().__init__(parent)
        self.date = date
        self.time_slots = {}
        self.appointments = []
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # عنوان اليوم
        day_header = QLabel(self.date.toString("dddd yyyy/MM/dd"))
        day_header.setAlignment(Qt.AlignCenter)
        day_header.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px; background-color: #f0f0f0;")
        main_layout.addWidget(day_header)
        
        # منطقة التمرير للفترات الزمنية
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # ويدجت الفترات الزمنية
        time_slots_widget = QWidget()
        self.time_slots_layout = QVBoxLayout(time_slots_widget)
        self.time_slots_layout.setContentsMargins(10, 10, 10, 10)
        self.time_slots_layout.setSpacing(2)
        
        # إنشاء الفترات الزمنية (من 8 صباحًا إلى 9 مساءً)
        start_hour = 8
        end_hour = 21
        
        for hour in range(start_hour, end_hour):
            # إنشاء عنوان الساعة
            hour_label = QLabel(f"{hour}:00")
            hour_label.setStyleSheet("font-weight: bold; color: #666;")
            self.time_slots_layout.addWidget(hour_label)
            
            # إنشاء 4 فترات زمنية لكل ساعة (كل 15 دقيقة)
            for minute in [0, 15, 30, 45]:
                start_time = QDateTime(self.date, QTime(hour, minute))
                end_time = QDateTime(self.date, QTime(hour, minute + 15 if minute < 45 else hour + 1, 0 if minute >= 45 else 0))
                
                time_slot = TimeSlotWidget(start_time, end_time)
                time_slot.clicked.connect(self.on_time_slot_clicked)
                
                self.time_slots_layout.addWidget(time_slot)
                self.time_slots[f"{hour}:{minute:02d}"] = time_slot
        
        scroll_area.setWidget(time_slots_widget)
        main_layout.addWidget(scroll_area)
    
    def on_time_slot_clicked(self, start_time, end_time):
        """معالجة حدث النقر على فترة زمنية"""
        self.time_slot_clicked.emit(start_time, end_time)
    
    def update_appointments(self, appointments):
        """تحديث المواعيد في العرض"""
        # إعادة تعيين جميع الفترات الزمنية
        for slot in self.time_slots.values():
            slot.appointment_data = None
            slot.is_selected = False
            slot.update()
        
        # تحديث الفترات الزمنية بالمواعيد
        self.appointments = appointments
        
        for appointment in appointments:
            appointment_date = QDateTime.fromString(appointment['appointment_datetime'], "yyyy-MM-dd hh:mm")
            appointment_end_date = appointment_date.addSecs(appointment['duration_minutes'] * 60)
            
            # التحقق من أن الموعد في نفس اليوم
            if appointment_date.date() == self.date:
                # تحديث الفترات الزمنية التي يغطيها الموعد
                current_time = appointment_date
                while current_time < appointment_end_date:
                    hour = current_time.time().hour()
                    minute = current_time.time().minute()
                    minute = (minute // 15) * 15  # تقريب إلى أقرب 15 دقيقة
                    
                    slot_key = f"{hour}:{minute:02d}"
                    if slot_key in self.time_slots:
                        self.time_slots[slot_key].appointment_data = appointment
                        self.time_slots[slot_key].update()
                    
                    current_time = current_time.addSecs(15 * 60)  # إضافة 15 دقيقة

class WeekViewWidget(QWidget):
    """ويدجت عرض الأسبوع"""
    time_slot_clicked = pyqtSignal(QDateTime, QDateTime)  # إشارة النقر على فترة زمنية
    
    def __init__(self, start_date, parent=None):
        super().__init__(parent)
        self.start_date = start_date
        self.day_views = []
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(1)
        
        # إنشاء عرض لكل يوم في الأسبوع
        for i in range(7):
            day_date = self.start_date.addDays(i)
            day_view = DayViewWidget(day_date)
            day_view.time_slot_clicked.connect(self.time_slot_clicked)
            
            main_layout.addWidget(day_view)
            self.day_views.append(day_view)
    
    def update_appointments(self, appointments):
        """تحديث المواعيد في العرض"""
        # تصفية المواعيد لكل يوم
        for i, day_view in enumerate(self.day_views):
            day_date = self.start_date.addDays(i)
            
            # تصفية المواعيد لهذا اليوم
            day_appointments = []
            for appointment in appointments:
                appointment_date = QDateTime.fromString(appointment['appointment_datetime'], "yyyy-MM-dd hh:mm").date()
                if appointment_date == day_date:
                    day_appointments.append(appointment)
            
            day_view.update_appointments(day_appointments)

class MonthViewWidget(QWidget):
    """ويدجت عرض الشهر"""
    date_clicked = pyqtSignal(QDate)  # إشارة النقر على تاريخ
    
    def __init__(self, date, parent=None):
        super().__init__(parent)
        self.date = date
        self.appointments = []
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # عنوان الشهر
        month_header = QLabel(self.date.toString("MMMM yyyy"))
        month_header.setAlignment(Qt.AlignCenter)
        month_header.setStyleSheet("font-weight: bold; font-size: 16px; padding: 10px;")
        main_layout.addWidget(month_header)
        
        # تقويم الشهر
        self.calendar = QCalendarWidget()
        self.calendar.setFirstDayOfWeek(Qt.Saturday)  # بداية الأسبوع يوم السبت
        self.calendar.setVerticalHeaderFormat(QCalendarWidget.NoVerticalHeader)
        self.calendar.setGridVisible(True)
        self.calendar.clicked.connect(self.on_date_clicked)
        
        # تعيين الشهر الحالي
        self.calendar.setSelectedDate(self.date)
        
        main_layout.addWidget(self.calendar)
    
    def on_date_clicked(self, date):
        """معالجة حدث النقر على تاريخ"""
        self.date_clicked.emit(date)
    
    def update_appointments(self, appointments):
        """تحديث المواعيد في العرض"""
        self.appointments = appointments
        
        # تحديث تنسيق التقويم لإظهار الأيام التي تحتوي على مواعيد
        # (هذا يتطلب تنفيذ مخصص لـ QCalendarWidget، وهو خارج نطاق هذا المثال)
        # يمكن استخدام dateTextFormat لتغيير تنسيق النص للأيام التي تحتوي على مواعيد

class AppointmentDialog(QDialog):
    """نافذة حوار إضافة/تعديل موعد"""
    def __init__(self, db_handler, start_time=None, end_time=None, appointment_data=None, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.start_time = start_time
        self.end_time = end_time
        self.appointment_data = appointment_data
        self.patients = []
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # عنوان النافذة
        self.setWindowTitle("إضافة موعد جديد" if not self.appointment_data else "تعديل الموعد")
        self.setMinimumWidth(400)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        
        # حقول الإدخال
        self.patient_combo = QComboBox()
        
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        if self.start_time:
            self.date_input.setDate(self.start_time.date())
        else:
            self.date_input.setDate(QDate.currentDate())
        
        self.time_input = QTimeEdit()
        if self.start_time:
            self.time_input.setTime(self.start_time.time())
        else:
            self.time_input.setTime(QTime.currentTime())
        
        self.duration_input = QSpinBox()
        self.duration_input.setRange(15, 180)
        self.duration_input.setSingleStep(15)
        self.duration_input.setSuffix(" دقيقة")
        self.duration_input.setValue(30)  # 30 دقيقة افتراضيًا
        
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات")
        self.notes_input.setMaximumHeight(80)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("المريض:", self.patient_combo)
        form_layout.addRow("التاريخ:", self.date_input)
        form_layout.addRow("الوقت:", self.time_input)
        form_layout.addRow("المدة:", self.duration_input)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # تعيين بيانات الموعد إذا كانت متوفرة
        if self.appointment_data:
            self.set_appointment_data()
    
    def load_patients(self):
        """تحميل قائمة المرضى"""
        self.patients = self.db_handler.get_all_patients()
        self.patient_combo.clear()
        
        for patient in self.patients:
            self.patient_combo.addItem(patient['name'], patient['id'])
    
    def set_appointment_data(self):
        """تعيين بيانات الموعد في النموذج"""
        # تعيين المريض
        patient_id = self.appointment_data.get('patient_id')
        for i in range(self.patient_combo.count()):
            if self.patient_combo.itemData(i) == patient_id:
                self.patient_combo.setCurrentIndex(i)
                break
        
        # تعيين التاريخ والوقت
        appointment_datetime = QDateTime.fromString(self.appointment_data.get('appointment_datetime', ''), "yyyy-MM-dd hh:mm")
        if appointment_datetime.isValid():
            self.date_input.setDate(appointment_datetime.date())
            self.time_input.setTime(appointment_datetime.time())
        
        # تعيين المدة
        self.duration_input.setValue(self.appointment_data.get('duration_minutes', 30))
        
        # تعيين الملاحظات
        self.notes_input.setText(self.appointment_data.get('notes', ''))
    
    def get_appointment_data(self):
        """الحصول على بيانات الموعد من النموذج"""
        patient_id = self.patient_combo.currentData()
        patient_name = self.patient_combo.currentText()
        
        appointment_date = self.date_input.date()
        appointment_time = self.time_input.time()
        appointment_datetime = QDateTime(appointment_date, appointment_time)
        
        return {
            'patient_id': patient_id,
            'patient_name': patient_name,
            'appointment_datetime': appointment_datetime.toString("yyyy-MM-dd hh:mm"),
            'duration_minutes': self.duration_input.value(),
            'notes': self.notes_input.toPlainText().strip()
        }

class AppointmentsTab(QWidget):
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.current_view = "day"  # day, week, month
        self.current_date = QDate.currentDate()
        self.init_ui()
        self.load_appointments()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # أزرار التنقل
        self.prev_button = QPushButton("السابق")
        self.prev_button.clicked.connect(self.go_to_previous)
        
        self.today_button = QPushButton("اليوم")
        self.today_button.clicked.connect(self.go_to_today)
        
        self.next_button = QPushButton("التالي")
        self.next_button.clicked.connect(self.go_to_next)
        
        # أزرار تغيير العرض
        self.day_view_button = QPushButton("يومي")
        self.day_view_button.setCheckable(True)
        self.day_view_button.setChecked(True)
        self.day_view_button.clicked.connect(lambda: self.change_view("day"))
        
        self.week_view_button = QPushButton("أسبوعي")
        self.week_view_button.setCheckable(True)
        self.week_view_button.clicked.connect(lambda: self.change_view("week"))
        
        self.month_view_button = QPushButton("شهري")
        self.month_view_button.setCheckable(True)
        self.month_view_button.clicked.connect(lambda: self.change_view("month"))
        
        # زر إضافة موعد
        self.add_appointment_button = QPushButton("إضافة موعد")
        self.add_appointment_button.clicked.connect(self.add_appointment)
        
        # إضافة الأزرار إلى شريط الأدوات
        toolbar_layout.addWidget(self.prev_button)
        toolbar_layout.addWidget(self.today_button)
        toolbar_layout.addWidget(self.next_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.day_view_button)
        toolbar_layout.addWidget(self.week_view_button)
        toolbar_layout.addWidget(self.month_view_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_appointment_button)
        
        main_layout.addLayout(toolbar_layout)
        
        # منطقة العرض
        self.view_container = QWidget()
        self.view_layout = QVBoxLayout(self.view_container)
        self.view_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء العرض اليومي افتراضيًا
        self.create_day_view()
        
        main_layout.addWidget(self.view_container)
    
    def create_day_view(self):
        """إنشاء عرض اليوم"""
        # مسح العرض الحالي
        self.clear_view()
        
        # إنشاء عرض اليوم
        self.day_view = DayViewWidget(self.current_date)
        self.day_view.time_slot_clicked.connect(self.on_time_slot_clicked)
        
        self.view_layout.addWidget(self.day_view)

        # تحديث المواعيد للعرض الحالي
        self.load_appointments()
    
    def create_week_view(self):
        """إنشاء عرض الأسبوع"""
        # مسح العرض الحالي
        self.clear_view()
        
        # حساب بداية الأسبوع (السبت)
        day_of_week = self.current_date.dayOfWeek()
        days_to_saturday = (day_of_week - 6) % 7
        start_of_week = self.current_date.addDays(-days_to_saturday)
        
        # إنشاء عرض الأسبوع
        self.week_view = WeekViewWidget(start_of_week)
        self.week_view.time_slot_clicked.connect(self.on_time_slot_clicked)
        
        self.view_layout.addWidget(self.week_view)

        # تحديث المواعيد للعرض الحالي
        self.load_appointments()
    
    def create_month_view(self):
        """إنشاء عرض الشهر"""
        # مسح العرض الحالي
        self.clear_view()
        
        # إنشاء عرض الشهر
        self.month_view = MonthViewWidget(self.current_date)
        self.month_view.date_clicked.connect(self.on_date_clicked)
        
        self.view_layout.addWidget(self.month_view)

        # تحديث المواعيد للعرض الحالي
        self.load_appointments()
    
    def clear_view(self):
        """مسح العرض الحالي"""
        # مسح التخطيط
        while self.view_layout.count():
            item = self.view_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def change_view(self, view_type):
        """تغيير نوع العرض"""
        if view_type == self.current_view:
            return
        
        self.current_view = view_type
        
        # تحديث حالة الأزرار
        self.day_view_button.setChecked(view_type == "day")
        self.week_view_button.setChecked(view_type == "week")
        self.month_view_button.setChecked(view_type == "month")
        
        # إنشاء العرض المناسب
        if view_type == "day":
            self.create_day_view()
        elif view_type == "week":
            self.create_week_view()
        elif view_type == "month":
            self.create_month_view()
    
    def go_to_previous(self):
        """الانتقال إلى الفترة السابقة"""
        if self.current_view == "day":
            self.current_date = self.current_date.addDays(-1)
        elif self.current_view == "week":
            self.current_date = self.current_date.addDays(-7)
        elif self.current_view == "month":
            self.current_date = self.current_date.addMonths(-1)
        
        self.update_view()
    
    def go_to_next(self):
        """الانتقال إلى الفترة التالية"""
        if self.current_view == "day":
            self.current_date = self.current_date.addDays(1)
        elif self.current_view == "week":
            self.current_date = self.current_date.addDays(7)
        elif self.current_view == "month":
            self.current_date = self.current_date.addMonths(1)
        
        self.update_view()
    
    def go_to_today(self):
        """الانتقال إلى اليوم الحالي"""
        self.current_date = QDate.currentDate()
        self.update_view()
    
    def update_view(self):
        """تحديث العرض الحالي"""
        # إعادة إنشاء العرض المناسب
        if self.current_view == "day":
            self.create_day_view()
        elif self.current_view == "week":
            self.create_week_view()
        elif self.current_view == "month":
            self.create_month_view()
        
        # تحميل المواعيد
        self.load_appointments()
    
    def load_appointments(self):
        """تحميل المواعيد"""
        # تحديد نطاق التاريخ بناءً على العرض الحالي
        if self.current_view == "day":
            start_date = self.current_date
            end_date = self.current_date
        elif self.current_view == "week":
            # حساب بداية الأسبوع (السبت)
            day_of_week = self.current_date.dayOfWeek()
            days_to_saturday = (day_of_week - 6) % 7
            start_date = self.current_date.addDays(-days_to_saturday)
            end_date = start_date.addDays(6)
        elif self.current_view == "month":
            start_date = QDate(self.current_date.year(), self.current_date.month(), 1)
            end_date = start_date.addMonths(1).addDays(-1)
        
        # تحميل المواعيد من قاعدة البيانات
        appointments = self.db_handler.get_appointments_by_date_range(
            start_date.toString("yyyy-MM-dd"),
            end_date.toString("yyyy-MM-dd")
        )
        
        # تحديث العرض بالمواعيد
        if self.current_view == "day" and hasattr(self, 'day_view'):
            self.day_view.update_appointments(appointments)
        elif self.current_view == "week" and hasattr(self, 'week_view'):
            self.week_view.update_appointments(appointments)
        elif self.current_view == "month" and hasattr(self, 'month_view'):
            self.month_view.update_appointments(appointments)
    
    def on_time_slot_clicked(self, start_time, end_time):
        """معالجة حدث النقر على فترة زمنية"""
        # التحقق مما إذا كانت الفترة الزمنية تحتوي على موعد
        for slot in (self.day_view.time_slots.values() if self.current_view == "day" else []):
            if slot.is_selected and slot.appointment_data:
                # تعديل الموعد الموجود
                self.edit_appointment(slot.appointment_data['id'])
                return
        
        # إضافة موعد جديد
        self.add_appointment(start_time, end_time)
    
    def on_date_clicked(self, date):
        """معالجة حدث النقر على تاريخ في عرض الشهر"""
        self.current_date = date
        self.change_view("day")
    
    def add_appointment(self, start_time=None, end_time=None):
        """إضافة موعد جديد"""
        dialog = AppointmentDialog(self.db_handler, start_time, end_time, parent=self)
        
        if dialog.exec_() == QDialog.Accepted:
            appointment_data = dialog.get_appointment_data()
            
            # التحقق من صحة البيانات
            if not appointment_data['patient_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المريض")
                return
            
            # إضافة الموعد إلى قاعدة البيانات
            appointment_id = self.db_handler.add_appointment(
                appointment_data['patient_id'],
                appointment_data['appointment_datetime'],
                appointment_data['duration_minutes'],
                appointment_data['notes']
            )
            
            if appointment_id:
                QMessageBox.information(self, "نجاح", "تمت إضافة الموعد بنجاح")
                self.load_appointments()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة الموعد")
    
    def edit_appointment(self, appointment_id):
        """تعديل موعد"""
        appointment_data = self.db_handler.get_appointment(appointment_id)
        
        if not appointment_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على الموعد")
            return
        
        dialog = AppointmentDialog(self.db_handler, appointment_data=appointment_data, parent=self)
        
        if dialog.exec_() == QDialog.Accepted:
            updated_data = dialog.get_appointment_data()
            
            # التحقق من صحة البيانات
            if not updated_data['patient_id']:
                QMessageBox.warning(self, "خطأ", "يرجى اختيار المريض")
                return
            
            # تحديث الموعد في قاعدة البيانات
            success = self.db_handler.update_appointment(
                appointment_id,
                updated_data['patient_id'],
                updated_data['appointment_datetime'],
                updated_data['duration_minutes'],
                updated_data['notes']
            )
            
            if success:
                QMessageBox.information(self, "نجاح", "تم تحديث الموعد بنجاح")
                self.load_appointments()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحديث الموعد")
    
    def delete_appointment(self, appointment_id):
        """حذف موعد"""
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من رغبتك في إلغاء هذا الموعد؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success = self.db_handler.delete_appointment(appointment_id)
            
            if success:
                QMessageBox.information(self, "نجاح", "تم إلغاء الموعد بنجاح")
                self.load_appointments()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إلغاء الموعد")