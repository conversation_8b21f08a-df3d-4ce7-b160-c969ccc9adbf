#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار التصميم الجديد - نافذة مبسطة لاختبار الأنماط
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                             QTableWidget, QTableWidgetItem, QTabWidget,
                             QFrame, QGroupBox, QComboBox, QTextEdit)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_style()
    
    def init_ui(self):
        self.setWindowTitle("اختبار التصميم الحديث")
        self.setGeometry(100, 100, 800, 600)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # العنوان
        title = QLabel("اختبار التصميم الحديث")
        title.setProperty("class", "title")
        main_layout.addWidget(title)
        
        # التبويبات
        tabs = QTabWidget()
        
        # تبويبة الأزرار
        buttons_tab = self.create_buttons_tab()
        tabs.addTab(buttons_tab, "الأزرار")
        
        # تبويبة النماذج
        forms_tab = self.create_forms_tab()
        tabs.addTab(forms_tab, "النماذج")
        
        # تبويبة الجداول
        tables_tab = self.create_tables_tab()
        tabs.addTab(tables_tab, "الجداول")
        
        main_layout.addWidget(tabs)
    
    def create_buttons_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # أزرار ملونة
        colors_group = QGroupBox("الأزرار الملونة")
        colors_layout = QHBoxLayout(colors_group)
        
        primary_btn = QPushButton("Primary")
        primary_btn.setProperty("class", "primary")
        colors_layout.addWidget(primary_btn)
        
        success_btn = QPushButton("Success")
        success_btn.setProperty("class", "success")
        colors_layout.addWidget(success_btn)
        
        warning_btn = QPushButton("Warning")
        warning_btn.setProperty("class", "warning")
        colors_layout.addWidget(warning_btn)
        
        error_btn = QPushButton("Error")
        error_btn.setProperty("class", "error")
        colors_layout.addWidget(error_btn)
        
        flat_btn = QPushButton("Flat")
        flat_btn.setProperty("class", "flat")
        colors_layout.addWidget(flat_btn)
        
        layout.addWidget(colors_group)
        
        # أزرار عادية
        normal_group = QGroupBox("أزرار عادية")
        normal_layout = QHBoxLayout(normal_group)
        
        normal_btn = QPushButton("زر عادي")
        normal_layout.addWidget(normal_btn)
        
        disabled_btn = QPushButton("زر معطل")
        disabled_btn.setEnabled(False)
        normal_layout.addWidget(disabled_btn)
        
        layout.addWidget(normal_group)
        layout.addStretch()
        
        return widget
    
    def create_forms_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # مجموعة النماذج
        forms_group = QGroupBox("حقول الإدخال")
        forms_layout = QVBoxLayout(forms_group)
        
        # حقل نص
        name_input = QLineEdit()
        name_input.setPlaceholderText("أدخل اسمك")
        forms_layout.addWidget(QLabel("الاسم:"))
        forms_layout.addWidget(name_input)
        
        # قائمة منسدلة
        combo = QComboBox()
        combo.addItems(["الخيار الأول", "الخيار الثاني", "الخيار الثالث"])
        forms_layout.addWidget(QLabel("اختر خياراً:"))
        forms_layout.addWidget(combo)
        
        # منطقة نص
        text_area = QTextEdit()
        text_area.setPlaceholderText("أدخل نصاً طويلاً هنا...")
        text_area.setMaximumHeight(100)
        forms_layout.addWidget(QLabel("الملاحظات:"))
        forms_layout.addWidget(text_area)
        
        layout.addWidget(forms_group)
        layout.addStretch()
        
        return widget
    
    def create_tables_tab(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # جدول
        table = QTableWidget(3, 4)
        table.setHorizontalHeaderLabels(["العمود الأول", "العمود الثاني", "العمود الثالث", "العمود الرابع"])
        
        # إضافة بيانات تجريبية
        for row in range(3):
            for col in range(4):
                item = QTableWidgetItem(f"بيانات {row+1}-{col+1}")
                table.setItem(row, col, item)
        
        layout.addWidget(table)
        
        return widget
    
    def load_style(self):
        """تحميل الأنماط"""
        try:
            style_path = os.path.join(os.path.dirname(__file__), 'assets', 'qt_modern_style.css')
            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as f:
                    style = f.read()
                self.setStyleSheet(style)
                print("تم تحميل الأنماط بنجاح")
            else:
                print("لم يتم العثور على ملف الأنماط")
        except Exception as e:
            print(f"خطأ في تحميل الأنماط: {e}")

def main():
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()