from PIL import Image, ImageDraw
import os

def create_dropdown_icon():
    """إنشاء أيقونة القائمة المنسدلة"""
    # إنشاء صورة جديدة بخلفية شفافة
    img = Image.new('RGBA', (12, 12), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # رسم مثلث للأسفل
    draw.polygon([(2, 4), (10, 4), (6, 8)], fill=(52, 152, 219))
    
    # حفظ الصورة
    icon_path = os.path.join(os.path.dirname(__file__), 'dropdown.png')
    img.save(icon_path)
    
    return icon_path

if __name__ == "__main__":
    # إنشاء الأيقونة عند تشغيل الملف مباشرة
    icon_path = create_dropdown_icon()
    print(f"تم إنشاء أيقونة القائمة المنسدلة في: {icon_path}")