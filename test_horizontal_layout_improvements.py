#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات التخطيط الأفقي في تبويبة علاج الأسنان
Test horizontal layout improvements in dental treatment tab
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from ui.tabs.dental_treatments_tab import TreatmentOptionsWidget

class HorizontalLayoutTestWindow(QMainWindow):
    """نافذة اختبار التخطيط الأفقي المحسن"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("اختبار التخطيط الأفقي المحسن في تبويبة علاج الأسنان")
        self.setGeometry(50, 50, 1800, 800)  # عرض أكبر لاستيعاب التخطيط الأفقي
        
        # الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الاختبار
        title = QLabel("اختبار التخطيط الأفقي المحسن في تبويبة علاج الأسنان")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # معلومات التحسين
        info_label = QLabel("""
        🎯 تحسينات التخطيط الأفقي المطبقة:
        
        ✅ إعادة تنظيم التخطيط:
        • تغيير من شبكة 2x4 إلى صف أفقي واحد يحتوي على 8 مجموعات
        • ترتيب المجموعات: [أسعار اللبية] [لبية] [أسعار الترميمية] [ترميمية] [أسعار التيجان] [تيجان] [أسعار الجراحة] [جراحة]
        • استخدام QHBoxLayout بدلاً من QGridLayout للحصول على ترتيب أفقي
        
        ✅ تقليل العرض الأدنى للمجموعات:
        • اللبية والترميمية والجراحة: 160px (انخفاض من 200px)
        • التيجان وأسعار التيجان: 170px (لاستيعاب النصوص الطويلة مثل "زيركون مغطى إيماكس")
        • تحسين استغلال المساحة الأفقية بنسبة 20-25%
        
        ✅ تحسين أحجام العناصر الداخلية:
        • حقول الأسعار: عرض 120px (انخفاض من 140px)، خط 11px
        • مربعات الاختيار: عرض 130px (انخفاض من 160px)، خط 13px
        • مربعات التيجان: عرض 140px، خط 12px (للنصوص الطويلة)
        • ارتفاع موحد 22px لجميع العناصر
        
        ✅ تحسين استغلال المساحة:
        • توزيع متساوي لجميع المجموعات الثمانية على العرض المتاح
        • استخدام setStretch لضمان التوزيع المتوازن
        • مسافات أقل بين المجموعات (10px بدلاً من 15px)
        • هوامش محسنة (10px بدلاً من 15px)
        
        📊 مقارنة الأبعاد:
        
        قبل التحسين:
        • التخطيط: شبكة 2x4 (صفان × 4 أعمدة)
        • عرض المجموعات: 200px
        • عرض حقول الأسعار: 140px
        • عرض مربعات الاختيار: 160px
        • المساحة المستغلة: ~65%
        
        بعد التحسين:
        • التخطيط: صف أفقي واحد (8 مجموعات)
        • عرض المجموعات: 160-170px
        • عرض حقول الأسعار: 120px
        • عرض مربعات الاختيار: 130-140px
        • المساحة المستغلة: ~85%
        
        🎨 الفوائد المحققة:
        • استغلال أفضل للمساحة الأفقية المتاحة
        • عرض جميع المجموعات في مستوى بصري واحد
        • سهولة المقارنة بين الأسعار والخيارات
        • تقليل الحاجة للتمرير العمودي
        • مظهر أكثر تنظيماً وحداثة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d5dbdb;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #2c3e50;
                line-height: 1.4;
            }
        """)
        layout.addWidget(info_label)
        
        # إضافة حاوية خيارات المعالجة مع التخطيط الأفقي الجديد
        options_title = QLabel("⚙️ حاوية خيارات المعالجة (تخطيط أفقي محسن - 8 مجموعات في صف واحد)")
        options_title.setAlignment(Qt.AlignCenter)
        options_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #007bff;
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 8px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(options_title)
        
        # إنشاء حاوية خيارات المعالجة
        self.treatment_options = TreatmentOptionsWidget()
        layout.addWidget(self.treatment_options)
        
        # أزرار اختبار
        buttons_layout = QHBoxLayout()
        
        # زر اختبار تحديد خيارات من جميع الفئات
        test_all_categories_btn = QPushButton("اختبار تحديد من جميع الفئات")
        test_all_categories_btn.clicked.connect(self.test_all_categories)
        test_all_categories_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        buttons_layout.addWidget(test_all_categories_btn)
        
        # زر اختبار النصوص الطويلة
        test_long_text_btn = QPushButton("اختبار النصوص الطويلة")
        test_long_text_btn.clicked.connect(self.test_long_text)
        test_long_text_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        buttons_layout.addWidget(test_long_text_btn)
        
        # زر اختبار الأسعار العالية
        test_high_prices_btn = QPushButton("اختبار الأسعار العالية")
        test_high_prices_btn.clicked.connect(self.test_high_prices)
        test_high_prices_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #e8690b;
            }
        """)
        buttons_layout.addWidget(test_high_prices_btn)
        
        # زر مسح جميع التحديدات
        clear_all_btn = QPushButton("مسح جميع التحديدات")
        clear_all_btn.clicked.connect(self.clear_all_selections)
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        buttons_layout.addWidget(clear_all_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # تعليمات الاختبار
        instructions_label = QLabel("""
        📋 تعليمات اختبار التخطيط الأفقي المحسن:
        
        🔍 ما يجب ملاحظته:
        • جميع المجموعات الثمانية معروضة في صف أفقي واحد
        • ترتيب منطقي: كل مجموعة أسعار بجانب مجموعة الخيارات المقابلة
        • استغلال أمثل للمساحة الأفقية المتاحة
        • أحجام محسنة للنصوص والحقول
        
        🧪 اختبارات يمكن إجراؤها:
        • لاحظ التوزيع المتساوي للمجموعات الثمانية
        • تحقق من وضوح جميع النصوص بدون تقطيع
        • اختبر النصوص الطويلة مثل "زيركون مغطى إيماكس"
        • جرب تغيير حجم النافذة ولاحظ التكيف
        • استخدم أزرار الاختبار لتجربة سيناريوهات مختلفة
        
        ✅ النتائج المتوقعة:
        • عرض واضح لجميع المجموعات في مستوى بصري واحد
        • استغلال أفضل للمساحة الأفقية (زيادة ~20%)
        • سهولة المقارنة بين الأسعار والخيارات
        • مظهر أكثر تنظيماً وحداثة
        • تحسين تجربة المستخدم بشكل عام
        """)
        instructions_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 11px;
                color: #856404;
                border: 1px solid #ffeaa7;
                line-height: 1.4;
                margin-top: 15px;
            }
        """)
        layout.addWidget(instructions_label)
        
        # نتيجة الاختبار
        result_label = QLabel("✅ تم تحسين التخطيط الأفقي بنجاح!")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 12px;
                background-color: #d5f4e6;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin-top: 15px;
            }
        """)
        layout.addWidget(result_label)
        
    def test_all_categories(self):
        """اختبار تحديد خيارات من جميع الفئات"""
        test_options = [
            "endodontic_Vital",
            "restorative_كومبوزت", 
            "crowns_زيركون مغطى إيماكس",  # نص طويل
            "surgery_خلع ضرس عقل"
        ]
        
        for option_key in test_options:
            if option_key in self.treatment_options.checkboxes:
                self.treatment_options.checkboxes[option_key].setChecked(True)
                
    def test_long_text(self):
        """اختبار النصوص الطويلة"""
        long_text_options = [
            "crowns_زيركون مغطى إيماكس",
            "crowns_زيركون مغطى خزف",
            "crowns_زيركون cutback",
            "endodontic_إعادة معالجة",
            "endodontic_أداة مكسورة"
        ]
        
        for option_key in long_text_options:
            if option_key in self.treatment_options.checkboxes:
                self.treatment_options.checkboxes[option_key].setChecked(True)
                
    def test_high_prices(self):
        """اختبار الأسعار العالية"""
        high_price_modifications = {
            "surgery_زرع": 750000,
            "crowns_زيركون مغطى إيماكس": 350000,
            "endodontic_أداة مكسورة": 450000,
            "surgery_رفع جيب": 400000
        }
        
        for option_key, new_price in high_price_modifications.items():
            if option_key in self.treatment_options.price_spinboxes:
                self.treatment_options.price_spinboxes[option_key].setValue(new_price)
                
    def clear_all_selections(self):
        """مسح جميع التحديدات"""
        for checkbox in self.treatment_options.checkboxes.values():
            checkbox.setChecked(False)

def main():
    """تشغيل الاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين الخط العربي
    app.setLayoutDirection(Qt.RightToLeft)
    
    window = HorizontalLayoutTestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
