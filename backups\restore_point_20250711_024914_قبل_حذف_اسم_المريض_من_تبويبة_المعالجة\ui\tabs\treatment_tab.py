from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QTabWidget)
from PyQt5.QtCore import Qt

# استيراد التبويبات الفرعية
from ui.tabs.dental_treatments_tab import DentalTreatmentsTab

class TreatmentTab(QWidget):
    """تبويبة المعالجة الرئيسية"""
    def __init__(self, db_handler, parent=None):
        super().__init__(parent)
        self.db_handler = db_handler
        self.patient_name = ""
        self.patient_id = None
        self.init_ui()

    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # عنوان المريض
        self.patient_label = QLabel(f"المريض: {self.patient_name}")
        self.patient_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        main_layout.addWidget(self.patient_label)

        # إنشاء منطقة المحتوى مع التبويبات على اليمين
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(15)

        # شريط التبويبات الفرعية - تعيين موضع التبويبات على اليمين (East)
        self.tabs = QTabWidget()
        self.tabs.setTabPosition(QTabWidget.North)

        # تطبيق أنماط حديثة تشبه شريط التبويبات الرئيسي
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                border-radius: 8px;
                background-color: #ffffff;
                margin-left: 0px;
            }

            QTabBar {
                background-color: #1e3a8a;
                border: none;
                border-radius: 8px;
                padding: 5px;
            }

            QTabBar::tab {
                background-color: transparent;
                border: none;
                border-radius: 6px;
                min-height: 45px;
                min-width: 100px; /* Adjusted for horizontal layout */
                padding: 12px 16px;
                margin: 2px 3px; /* Adjusted for horizontal layout */
                color: white;
                font-weight: 600;
                font-size: 13px;
                text-align: center;
                qproperty-alignment: AlignRight | AlignVCenter;
            }

            QTabBar::tab:hover:!selected {
                background-color: rgba(255, 255, 255, 0.15);
            }

            QTabBar::tab:pressed {
                background-color: rgba(255, 255, 255, 0.25);
            }

            QTabBar::tab:selected {
                background-color: white;
                color: #1e3a8a;
                font-weight: 700;
                border: none;
            }

            QTabBar::tab:first {
                margin-left: 5px; /* Adjusted for horizontal layout */
            }

            QTabBar::tab:last {
                margin-right: 5px; /* Adjusted for horizontal layout */
            }
        """)

        # إنشاء التبويبات الفرعية
        self.create_tabs()

        # إضافة التبويبات إلى التخطيط الرئيسي
        content_layout.addWidget(self.tabs)
        main_layout.addLayout(content_layout)

    def create_tabs(self):
        """إنشاء التبويبات الفرعية"""
        # تبويب المعالجات السنية
        self.dental_treatments_tab = DentalTreatmentsTab(self.db_handler)
        self.tabs.addTab(self.dental_treatments_tab, "علاج الاسنان")

        # تبويب الجسور
        bridges = QWidget()
        self.tabs.addTab(bridges, "الجسور")

        # تبويب الفينير
        veneers = QWidget()
        self.tabs.addTab(veneers, "فينير")

        # تبويب التبييض
        whitening = QWidget()
        self.tabs.addTab(whitening, "التبييض")

        # تبويب الأطفال
        children = QWidget()
        self.tabs.addTab(children, "الأطفال")

        # تبويب اللثة
        gums = QWidget()
        self.tabs.addTab(gums, "اللثة")

        # تبويب التقويم
        orthodontics = QWidget()
        self.tabs.addTab(orthodontics, "التقويم")

        # تبويب الزرع
        implants = QWidget()
        self.tabs.addTab(implants, "الزرع")

        # تبويب المتحركة
        removable = QWidget()
        self.tabs.addTab(removable, "المتحركة")

    def set_patient_name(self, name):
        """تعيين اسم المريض"""
        self.patient_name = name
        self.patient_label.setText(f"المريض: {self.patient_name}")

    def set_patient(self, patient_id):
        """تعيين المريض وتحميل خطط العلاج الخاصة به"""
        self.patient_id = patient_id
        patient_data = self.db_handler.get_patient(patient_id)
        if patient_data:
            self.set_patient_name(patient_data['name'])
            # يمكن هنا إضافة كود لتحميل خطط العلاج الخاصة بالمريض
        else:
            self.set_patient_name("لم يتم اختيار مريض")
