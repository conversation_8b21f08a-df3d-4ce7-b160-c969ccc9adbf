#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف اختبار التصميم الجديد لتبويبة المرضى
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
from database.db_handler import DatabaseHandler
from ui.tabs.patients_tab import PatientsTab

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار التصميم الجديد - تبويبة المرضى")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد قاعدة البيانات
        self.db_handler = DatabaseHandler()
        self.db_handler.create_tables()  # إنشاء الجداول إذا لم تكن موجودة
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط رئيسي
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # إضافة تبويبة المرضى
        self.patients_tab = PatientsTab(self.db_handler)
        layout.addWidget(self.patients_tab)
        
        # تحميل الأنماط
        self.load_styles()
    
    def load_styles(self):
        """تحميل الأنماط"""
        try:
            # قراءة ملف الأنماط
            style_path = os.path.join(project_root, 'assets', 'modern_style.css')
            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as f:
                    self.setStyleSheet(f.read())
        except Exception as e:
            print(f"خطأ في تحميل الأنماط: {e}")

def main():
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط للعربية
    font = app.font()
    font.setFamily("Arial")
    font.setPointSize(10)
    app.setFont(font)
    
    window = TestMainWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()