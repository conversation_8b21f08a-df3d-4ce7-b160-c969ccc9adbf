import sys
from datetime import datetime
from PyQt5.QtWidgets import (<PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QTableWidget, QTableWidgetItem, QHeaderView,
                             QMessageBox, QFormLayout, QTextEdit, QSplitter, QFrame,
                             QSpinBox, QComboBox, QGroupBox, QTabWidget, QToolButton,
                             QDialog, QDialogButtonBox, QDateEdit, QCheckBox, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QDate
from PyQt5.QtGui import QIcon, QFont, QCursor, QColor
from ui.style_helper import StyleHelper

class PatientForm(QWidget):
    """نموذج إضافة/تعديل بيانات المريض
    
    Attributes:
        parent (QWidget): العنصر الأب (اختياري)
        name_input (QLineEdit): حقل إدخال اسم المريض
        birth_year_input (QSpinBox): حقل سنة الولادة
        age_input (QSpinBox): حقل العمر
        mobile_input (QLineEdit): حقل رقم الموبايل
        whatsapp_input (QLineEdit): حقل رقم الواتساب
        general_diseases_input (QTextEdit): حقل الأمراض العامة
        medications_input (QTextEdit): حقل الأدوية
        notes_input (QTextEdit): حقل الملاحظات
    """
    def __init__(self, parent=None):
        """تهيئة نموذج المريض
        
        Args:
            parent (QWidget, optional): العنصر الأب. Defaults to None.
        """
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        # تعيين الاتجاه العربي
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # تطبيق أنماط للعناوين (نفس تنسيق رؤوس الجدول)
        self.setStyleSheet("""
            QFormLayout QLabel {
                color: #495057;
                font-weight: bold;
                padding: 8px 0px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
        """)
        
        # تطبيق تنسيق موحد لجميع الحقول مع ارتفاعات متناسقة
        field_style = """
            QLineEdit, QSpinBox {
                text-align: right;
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 14px;
                background-color: white;
                min-height: 35px;
                max-height: 35px;
            }
            QTextEdit {
                text-align: right;
                padding: 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 14px;
                background-color: white;
                min-height: 80px;
                max-height: 80px;
            }
            QLineEdit:focus, QSpinBox:focus, QTextEdit:focus {
                border: 2px solid #007bff;
                outline: none;
            }
        """

        # حقول الإدخال مع تطبيق التنسيق الموحد
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المريض")

        self.birth_year_input = QSpinBox()
        self.birth_year_input.setRange(1900, datetime.now().year)
        # تعيين القيمة الافتراضية للحد الأدنى لتفعيل النص الخاص
        self.birth_year_input.setValue(1900)
        # إظهار حقل فارغ عندما تكون القيمة في الحد الأدنى
        self.birth_year_input.setSpecialValueText("")
        # إزالة أسهم التحكم
        self.birth_year_input.setButtonSymbols(QSpinBox.NoButtons)
        self.birth_year_input.valueChanged.connect(self.calculate_age)
        # تطبيق التنسيق من اليمين إلى اليسار
        self.birth_year_input.setAlignment(Qt.AlignRight)
        self.birth_year_input.setLayoutDirection(Qt.RightToLeft)

        self.age_input = QSpinBox()
        self.age_input.setRange(0, 120)
        # تعيين القيمة الافتراضية للحد الأدنى لتفعيل النص الخاص
        self.age_input.setValue(0)
        # إظهار حقل فارغ عندما تكون القيمة في الحد الأدنى
        self.age_input.setSpecialValueText("")
        # إزالة أسهم التحكم
        self.age_input.setButtonSymbols(QSpinBox.NoButtons)
        self.age_input.valueChanged.connect(self.calculate_birth_year)
        # تطبيق التنسيق من اليمين إلى اليسار
        self.age_input.setAlignment(Qt.AlignRight)
        self.age_input.setLayoutDirection(Qt.RightToLeft)

        self.mobile_input = QLineEdit()
        self.mobile_input.setPlaceholderText("أدخل رقم الموبايل")

        self.whatsapp_input = QLineEdit()
        self.whatsapp_input.setPlaceholderText("أدخل رقم الواتساب")

        self.general_diseases_input = QTextEdit()
        self.general_diseases_input.setPlaceholderText("أدخل الأمراض العامة")
        # إزالة setMaximumHeight لأنه محدد في CSS

        self.medications_input = QTextEdit()
        self.medications_input.setPlaceholderText("أدخل الأدوية")
        # إزالة setMaximumHeight لأنه محدد في CSS

        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("أدخل الملاحظات")
        # إزالة setMaximumHeight لأنه محدد في CSS

        # تطبيق التنسيق على جميع الحقول
        self.setStyleSheet(field_style)
        
        # إنشاء عناوين بتنسيق موحد مع ارتفاع متغير حسب نوع الحقل
        def create_label(text, is_text_field=False):
            label = QLabel(text)
            # تطبيق التنسيق العربي بدون رموز Unicode المشكلة
            label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
            label.setLayoutDirection(Qt.RightToLeft)
            label.setTextFormat(Qt.PlainText)
            label.setText(text)  # النص العادي بدون رموز Unicode

            # تحديد الارتفاع بناءً على نوع الحقل
            if is_text_field:
                # ارتفاع موحد مع حقول النصوص الطويلة (QTextEdit)
                height_style = "min-height: 80px; max-height: 80px;"
            else:
                # ارتفاع موحد مع الحقول العادية (QLineEdit, QSpinBox)
                height_style = "min-height: 35px; max-height: 35px;"

            label.setStyleSheet(f"""
                QLabel {{
                    color: #495057;
                    font-weight: bold;
                    padding: 8px;
                    text-align: right;
                    min-width: 120px;
                    max-width: 120px;
                    {height_style}
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    background-color: #f8f9fa;
                    qproperty-alignment: AlignRight;
                }}
            """)
            return label

        # إضافة الحقول إلى النموذج مع توحيد الارتفاعات
        form_layout.addRow(create_label("الاسم"), self.name_input)

        # تخطيط العمر وسنة الولادة
        age_layout = QHBoxLayout()
        age_layout.addWidget(self.birth_year_input)
        age_layout.addWidget(QLabel("العمر:"))
        age_layout.addWidget(self.age_input)
        form_layout.addRow(create_label("سنة الولادة"), age_layout)

        form_layout.addRow(create_label("رقم الموبايل"), self.mobile_input)
        form_layout.addRow(create_label("رقم الواتساب"), self.whatsapp_input)

        # حقول النصوص الطويلة مع عناوين بارتفاع موحد
        form_layout.addRow(create_label("الأمراض العامة", is_text_field=True), self.general_diseases_input)
        form_layout.addRow(create_label("الأدوية", is_text_field=True), self.medications_input)
        form_layout.addRow(create_label("ملاحظات", is_text_field=True), self.notes_input)
        
        # إضافة النموذج إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        

    
    def calculate_age(self):
        """حساب العمر من سنة الولادة - التعامل مع القيم الفارغة

        يحسب العمر بناءً على سنة الولادة المحددة ويحدث حقل العمر تلقائياً

        Returns:
            int: العمر المحسوب
        """
        try:
            birth_year = self.birth_year_input.value()
            # التحقق من أن سنة الولادة ليست فارغة (الحد الأدنى)
            if birth_year <= 1900:
                # إذا كانت سنة الولادة فارغة، اجعل العمر فارغاً أيضاً
                self.age_input.blockSignals(True)
                self.age_input.setValue(0)  # القيمة الخاصة للحقل الفارغ
                self.age_input.blockSignals(False)
                return 0

            current_year = datetime.now().year
            age = current_year - birth_year
            # التأكد من أن العمر في النطاق المعقول
            if age < 0 or age > 120:
                age = 0

            self.age_input.blockSignals(True)
            self.age_input.setValue(age)
            self.age_input.blockSignals(False)
            return age
        except Exception as e:
            logging.error(f"Error calculating age: {str(e)}")
            return 0
    
    def calculate_birth_year(self):
        """حساب سنة الولادة من العمر - التعامل مع القيم الفارغة"""
        try:
            age = self.age_input.value()
            # التحقق من أن العمر ليس فارغاً (الحد الأدنى)
            if age <= 0:
                # إذا كان العمر فارغاً، اجعل سنة الولادة فارغة أيضاً
                self.birth_year_input.blockSignals(True)
                self.birth_year_input.setValue(1900)  # القيمة الخاصة للحقل الفارغ
                self.birth_year_input.blockSignals(False)
                return

            current_year = datetime.now().year
            birth_year = current_year - age

            # التأكد من أن سنة الولادة في النطاق المعقول
            if birth_year < 1900 or birth_year > current_year:
                birth_year = 1900  # القيمة الافتراضية للحقل الفارغ

            self.birth_year_input.blockSignals(True)
            self.birth_year_input.setValue(birth_year)
            self.birth_year_input.blockSignals(False)
        except Exception as e:
            logging.error(f"Error calculating birth year: {str(e)}")
            # في حالة الخطأ، اجعل سنة الولادة فارغة
            self.birth_year_input.blockSignals(True)
            self.birth_year_input.setValue(1900)
            self.birth_year_input.blockSignals(False)
    
    def set_patient_data(self, patient_data):
        """تعيين بيانات المريض في النموذج"""
        if patient_data:
            self.name_input.setText(patient_data.get('name', ''))
            
            birth_year = patient_data.get('birth_year')
            if birth_year:
                try:
                    birth_year_value = int(birth_year)
                    # التحقق من أن سنة الولادة في النطاق المعقول
                    if birth_year_value >= 1900 and birth_year_value <= datetime.now().year:
                        self.birth_year_input.setValue(birth_year_value)
                        self.calculate_age()
                    else:
                        # قيمة غير معقولة، اجعل الحقل فارغاً
                        self.birth_year_input.setValue(1900)
                        self.age_input.setValue(0)
                except (ValueError, TypeError):
                    # في حالة عدم صحة البيانات، اجعل الحقول فارغة
                    self.birth_year_input.setValue(1900)
                    self.age_input.setValue(0)
            else:
                # لا توجد بيانات سنة الولادة، اجعل الحقول فارغة
                self.birth_year_input.setValue(1900)
                self.age_input.setValue(0)
            
            self.mobile_input.setText(patient_data.get('mobile', ''))
            self.whatsapp_input.setText(patient_data.get('whatsapp', ''))
            self.general_diseases_input.setText(patient_data.get('general_diseases', ''))
            self.medications_input.setText(patient_data.get('medications', ''))
            self.notes_input.setText(patient_data.get('notes', ''))
    
    def get_patient_data(self):
        """الحصول على بيانات المريض من النموذج - التعامل مع القيم الفارغة"""
        # التعامل مع سنة الولادة الفارغة
        birth_year = self.birth_year_input.value()
        if birth_year <= 1900:
            birth_year = None  # حفظ كقيمة فارغة في قاعدة البيانات

        return {
            'name': self.name_input.text().strip(),
            'birth_year': birth_year,
            'mobile': self.mobile_input.text().strip(),
            'whatsapp': self.whatsapp_input.text().strip(),
            'general_diseases': self.general_diseases_input.toPlainText().strip(),
            'medications': self.medications_input.toPlainText().strip(),
            'notes': self.notes_input.toPlainText().strip()
        }
    
    def clear_form(self):
        """مسح النموذج - تعيين القيم الفارغة"""
        self.name_input.clear()
        # تعيين القيم الفارغة لحقول سنة الولادة والعمر
        self.birth_year_input.setValue(1900)  # القيمة الخاصة للحقل الفارغ
        self.age_input.setValue(0)  # القيمة الخاصة للحقل الفارغ
        self.mobile_input.clear()
        self.whatsapp_input.clear()
        self.general_diseases_input.clear()
        self.medications_input.clear()
        self.notes_input.clear()

class PatientDialog(QDialog):
    """نافذة حوار إضافة/تعديل المريض"""
    def __init__(self, parent=None, patient_data=None):
        super().__init__(parent)
        self.patient_data = patient_data
        self.init_ui()
    
    def init_ui(self):
        # عنوان النافذة
        self.setWindowTitle("إضافة مريض جديد" if not self.patient_data else "تعديل بيانات المريض")
        self.setMinimumWidth(500)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج بيانات المريض
        self.patient_form = PatientForm()
        main_layout.addWidget(self.patient_form)
        
        # تعيين بيانات المريض إذا كانت متوفرة
        if self.patient_data:
            self.patient_form.set_patient_data(self.patient_data)
        
        # أزرار الحوار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)
        
        # ربط أزرار النموذج
        self.patient_form.save_button.clicked.connect(self.accept)
        self.patient_form.cancel_button.clicked.connect(self.reject)
    
    def get_patient_data(self):
        """الحصول على بيانات المريض من النموذج"""
        return self.patient_form.get_patient_data()



class PatientsTab(QWidget):
    # إشارة اختيار المريض
    patient_selected = pyqtSignal(int)  # معرف المريض
    
    def __init__(self, db_handler):
        super().__init__()
        self.db_handler = db_handler
        self.current_patient_id = None
        self.init_ui()
        self.load_patients()
    
    def init_ui(self):
        # التخطيط الرئيسي الأفقي
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # القسم الأيمن: قائمة المرضى مع البحث
        patients_widget = QWidget()
        patients_layout = QVBoxLayout(patients_widget)
        patients_layout.setContentsMargins(0, 0, 0, 0)
        patients_layout.setSpacing(10)

        # إضافة مربع البحث في أعلى القسم الأيمن
        search_frame = QFrame()
        search_frame.setFrameShape(QFrame.StyledPanel)
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        search_layout = QVBoxLayout(search_frame)
        search_layout.setSpacing(10)

        # حذف عنوان قسم البحث حسب المطلوب

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث بالاسم أو رقم الموبايل...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ced4da;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
        self.search_input.textChanged.connect(self.search_patients)
        search_layout.addWidget(self.search_input)

        patients_layout.addWidget(search_frame)

        # عنوان قائمة المرضى
        patients_title = QLabel("👥 قائمة المرضى")
        patients_title_font = QFont()
        patients_title_font.setPointSize(12)
        patients_title_font.setBold(True)
        patients_title.setFont(patients_title_font)
        patients_title.setStyleSheet("color: #007bff; margin-bottom: 10px;")
        patients_layout.addWidget(patients_title)
        
        self.patients_table = QTableWidget()
        self.patients_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                gridline-color: transparent;
                selection-background-color: #007bff;
                selection-color: white;
                outline: none;
                show-decoration-selected: 1;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                color: #495057;
                border: 1px solid #dee2e6;
                padding: 8px;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
                outline: none;
            }
            QTableWidget::item:selected {
                background-color: #007bff !important;
                color: white !important;
                border: none;
                outline: none;
            }
            QTableWidget::item:focus {
                background-color: #007bff !important;
                color: white !important;
                border: none;
                outline: none;
            }
        """)
        self.patients_table.setColumnCount(3)
        self.patients_table.setHorizontalHeaderLabels(["👤 الاسم", "📱 رقم الموبايل", "💬 رقم الواتساب"])
        self.patients_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.patients_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.patients_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.patients_table.verticalHeader().setVisible(False)
        self.patients_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.patients_table.setSelectionMode(QTableWidget.SingleSelection)
        self.patients_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.patients_table.itemClicked.connect(self.on_patient_selected)
        self.patients_table.setAlternatingRowColors(True)
        self.patients_table.setLayoutDirection(Qt.RightToLeft)

        # إضافة تأثير اليد للجدول برمجياً
        self.patients_table.setCursor(QCursor(Qt.PointingHandCursor))

        # إعدادات إضافية للجدول لتحسين التفاعل
        self.patients_table.setFocusPolicy(Qt.NoFocus)  # إزالة التركيز المرئي
        self.patients_table.setShowGrid(False)  # إخفاء خطوط الشبكة
        self.patients_table.setSelectionBehavior(QTableWidget.SelectRows)  # تحديد الصف كاملاً
        self.patients_table.setSelectionMode(QTableWidget.SingleSelection)  # تحديد واحد فقط

        # إنشاء فئة مخصصة للجدول مع تظليل الصف
        self.setup_custom_table_behavior()

        patients_layout.addWidget(self.patients_table)

        # القسم الأيسر: نموذج إضافة/عرض المريض (بدون البحث)
        self.patient_form_container = self.create_patient_form_container()

        # إضافة الحاويات إلى التخطيط الرئيسي (من اليمين إلى اليسار)
        main_layout.addWidget(patients_widget, 2)  # نسبة 2 - قائمة المرضى مع البحث (يمين)
        main_layout.addWidget(self.patient_form_container, 2)  # نسبة 2 - نموذج المريض (يسار)

    def create_patient_form_container(self):
        """إنشاء حاوية نموذج إضافة/عرض المريض"""
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(10, 0, 0, 0)
        container_layout.setSpacing(15)

        # تم نقل شريط البحث إلى القسم الأيمن مع قائمة المرضى

        # نموذج المريض
        form_frame = QFrame()
        form_frame.setFrameShape(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(10)

        # عنوان النموذج
        self.form_title = QLabel("📋 معلومات المريض")
        form_title_font = QFont()
        form_title_font.setPointSize(12)
        form_title_font.setBold(True)
        self.form_title.setFont(form_title_font)
        self.form_title.setStyleSheet("color: #007bff; margin-bottom: 10px;")
        form_layout.addWidget(self.form_title)

        # نموذج بيانات المريض
        self.patient_form = PatientForm()
        form_layout.addWidget(self.patient_form)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        self.save_button = QPushButton("💾 حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #007bff, stop: 1 #0056b3);
                border: 1px solid #0056b3;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #0056b3, stop: 1 #004085);
                border-color: #004085;
            }
            QPushButton:pressed {
                background: #004085;
            }
        """)
        self.save_button.clicked.connect(self.save_patient)

        self.delete_button = QPushButton("🗑️ حذف المريض")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #dc3545, stop: 1 #bd2130);
                border: 1px solid #bd2130;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #bd2130, stop: 1 #a71e2a);
                border-color: #a71e2a;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                border-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.delete_button.clicked.connect(self.delete_patient)
        self.delete_button.setEnabled(False)

        # زر مريض جديد
        self.new_patient_button = QPushButton("👤 مريض جديد")
        self.new_patient_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #28a745, stop: 1 #1e7e34);
                border: 1px solid #1e7e34;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #1e7e34, stop: 1 #155724);
                border-color: #155724;
            }
            QPushButton:pressed {
                background: #155724;
            }
        """)
        self.new_patient_button.clicked.connect(self.new_patient)

        # زر تعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #fd7e14, stop: 1 #e8590c);
                border: 1px solid #e8590c;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                min-height: 30px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e8590c, stop: 1 #d63384);
                border-color: #d63384;
            }
            QPushButton:pressed {
                background: #d63384;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                border-color: #6c757d;
                color: #adb5bd;
            }
        """)
        self.edit_button.clicked.connect(self.edit_patient)
        self.edit_button.setEnabled(False)  # معطل حتى يتم اختيار مريض

        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(self.new_patient_button)
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addStretch()

        # تطبيق تأثير اليد للأزرار
        self.save_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.new_patient_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.edit_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.delete_button.setCursor(QCursor(Qt.PointingHandCursor))

        form_layout.addLayout(buttons_layout)
        container_layout.addWidget(form_frame)

        return container

    def create_patient_details_container(self):
        """إنشاء حاوية تفاصيل المريض بتصميم جميل ومرن"""
        details_widget = QWidget()
        details_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        main_layout = QVBoxLayout(details_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # رأس التفاصيل مع الأزرار
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)
        
        self.patient_name_detail_label = QLabel("📋 تفاصيل المريض")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        self.patient_name_detail_label.setFont(title_font)
        self.patient_name_detail_label.setStyleSheet("""
            QLabel {
                color: #495057;
                padding: 5px 0px;
            }
        """)
        
        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(8)
        
        self.edit_button = QPushButton("✏️ تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ffc107, stop: 1 #e0a800);
                border: 1px solid #e0a800;
                color: #212529;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e0a800, stop: 1 #d39e00);
                border-color: #d39e00;
            }
            QPushButton:disabled {
                background: #e9ecef;
                border-color: #ced4da;
                color: #6c757d;
            }
        """)
        self.edit_button.setEnabled(False)
        self.edit_button.clicked.connect(self.edit_patient)

        self.delete_button = QPushButton("🗑️ حذف")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #dc3545, stop: 1 #c82333);
                border: 1px solid #c82333;
                color: white;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #c82333, stop: 1 #bd2130);
                border-color: #bd2130;
            }
            QPushButton:disabled {
                background: #e9ecef;
                border-color: #ced4da;
                color: #6c757d;
            }
        """)
        self.delete_button.setEnabled(False)
        self.delete_button.clicked.connect(self.delete_patient)
        
        buttons_layout.addWidget(self.edit_button)
        buttons_layout.addWidget(self.delete_button)

        # تطبيق تأثير اليد للأزرار
        self.edit_button.setCursor(QCursor(Qt.PointingHandCursor))
        self.delete_button.setCursor(QCursor(Qt.PointingHandCursor))

        header_layout.addWidget(self.patient_name_detail_label)
        header_layout.addStretch()
        header_layout.addLayout(buttons_layout)
        
        main_layout.addLayout(header_layout)
        
        # إضافة خط فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("color: #dee2e6;")
        main_layout.addWidget(separator)
        
        # منطقة التفاصيل القابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
        """)
        
        # محتوى التفاصيل
        details_content = QWidget()
        details_content_layout = QVBoxLayout(details_content)
        details_content_layout.setContentsMargins(5, 5, 5, 5)
        details_content_layout.setSpacing(12)
        
        # إنشاء حقول التفاصيل بتصميم جميل
        self.mobile_label = self.create_detail_field("📱 رقم الموبايل", "")
        self.whatsapp_label = self.create_detail_field("💬 رقم الواتساب", "")
        self.birth_year_label = self.create_detail_field("🎂 سنة الولادة", "")
        self.age_label = self.create_detail_field("👤 العمر", "")
        self.general_diseases_label = self.create_detail_field("🏥 الأمراض العامة", "", is_long_text=True)
        self.medications_label = self.create_detail_field("💊 الأدوية", "", is_long_text=True)
        self.notes_label = self.create_detail_field("📝 ملاحظات", "", is_long_text=True)
        
        # إضافة الحقول إلى التخطيط
        for field in [self.mobile_label, self.whatsapp_label, self.birth_year_label, 
                     self.age_label, self.general_diseases_label, 
                     self.medications_label, self.notes_label]:
            details_content_layout.addWidget(field)
        
        details_content_layout.addStretch()
        
        scroll_area.setWidget(details_content)
        main_layout.addWidget(scroll_area)
        
        return details_widget
    
    def create_detail_field(self, title, content, is_long_text=False):
        """إنشاء حقل تفاصيل بتصميم جميل"""
        container = QFrame()
        container.setFrameShape(QFrame.StyledPanel)
        container.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-weight: bold;
                font-size: 13px;
                margin-bottom: 3px;
            }
        """)
        layout.addWidget(title_label)
        
        # المحتوى
        content_label = QLabel(content if content else "لا توجد بيانات")
        content_label.setStyleSheet("""
            QLabel {
                color: #212529;
                font-size: 14px;
                padding: 4px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        content_label.setWordWrap(True)
        if is_long_text:
            content_label.setMinimumHeight(60)
            content_label.setAlignment(Qt.AlignTop)
        
        layout.addWidget(content_label)
        
        # حفظ مرجع للمحتوى للتحديث لاحقاً
        container.content_label = content_label
        
        return container
    

    

    
    def load_patients(self):
        """تحميل قائمة المرضى من قاعدة البيانات
        
        يقوم بجلب جميع المرضى من قاعدة البيانات وعرضهم في الجدول
        
        Returns:
            bool: True إذا نجح التحميل، False إذا فشل
        """
        try:
            patients = self.db_handler.get_all_patients()
            if patients is not None:
                self.update_patients_table(patients)
                return True
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحميل قائمة المرضى")
                logging.warning("Failed to load patients: returned None")
                return False
        except Exception as e:
            error_msg = f"حدث خطأ أثناء تحميل المرضى: {str(e)}"
            QMessageBox.critical(self, "خطأ", error_msg)
            logging.error(error_msg)
            return False
    
    def search_patients(self):
        """البحث عن المرضى"""
        try:
            search_term = self.search_input.text().strip()
            if search_term:
                patients = self.db_handler.search_patients(search_term)
            else:
                patients = self.db_handler.get_all_patients()
                
            if patients is not None:
                self.update_patients_table(patients)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في البحث عن المرضى")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء البحث: {str(e)}")
    
    def update_patients_table(self, patients):
        """تحديث جدول المرضى"""
        self.patients_table.setRowCount(0)
        
        if not patients:
            return
        
        for row, patient in enumerate(patients):
            if not patient or 'id' not in patient or 'name' not in patient:
                continue
                
            self.patients_table.insertRow(row)
            
            # اسم المريض
            name_item = QTableWidgetItem(str(patient['name']) if patient['name'] else '')
            name_item.setData(Qt.UserRole, patient['id'])  # تخزين معرف المريض
            self.patients_table.setItem(row, 0, name_item)
            
            # رقم الموبايل
            mobile = patient.get('mobile', '') or ''
            mobile_item = QTableWidgetItem(str(mobile))
            self.patients_table.setItem(row, 1, mobile_item)

            # رقم الواتساب
            whatsapp = patient.get('whatsapp', '') or ''
            whatsapp_item = QTableWidgetItem(str(whatsapp))
            self.patients_table.setItem(row, 2, whatsapp_item)
    
    def on_patient_selected(self, item):
        """معالجة حدث اختيار المريض"""
        if not item:
            return
            
        row = item.row()
        name_item = self.patients_table.item(row, 0)
        if not name_item:
            return
            
        patient_id = name_item.data(Qt.UserRole)
        if not patient_id:
            return

        # تحديث النموذج ببيانات المريض المختار
        self.load_patient_to_form(patient_id)

        # إرسال إشارة اختيار المريض
        self.patient_selected.emit(patient_id)

    def load_patient_to_form(self, patient_id):
        """تحميل بيانات المريض إلى النموذج"""
        self.current_patient_id = patient_id
        patient_data = self.db_handler.get_patient(patient_id)

        if patient_data:
            # تحديث عنوان النموذج
            self.form_title.setText(f"📋 معلومات المريض: {patient_data['name']}")
            self.form_title.setStyleSheet("color: #007bff; margin-bottom: 10px;")

            # تحميل البيانات في النموذج
            self.patient_form.set_patient_data(patient_data)

            # تفعيل أزرار التعديل والحذف
            self.edit_button.setEnabled(True)
            self.delete_button.setEnabled(True)
        else:
            self.clear_form()

    def save_patient(self):
        """حفظ بيانات المريض (إضافة أو تحديث)
        
        يحفظ بيانات المريض في قاعدة البيانات سواء كان جديداً أو موجوداً
        
        Returns:
            bool: True إذا نجح الحفظ، False إذا فشل
        """
        try:
            patient_data = self.patient_form.get_patient_data()

            # التحقق من صحة البيانات
            if not patient_data['name'].strip():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المريض")
                return False
            
            # التحقق من صحة رقم الموبايل إذا تم إدخاله
            mobile = patient_data['mobile'].strip()
            if mobile and not mobile.replace('+', '').replace('-', '').replace(' ', '').isdigit():
                QMessageBox.warning(self, "خطأ", "رقم الموبايل غير صحيح")
                return False
            
            # التحقق من صحة رقم الواتساب إذا تم إدخاله
            whatsapp = patient_data['whatsapp'].strip()
            if whatsapp and not whatsapp.replace('+', '').replace('-', '').replace(' ', '').isdigit():
                QMessageBox.warning(self, "خطأ", "رقم الواتساب غير صحيح")
                return False

            if self.current_patient_id:
                # تحديث مريض موجود
                if self.db_handler.update_patient(
                    self.current_patient_id,
                    patient_data['name'],
                    patient_data['birth_year'],
                    patient_data['mobile'],
                    patient_data['whatsapp'],
                    patient_data['general_diseases'],
                    patient_data['medications'],
                    patient_data['notes']
                ):
                    QMessageBox.information(self, "نجاح", "تم تحديث بيانات المريض بنجاح")
                    self.load_patients()
                    # إعادة تحديد المريض في الجدول
                    self.select_patient_in_table(self.current_patient_id)
                else:
                    QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء تحديث بيانات المريض")
            else:
                # إضافة مريض جديد
                patient_id = self.db_handler.add_patient(
                    patient_data['name'],
                    patient_data['birth_year'],
                    patient_data['mobile'],
                    patient_data['whatsapp'],
                    patient_data['general_diseases'],
                    patient_data['medications'],
                    patient_data['notes']
                )

                if patient_id:
                    QMessageBox.information(self, "نجاح", "تمت إضافة المريض بنجاح")
                    self.load_patients()
                    # تحديد المريض الجديد في الجدول
                    self.select_patient_in_table(patient_id)
                    self.patient_selected.emit(patient_id)
                else:
                    QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة المريض")
            return True
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ بيانات المريض: {str(e)}")
            return False

    def clear_form(self):
        """مسح النموذج للإضافة الجديدة"""
        self.current_patient_id = None
        self.patient_form.clear_form()

        # تحديث عنوان النموذج
        self.form_title.setText("📋 معلومات المريض")
        self.form_title.setStyleSheet("color: #007bff; margin-bottom: 10px;")

        # تعطيل زر الحذف
        self.delete_button.setEnabled(False)

        # إلغاء تحديد المريض في الجدول
        self.patients_table.clearSelection()

    def select_patient_in_table(self, patient_id):
        """تحديد مريض في الجدول"""
        for row in range(self.patients_table.rowCount()):
            item = self.patients_table.item(row, 0)
            if item and item.data(Qt.UserRole) == patient_id:
                self.patients_table.selectRow(row)
                break
    
    def delete_patient(self):
        """حذف المريض المحدد"""
        if not self.current_patient_id:
            QMessageBox.warning(self, "تنبيه", "يرجى اختيار مريض للحذف")
            return

        # الحصول على بيانات المريض للتأكيد
        patient_data = self.db_handler.get_patient(self.current_patient_id)
        if not patient_data:
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من رغبتك في حذف المريض '{patient_data['name']}' وجميع بياناته المرتبطة؟\n\n"
            "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            if self.db_handler.delete_patient(self.current_patient_id):
                QMessageBox.information(self, "نجاح", "تم حذف المريض بنجاح")
                self.load_patients()
                self.clear_form()
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء حذف المريض")

    def new_patient(self):
        """إضافة مريض جديد - مسح النموذج للبدء من جديد"""
        try:
            # مسح النموذج مباشرة
            if hasattr(self, 'current_patient_id'):
                self.current_patient_id = None

            # مسح النموذج بأمان
            if hasattr(self, 'patient_form'):
                self.patient_form.clear_form()

            # إلغاء تحديد أي مريض في الجدول
            if hasattr(self, 'patients_table'):
                self.patients_table.clearSelection()

            # تعطيل أزرار التعديل والحذف
            if hasattr(self, 'edit_button'):
                self.edit_button.setEnabled(False)
            if hasattr(self, 'delete_button'):
                self.delete_button.setEnabled(False)

            # تركيز على حقل الاسم
            if hasattr(self, 'patient_form') and hasattr(self.patient_form, 'name_input'):
                self.patient_form.name_input.setFocus()

            # رسالة تأكيد
            QMessageBox.information(self, "مريض جديد", "تم تجهيز النموذج لإضافة مريض جديد")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تجهيز النموذج: {str(e)}")

    def edit_patient(self):
        """تعديل بيانات المريض المحدد"""
        try:
            if not self.current_patient_id:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مريض للتعديل أولاً")
                return

            # التحقق من وجود بيانات للتعديل
            patient_data = self.db_handler.get_patient(self.current_patient_id)
            if not patient_data:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات المريض")
                return

            # تأكيد التعديل
            reply = QMessageBox.question(
                self, "تأكيد التعديل",
                f"هل تريد تعديل بيانات المريض '{patient_data['name']}'؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تمكين النموذج للتعديل
                if hasattr(self, 'patient_form'):
                    # تمكين جميع الحقول
                    self.patient_form.setEnabled(True)

                    # تركيز على حقل الاسم
                    if hasattr(self.patient_form, 'name_input'):
                        self.patient_form.name_input.setFocus()
                        self.patient_form.name_input.selectAll()

                # رسالة إرشادية
                QMessageBox.information(self, "وضع التعديل",
                                      "يمكنك الآن تعديل البيانات ثم الضغط على 'حفظ' لحفظ التغييرات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المريض: {str(e)}")

    def setup_custom_table_behavior(self):
        """إعداد سلوك مخصص للجدول مع تظليل الصف الكامل"""
        # إنشاء فئة مخصصة للجدول
        original_enter_event = self.patients_table.enterEvent
        original_leave_event = self.patients_table.leaveEvent
        original_mouse_move_event = self.patients_table.mouseMoveEvent

        def custom_enter_event(event):
            original_enter_event(event)

        def custom_leave_event(event):
            # إزالة تظليل التمرير عند مغادرة الجدول
            self.patients_table.clearSelection()
            original_leave_event(event)

        def custom_mouse_move_event(event):
            # الحصول على الصف تحت المؤشر
            item = self.patients_table.itemAt(event.pos())
            if item:
                row = item.row()
                # تحديد الصف كاملاً للتظليل
                self.patients_table.selectRow(row)
            original_mouse_move_event(event)

        # تطبيق الأحداث المخصصة
        self.patients_table.enterEvent = custom_enter_event
        self.patients_table.leaveEvent = custom_leave_event
        self.patients_table.mouseMoveEvent = custom_mouse_move_event

        # تمكين تتبع الماوس
        self.patients_table.setMouseTracking(True)