---
description: Repository Information Overview
alwaysApply: true
---

# نظام إدارة العيادة السنية (Dental Clinic Management System)

## Summary
A desktop application for dental clinic management written in Python with a fully Arabic user interface and right-to-left (RTL) design. The system provides comprehensive features for managing patients, treatments, appointments, lab orders, expenses, and reports.

## Structure
- **main.py**: Main entry point for the application
- **ui/**: User interface modules including login, main window, and various tabs
  - **tabs/**: Application tabs (patients, treatment, appointments, lab, expenses, reports, settings)
- **database/**: Database handling modules for SQLite operations
- **assets/**: Application assets including icons, images, and CSS styles
- **reports/**: Report templates and generation modules
- **backup/**: Backup and restore functionality

## Language & Runtime
**Language**: Python
**Version**: 3.7 or newer (as specified in setup.py)
**Build System**: setuptools
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- PyQt5 (>=5.15.9): GUI framework
- QtPy (>=2.3.0): Qt abstraction layer
- python-dateutil (>=2.8.2): Date handling utilities
- pillow (>=10.0.0): Image processing
- matplotlib (>=3.7.2): Charting and visualization
- reportlab (>=4.0.4): PDF report generation
- qrcode (>=7.4.2): QR code generation

## Build & Installation
```bash
# Install required dependencies
pip install -r requirements.txt

# Run the application
python main.py

# Install as package (optional)
pip install -e .
```

## Database
**Engine**: SQLite
**Handler**: database/db_handler.py
**Features**: 
- Automatic initialization and repair
- Default admin account creation
- Backup and restore functionality

## Main Features
- **User Management**: Login system with role-based access control
- **Patient Management**: Patient records and medical history
- **Dental Treatments**: Multiple treatment types (general, bridges, veneers, whitening, pediatric, periodontal, orthodontic, implants, removable)
- **Appointments**: Scheduling and management with calendar views
- **Lab Orders**: Tracking lab work and orders
- **Expenses**: Clinic expense tracking and management
- **Reports**: Comprehensive reporting for patients, lab work, and clinic operations

## User Interface
**Framework**: PyQt5
**Direction**: RTL (right-to-left) for Arabic language support
**Style**: Supports light/dark themes via pyqtdarktheme (optional)
**Components**: Windows, tabs, forms, tables, charts
**CSS Styling**: Multiple style files in assets/ directory

## Authentication
**Default Login**:
- **Username**: admin
- **Password**: admin123
- **Role**: System Administrator (admin)

## Backup System
**Functionality**: Automatic and manual backup options
**Location**: backups/ directory
**Features**: Named restore points with timestamps
**Scripts**: backup_now.bat, create_restore_point.py